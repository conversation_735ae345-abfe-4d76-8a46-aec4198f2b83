{"cells": [{"metadata": {}, "cell_type": "code", "source": ["from feature_selection import compute_feature_ic\n", "from stratergy_runner import YearBacktraderListener\n", "from util import *\n", "from stratergy_runner import prepare_ic\n", "\n", "os.ch<PERSON>('900010')\n", "init_logger()\n", "init_qlib()"], "outputs": [], "execution_count": null}, {"cell_type": "code", "metadata": {"is_executing": true, "scrolled": true}, "source": ["def draw_predict_ic(alpha_type, test_start_date, end_date, model_name):\n", "    df = load_stock_data(alpha_type, config_info['market'])\n", "    factor_names = get_feature_names(alpha_type, config_info['market'], config_info['period_n'])\n", "    predictor = load_model(model_name)\n", "    test_data = prepare_data_for_model(df, factor_names, config_info['period_n'], test_start_date,\n", "                                       end_date)\n", "    predicted_data = predict_proba(predictor, test_data)\n", "    pred_data = prepare_bt_pred_data(predicted_data, df, 0)\n", "    pred_data.set_index(['date', 'sec_id'], inplace=True)\n", "    df_close = df[['close']]\n", "    pred_data = pred_data.join(df_close, how='left')\n", "    prices = pred_data['close'].unstack(level='sec_id')\n", "    pred_data['return_pct'] = pred_data['return'] * (1 + pred_data['return'].pct_change(3))\n", "    factor_data = alphalens.utils.get_clean_factor_and_forward_returns(\n", "        pred_data['return_pct'],\n", "        prices,\n", "        periods=(1, 5, 10),\n", "        quantiles=10,\n", "    )\n", "\n", "    # 4. 绘制完整的 tear sheet\n", "    alphalens.tears.create_full_tear_sheet(factor_data)"], "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["ic, factors = compute_feature_ic('alpha027', '20230101', '20231231')\n", "alphalens.tears.create_full_tear_sheet(factors)"], "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "draw_predict_ic('a458', '20200101', '20201231', 'auto_weight_467_sel_m_reg_a458_10_ends_2019_t600')", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["for year in range(2019, 2025):\n", "\n", "    df = feature_importance('a458', f'{year-1}0101', f'{year}1231', f'397_sel_m_reg_a458_10_ends_{year}_t450')\n", "    excluded = df[df['p95_high'] <= 0].sort_values('importance')\n", "    with open(f'feature_abandon_{year}', 'w') as f:\n", "        for index in excluded.index:\n", "            f.write(f\"{index}\\n\")"], "outputs": [], "execution_count": null}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 4}