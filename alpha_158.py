import datetime

from qlib.contrib.data.handler import Alpha158

import alphabase
from util import *

class CustomAlpha158(Alpha158):
    def get_feature_config(self):
        fields, names  = super().get_feature_config()
        if config_info['index_feature_count'] > 0:
            market = config_info['market']
            additional_features = [
                f'Mask($close/Ref($close,1)-1, "{market}")',
                f'Mask(Mean($close/Ref($close,1)-1,5), "{market}")',
                f'Mask(Std($close/Ref($close,1)-1,5), "{market}")',
                f'Mask(Mean($volume,5)/$volume, "{market}")',
                f'Mask(Std($volume,5)/$volume, "{market}")',
                f'Mask(Mean($close/Ref($close,1)-1,10), "{market}")',
                f'Mask(Std($close/Ref($close,1)-1,10), "{market}")',
                f'Mask(Mean($volume,10)/$volume, "{market}")',
                f'Mask(Std($volume,10)/$volume, "{market}")',
                f'Mask(Mean($close/Ref($close,1)-1,20), "{market}")',
                f'Mask(Std($close/Ref($close,1)-1,20), "{market}")',
                f'Mask(Mean($volume,20)/$volume, "{market}")',
                f'Mask(Std($volume,20)/$volume, "{market}")',
                f'Mask(Mean($close/Ref($close,1)-1,30), "{market}")',
                f'Mask(Std($close/Ref($close,1)-1,30), "{market}")',
                f'Mask(Mean($volume,30)/$volume, "{market}")',
                f'Mask(Std($volume,30)/$volume, "{market}")',
                f'Mask(Mean($close/Ref($close,1)-1,60), "{market}")',
                f'Mask(Std($close/Ref($close,1)-1,60), "{market}")',
                f'Mask(Mean($volume,60)/$volume, "{market}")',
                f'Mask(Std($volume,60)/$volume, "{market}")'
            ]
            for i, feature in enumerate(additional_features, 1):
                names.append(f'index_feature{i}')
            fields.extend(additional_features)
        return fields,names

def prepare_alpha158_data(start, end):
    start_time = start.strftime("%Y-%m-%d")
    end_time = end.strftime("%Y-%m-%d")
    generate_ins_date = config_info['generate_ins_date']
    config_info['generate_ins_date'] = None
    df_all, _ = alphabase.Alphas.get_stocks_data_by_date(end_time, start_time)
    config_info['generate_ins_date'] = generate_ins_date
    config = {
        "start_time": start_time,
        "end_time": end_time,
        "fit_start_time": start_time,
        "fit_end_time": end_time,
        "instruments": configs.convert_to_csi_code(config_info['market']),
    }
    alpha158 = CustomAlpha158(**config)
    df = alpha158.fetch()
    df['date'] = pd.to_datetime(df.index.get_level_values(0))
    df['sec_id'] = df.index.get_level_values(1)
    df['sec_id'] = df['sec_id'].apply(lambda x: x.lstrip('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'))
    df.set_index(['date', 'sec_id'], inplace=True, drop=True)
    df.drop(columns=['LABEL0'], inplace=True)
    df_all.set_index(['date', 'sec_id'], inplace=True)
    df_all.rename(columns={'change': 'return'}, inplace=True)
    df = df.merge(df_all, left_index=True, right_index=True, how='inner')
    df.to_pickle(f'a158_{config_info["market"]}.pkl')
    refine_stock_data('a158')


def compute_158_ic_table(end=None):
    stock_data = pd.read_pickle(f'refined_a158_{config_info["market"]}.pkl')
    if end is None:
        end = datetime.datetime.now()
    elif isinstance(end, str):
        end = pd.to_datetime(end, format='%Y%m%d')
    start = pd.to_datetime(end - datetime.timedelta(days=2 * 365 + 400))
    stock_data = stock_data[
        (stock_data.index.get_level_values(0) >= start) & (stock_data.index.get_level_values(0) <= end)]
    returns = stock_data.loc[:, ['close']].copy()
    returns = returns.pivot_table(index='date', columns='sec_id', values='close')
    returns.rename(columns={'close': 'returns'}, inplace=True)
    ret = pd.DataFrame()
    for col in stock_data.columns:
        if col in ['return', 'open', 'high', 'low', 'close', 'volume', 'vwap']:
            continue
        try:
            factors = compute_alphalen_factor(returns, col, stock_data)
            ic = perf.mean_information_coefficient(factors)
            ic = abs(ic)
            ret = pd.concat([ret, pd.DataFrame(ic.T, columns=[col])], axis=1)
        except:
            ret = pd.concat([ret, pd.DataFrame([[0], [0], [0]], columns=[col], index=['1D', '5D', '10D'])], axis=1)
    ret = ret.T
    ret.drop_duplicates(subset=['10D'])
    ret.to_csv(f'a158_{config_info["market"]}_ic.csv')


def combined_with_a300():
    a158 = pd.read_pickle(f'refined_a158_{config_info["market"]}.pkl')
    a300 = pd.read_pickle(f'refined_a300_{config_info["market"]}.pkl')
    df = pd.merge(a158, a300, left_index=True, right_index=True, how='inner')
    duplicate_cols = []
    for col in df.columns:
        if '_x' in col:
            duplicate_cols.append(col[:-2])
    for col in duplicate_cols:
        df = df.drop(columns=[col + '_y'])
        df = df.rename(columns={col + '_x': col})

    columns = get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'],
                                config_info['feature_count'])

    if config_info['pct_feature']:
        for col in columns:
            if col in df.columns:
                df[col] = df.groupby(level=0)[col].rank(pct=True)
    df.to_pickle(f'refined_a458_{config_info["market"]}.pkl')


def alpha_158_main():
    init_qlib()
    prepare_alpha158_data(datetime.date(2008, 1, 1), datetime.date.today())
    refine_stock_data('a300', 'daily_')
    combined_with_a300()


if __name__ == '__main__':
    alpha_158_main()
