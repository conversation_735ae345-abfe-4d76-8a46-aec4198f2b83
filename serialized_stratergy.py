import sys
import math
import re

import pandas as pd
from sympy.physics.units import years

from combined_stratergy import CombinedTopNStratergy
from train_evaluator import train, leaderboard_model
from util import *

class SerializedTopNStratergy(TopNStratergy):
    params = (
        ('alpha_type', 'a101'),
        ('init_model_name', '101_10_300_model'),
        ('last_train_ends_time', None),
        ('interval', 60),
    )

    def __init__(self):
        super().__init__()
        self._factor_names = None
        self.alpha_type = self.p.alpha_type
        self.last_train_ends_time = None if self.p.last_train_ends_time is None else pd.to_datetime(
            self.p.last_train_ends_time)
        self._df = None
        self._evaluate_score_val = None
        self._models_count = 0

    def prepare_model(self, dt):
        end_time = self.last_train_ends_time - pd.DateOffset(days=config_info['train_delay'])
        start_time = end_time - pd.DateOffset(years=self.train_year)
        start_time = start_time.replace(month=1, day=1)
        model_name = self.p.init_model_name + "_" + end_time.strftime("%Y%m%d")
        logging.getLogger().info('Training model on %s, from %s to %s, load model name : %s', dt, start_time,
                                 end_time, model_name)
        ret = load_model(model_name)
        if ret is None:
            os.system(
                f'python ../stratergy_runner.py --market {config_info["market"]} --period_n {config_info["period_n"]} --train {end_time.strftime("%Y%m%d")} -c {config_info["feature_count"]} --model {model_name} --quality {config_info["presets"]} --train_year {config_info["train_year"]}')
            ret = load_model(model_name)

        evaluate_score_val = ret.leaderboard()

        evaluate_score_val = evaluate_score_val[evaluate_score_val['score_val'].notna()]['score_val'].max()
        if not math.isnan(evaluate_score_val):
            if self._evaluate_score_val is None:
                self._evaluate_score_val = evaluate_score_val
            else:
                self._evaluate_score_val = self._evaluate_score_val + evaluate_score_val
        self._models_count += 1
        return ret

    def update_pred_dat(self, dt, predictor):
        logging.getLogger().info('update predict data from %s to %s', dt, dt + pd.DateOffset(days=self.p.interval))
        test_data = prepare_data_for_model(self._df, self._factor_names, self.period, dt,
                                           dt + pd.DateOffset(days=self.p.interval))
        predicted_data = predict_proba(predictor, test_data)
        self.pred_data = prepare_bt_pred_data(predicted_data, self._df, 0)
        self.pred_data['date'] = pd.to_datetime(self.pred_data['date'])
        self.farrest_day = self.pred_data['date'].min()

        if self.wencai_data is not None:
            wencai_columns = '拟合评分'
            updated = pd.merge(self.pred_data, self.wencai_data[wencai_columns], left_on=['date', 'sec_id'],
                               right_index=True)
            if not updated.empty:
                self.pred_data = updated
                self.pred_data = self.pred_data[self.pred_data['rank'] <= self.topn * 2.5]
                self.pred_data['rank'] = self.pred_data.groupby('date')[wencai_columns].rank(ascending=True)

    def update_model(self, dt):
        if self._df is None:
            alpha_type = self.alpha_type
            self._df = load_stock_data(alpha_type, config_info['market'])
            self._factor_names = get_feature_names(alpha_type, config_info['market'], self.period)
            logging.getLogger().info(f'{len(self.datas)} stocks in the pool')

        self.last_train_ends_time = dt - pd.DateOffset(days=1)
        predictor = self.prepare_model(dt)
        self.update_pred_dat(dt, predictor)

    def next(self):
        dt = pd.to_datetime(self.datas[0].datetime.date(0))
        if self._df is None or dt >= self.last_train_ends_time + pd.DateOffset(days=self.p.interval):
            self.update_model(dt)
        super().next()

    def dump_evaluation(self):
        if self._evaluate_results is None:
            return
        evaluate_results = {key: value / self._models_count if not isinstance(value, str) else value for key, value in
                            self._evaluate_results.items()}
        logging.getLogger().info('evaluate reults:')
        logging.getLogger().info(evaluate_results)
        if self._evaluate_score_val is not None:
            evaluate_score_val = self._evaluate_score_val / self._models_count
            logging.getLogger().info(f'evaluate: {evaluate_score_val}')


class OptimizedSerializedTopNStratergy(SerializedTopNStratergy):
    params = (
        ('lookback_days', 20),
        ('eval_period', 5),
    )

    def __init__(self):
        super().__init__()
        self.lookback_days = self.p.lookback_days
        self.eval_period = self.p.eval_period
        self.cur_model = None

    def update_model(self, dt):
        if self._df is None:
            alpha_type = self.alpha_type
            self._df = load_stock_data(alpha_type, config_info['market'])
            self._factor_names = get_feature_names(alpha_type, config_info['market'], self.period)
            logging.getLogger().info(f'{len(self.datas)} stocks in the pool')

        self.last_train_ends_time = dt - pd.DateOffset(days=1)

        cur_model = self.prepare_model(dt)
        if self.cur_model is None:
            self.cur_model = cur_model
        one_year_ago = dt - pd.DateOffset(years=1)
        models_to_evaluate = self.get_models_in_timeframe(one_year_ago, dt - pd.DateOffset(days=self.lookback_days))
        if len(models_to_evaluate) > 0:
            eval_end = dt - pd.DateOffset(days=1)
            eval_start = eval_end - pd.DateOffset(days=self.lookback_days)

            best_model = self.evaluate_models(models_to_evaluate, eval_start, eval_end)
            if best_model is not None:
                self.cur_model = best_model
        self.update_pred_dat(dt, self.cur_model)

    def get_models_in_timeframe(self, start_date, end_date):
        model_paths = []
        model_base = self.p.init_model_name
        date_pattern = r'ends_(\d{8})'
        match = re.search(date_pattern, model_base)
        date_str = match.group(1)
        current_date = pd.to_datetime(date_str)
        one_year_ago = current_date - pd.DateOffset(years=1)
        previous_date = one_year_ago.strftime("%Y%m%d")

        previous_model_base = model_base.replace(f"ends_{date_str}", f"ends_{previous_date}")
        model_dir = 'models'

        if not os.path.exists(model_dir):
            logging.getLogger().warning(f"模型目录 {model_dir} 不存在")
            return model_paths

        for model_name in os.listdir(model_dir):
            matches_current = model_name.startswith(model_base)
            matches_previous_year = model_name.startswith(previous_model_base)
            model_name.rindex('_')
            if matches_current or matches_previous_year:
                try:
                    date_str = model_name[model_name.rindex('_') + 1:]
                    model_date = pd.to_datetime(date_str)
                    if start_date <= model_date <= end_date:
                        model_path = model_name.split('.')[0]  # 移除文件扩展名
                        model_paths.append(model_path)
                except Exception as e:
                    logging.getLogger().warning(f"无法解析模型名称: {model_name}, 错误: {e}")
        return model_paths

    def evaluate_models(self, model_paths, eval_start, eval_end):
        best_score = -float('inf')
        best_model = None
        best_path = None

        for model_path in model_paths:
            try:
                model = load_model(model_path)
                if model is None:
                    logging.getLogger().warning(f"无法加载模型: {model_path}, 跳过")
                    continue
                
                test_data = prepare_data_for_model(self._df, self._factor_names, self.period,
                                               eval_start, eval_end)
                predicted_data = predict_proba(model, test_data)
                predicted_data = prepare_bt_pred_data(predicted_data, self._df, 0)

                score = self.calculate_model_score(predicted_data, eval_start, eval_end)
                
                if score > best_score:
                    best_score = score
                    best_model = model
                    best_path = model_path
            except Exception as e:
                logging.getLogger().warning(f"评估模型 {model_path} 时出错: {e}")
                
        if best_model is not None:
            logging.getLogger().info(f"选择最佳模型: {best_path}, 得分: {best_score}")

        return best_model

    def calculate_model_score(self, predicted_data, start_date, end_date):
        predicted_data['date'] = pd.to_datetime(predicted_data['date'])
        pred_data_filtered = predicted_data[(predicted_data['date'] >= start_date) &
                                            (predicted_data['date'] <= end_date)]

        topn_stocks = pred_data_filtered[pred_data_filtered['rank'] <= self.topn]

        if topn_stocks.empty:
            return -float('inf')

        try:
            topn_stocks.set_index(['date', 'sec_id'], inplace=True)

            topn_stocks = pd.merge(topn_stocks, self._df['close'], left_index=True, right_index=True)
            topn_stocks = pd.merge(topn_stocks, self._df['open'], left_index=True, right_index=True)

            topn_stocks.reset_index(inplace=True)
            topn_stocks = topn_stocks.sort_values(by=['sec_id', 'date'])

            holding_days = self.eval_period
            topn_stocks['future_return'] = topn_stocks.groupby('sec_id')['close'].pct_change(holding_days)
            topn_stocks['future_return'] = topn_stocks.groupby('sec_id')['future_return'].shift(-holding_days)

            topn_stocks = topn_stocks.dropna(subset=['future_return'])

            if topn_stocks.empty:
                return -float('inf')

            returns_series = topn_stocks.groupby('date')['future_return'].mean()

            if len(returns_series) < 2:
                return returns_series.mean() if not returns_series.empty else -float('inf')

            mean_return = returns_series.mean()
            sharpe_ratio = mean_return / (returns_series.std() + 1e-10)

            return mean_return * (1 + sharpe_ratio)

        except Exception as e:
            logging.getLogger().error(f"评估模型时出错: {e}")
            return -float('inf')


class SerializedCombinedStratergy(SerializedTopNStratergy):
    params = (
        ('assist_period', 5),
        ('assist_model_name', None),
        ('assist_feature_count', 210),
    )

    def __init__(self):
        super().__init__()
        self.assist_period = self.p.assist_period
        self.assist_model_name = self.p.assist_model_name
        self.assist_feature_count = self.p.assist_feature_count
        self._pred_data_assist = None
        self._assist_feature_name = None
        self._pred_data_combined = None

    def prepare_model(self, dt):
        end_time = self.last_train_ends_time - pd.DateOffset(days=config_info['train_delay'])
        start_time = end_time - pd.DateOffset(years=self.train_year)
        start_time = start_time.replace(month=1, day=1)
        logging.getLogger().info('Training model on %s, from %s to %s, load model name : %s', dt, start_time,
                                 end_time, (self.p.init_model_name + "_" + end_time.strftime("%Y%m%d")))

        model_name = self.p.init_model_name + "_" + end_time.strftime("%Y%m%d")
        ret = load_model(model_name)
        if ret is None:
            ret = train_model(self._df, self._factor_names, self.period, start_time,
                              end_time, model_name)

        if self._assist_feature_name is None:
            self._assist_feature_name = get_feature_names(config_info['assist_alpha_type'], config_info['market'],
                                                          self.assist_period,
                                                          self.assist_feature_count)
        train_type = config_info['train_type']
        config_info['train_type'] = config_info['assist_train_type']
        model_name = self.p.assist_model_name + "_" + end_time.strftime("%Y%m%d")

        predictor = load_model(model_name)
        if predictor is None:
            predictor = train_model(self._df, self._assist_feature_name, self.assist_period, start_time,
                                    end_time, model_name)

        if config_info['assist_train_type'] == 'cls':
            backtrader_pred = leaderboard_model(config_info['assist_alpha_type'], self.assist_period, dt,
                                                dt + pd.DateOffset(days=self.p.interval),
                                                model_name)
            print(backtrader_pred[backtrader_pred['predict_probe'] > 0]['return_10D'].describe())
            print(backtrader_pred['return_10D'].describe())

        test_data = prepare_data_for_model(self._df, self._assist_feature_name, self.assist_period, dt,
                                           dt + pd.DateOffset(days=self.p.interval))
        predicted_data = predict_proba(predictor, test_data)

        self._pred_data_assist = prepare_bt_pred_data(predicted_data, self._df, 0)
        config_info['train_type'] = train_type
        return ret

    def prepare_cur_step_predicted_data(self, predict_date):
        self.cur_step_pred_data = self._pred_data_combined.loc[
            (self._pred_data_combined['date'] == predict_date) & (self._pred_data_combined['rank'] <= self.topn)]

    def get_predict_data(self, predict_date):
        return self._pred_data_combined.loc[(self.pred_data['date'] == predict_date), :]

    def update_model(self, dt):
        super().update_model(dt)
        if config_info['assist_train_type'] == 'cls':
            self._pred_data_combined = self.pred_data[self._pred_data_assist['return'] > 0]
        else:
            self._pred_data_combined = self.pred_data.copy()
            self._pred_data_combined['return'] = (
                    self.pred_data['return'] * 0.6 + self._pred_data_assist['return'] * 0.4)
            self._pred_data_combined['rank'] = self._pred_data_combined.groupby('date')['return'].rank(ascending=False)


def get_model_path(prefix, train_end_date, interval, feature_count, is_assist=False):
    period = config_info['period_n'] if not is_assist else config_info['assist_period_n']
    model_path = f'{prefix}{feature_count}_sel_{config_info["presets"][0]}_{config_info["train_type"]}_{config_info["alpha_type"]}_{period}_ends_{train_end_date.strftime("%Y%m%d")}_t{config_info["tuning_days"]}'
    # if config_info['market'] != '000300':
    #     model_path += f'_{configs.convert_to_csi_code(config_info["market"])}'
    if config_info['cpu']:
        model_path += '_cpu'
    return model_path


def test_serialized_stratergy_s(start_date, end_date, interval=60, prefix=''):
    train_end_date = pd.to_datetime(start_date) + pd.DateOffset(days=-1)
    model_path = get_model_path(prefix, train_end_date, interval, config_info['feature_count'])
    cerebro, stats = test_serialized_stratergy(config_info['alpha_type'], start_date, end_date, model_path, interval)
    return cerebro.broker.getvalue() / 10000000, stats, cerebro


def test_serialized_combined_stratergy_s(start_date, end_date, interval=60, prefix='', assist_prefix=''):
    train_end_date = pd.to_datetime(start_date) + pd.DateOffset(days=-1)
    model_path = get_model_path(prefix, train_end_date, interval, config_info['feature_count'])
    assist_model_path = get_model_path(assist_prefix, train_end_date, interval, config_info['assist_feature_count'],
                                       True)
    cerebro, stats = test_serialized_combined_stratergy(config_info['alpha_type'], start_date, end_date, model_path,
                                                        assist_model_path, interval)
    return cerebro.broker.getvalue() / 10000000, stats, cerebro


def add_benchmark_data(start, end, cerebro):
    benchmark_file = f"{config_info['market']}.csv"
    benchmark_df = pd.read_csv(benchmark_file, index_col=0, parse_dates=True)
    benchmark_df = benchmark_df.loc[(benchmark_df.index >= start) & (benchmark_df.index <= end), :]
    benchmark_df.rename(columns={'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'volume': 'Volume'},
                        inplace=True)
    benchmark_data = bt.feeds.PandasData(dataname=benchmark_df, plot=True, name='benchmark')
    cerebro.adddata(benchmark_data)
    cerebro.addobserver(bt.observers.Benchmark, data=benchmark_data, timeframe=bt.TimeFrame.Days)


def get_stratergy_class():
    class_name = config_info['serial_stratergy']
    current_module = sys.modules[__name__]
    try:
        return getattr(current_module, class_name)
    except (AttributeError, TypeError):
        logging.getLogger().warning(f"找不到策略类 '{class_name}'，使用默认策略类 SerializedTopNStratergy")
        return SerializedTopNStratergy

def test_serialized_stratergy(alpha_type, test_start_date, end_date, model_path, interval=60):
    df = load_stock_data(alpha_type, config_info['market'])
    cerebro = prepare_data_for_cerebro(df, end_date, test_start_date)
    if os.path.exists(f'trade_{config_info["feature_count"]}.csv'):
        trade_data = [pd.read_csv(f'trade_{config_info["feature_count"]}.csv', index_col=0, parse_dates=True)]
    else:
        trade_data = [pd.DataFrame(columns=['sec_id', 'date'])]

    cerebro.addstrategy(get_stratergy_class(), trade_data=trade_data,
                        topn=config_info['topn'],
                        max_down=config_info['max_down'],
                        min_hold=1000,
                        printlog=config_info['trade_log'],
                        period=config_info['period_n'],
                        alpha_type=alpha_type,
                        init_model_name=model_path,
                        interval=interval,
                        train_year=config_info['train_year'],
                        last_train_ends_time=pd.to_datetime(test_start_date) - pd.DateOffset(days=1))
    prepare_cerebro(cerebro)
    add_benchmark_data(test_start_date, end_date, cerebro)
    stats = cerebro.run(optreturn=True)
    if config_info['trade_log'] and not config_info['use_rank_model']:
        trade_data[0].to_csv(f'trade_{config_info["feature_count"]}.csv')
    loginfo = f"{config_info['market']}: normal for fc:{config_info['feature_count']} range:{test_start_date}_{end_date} interval:{interval} period:{config_info['period_n']}"
    dump_stats_cerebro(cerebro, stats, loginfo)
    return cerebro, stats


def test_serialized_combined_stratergy(alpha_type, test_start_date, end_date, model_path, assist_model_path,
                                       interval=60):
    df = load_stock_data(alpha_type, config_info['market'])
    cerebro = prepare_data_for_cerebro(df, end_date, test_start_date)
    trade_data = [pd.DataFrame(columns=['sec_id', 'date'])]
    cerebro.addstrategy(SerializedCombinedStratergy, trade_data=trade_data,
                        topn=config_info['topn'],
                        max_down=config_info['max_down'],
                        min_hold=1000,
                        printlog=config_info['trade_log'],
                        interval=interval,
                        period=config_info['period_n'],
                        alpha_type=alpha_type,
                        init_model_name=model_path,
                        assist_period=config_info['assist_period_n'],
                        assist_feature_count=config_info['assist_feature_count'],
                        assist_model_name=assist_model_path,
                        train_year=config_info['train_year'],
                        last_train_ends_time=pd.to_datetime(test_start_date) - pd.DateOffset(days=1))
    prepare_cerebro(cerebro)
    add_benchmark_data(test_start_date, end_date, cerebro)
    stats = cerebro.run(optreturn=True)
    loginfo = f"combine for fc:{config_info['feature_count']}_{config_info['assist_feature_count']} range:{test_start_date}_{end_date} interval:{interval} period:{config_info['period_n']}"
    dump_stats_cerebro(cerebro, stats, loginfo)
    return cerebro, stats


def test_model_stratergy(alpha_type, test_start_date, end_date, model_name, sub_model_name=None, tuning_sec_only=False):
    df = load_stock_data(alpha_type, config_info['market'])
    factor_names = get_feature_names(alpha_type, config_info['market'], config_info['period_n'], MAX_FEATURE_COUNT)
    if config_info['feature_for_model'] is None:
        predictor = load_model(model_name)
        if predictor is not None and predictor.is_fit:
            if sub_model_name is None:
                if config_info['sub_model_name'] is not None:
                    sub_model_name = config_info['sub_model_name']
                    if sub_model_name not in predictor.model_names():
                        l1_models = [m for m in predictor.model_names() if 'WeightedEnsemble' not in m]
                        predictor.fit_weighted_ensemble(
                            base_models=l1_models, name_suffix=sub_model_name[len('WeightedEnsemble_L2'):])

                elif config_info['enable_second_ensemble'] and config_info['num_stack_levels'] == 0:
                    sub_model_name = 'WeightedEnsemble_L2Best'
                    all_models = predictor.model_names()
                    if sub_model_name not in all_models:
                        l1_models = [m for m in all_models if 'WeightedEnsemble' not in m]
                        predictor.fit_weighted_ensemble(
                            base_models=list(l1_models))
                else:
                    sub_model_name = predictor.model_best

            logging.info('sub model: ' + sub_model_name)
            best_model = predictor._trainer.load_model(sub_model_name)
            if sub_model_name.startswith('WeightedEnsemble'):
                logging.info(best_model._get_model_weights())
        test_data = prepare_data_for_model(df, factor_names, config_info['period_n'], test_start_date,
                                           end_date)
        predicted_data = predict_proba(predictor, test_data, sub_model_name)
        pred_data = prepare_bt_pred_data(predicted_data, df, 0)
    else:
        positive = True
        feature_for_model = config_info['feature_for_model']
        if feature_for_model.startswith('-'):
            positive = False
            feature_for_model = feature_for_model[1:]
        pred_data = df.loc[
            (df.index.get_level_values(0) >= test_start_date) &
            (df.index.get_level_values(0) <= end_date),
            [feature_for_model]
        ].copy()
        pred_data.rename(columns={feature_for_model: 'return'}, inplace=True)
        pred_data['rank'] = pred_data.groupby('date')['return'].rank(ascending=not positive)
        if positive:
            pred_data['return'] = (pred_data['return'] - pred_data['return'].min()) / (
                    pred_data['return'].max() - pred_data['return'].min())
        else:
            pred_data['return'] = (pred_data['return'].max() - pred_data['return']) / (
                    pred_data['return'].max() - pred_data['return'].min())
        pred_data['sec_id'] = pred_data.index.get_level_values(1)
        pred_data['date'] = pred_data.index.get_level_values(0)
        pred_data.reset_index(drop=True, inplace=True)
        pred_data.sort_values(['date', 'rank'], inplace=True)

    cerebro = prepare_data_for_cerebro(df, end_date, test_start_date, tuning_sec_only)

    if os.path.exists(f'trade_{config_info["feature_count"]}.csv'):
        trade_data = [pd.read_csv(f'trade_{config_info["feature_count"]}.csv', index_col=0, parse_dates=True)]
    else:
        trade_data = [pd.DataFrame(columns=['sec_id', 'date'])]

    # 训练rank_model
    rank_model = None
    if config_info.get('use_rank_model', False):
        rank_model_path = f'rank_{model_name}'
        train_start_date = pd.to_datetime(test_start_date) - pd.DateOffset(years=10)
        train_end_date = pd.to_datetime(test_start_date) - pd.DateOffset(days=1)
        rank_model = train_rank_model(
            f'trade_{config_info["feature_count"]}.csv',
            df,
            train_start_date,
            train_end_date,
            rank_model_path
        )

    cerebro.addstrategy(TrendPositionTopNStrategy, trade_data=trade_data,
                        topn=config_info['topn'],
                        max_down=config_info['max_down'],
                        min_hold=1000,
                        printlog=config_info['trade_log'],
                        pred_data=pred_data,
                        train_year=config_info['train_year'],
                        period=config_info['period_n'],
                        rank_model=rank_model)
    prepare_cerebro(cerebro)
    stats = cerebro.run(optreturn=True)
    if config_info['trade_log'] and not config_info['use_rank_model']:
        trade_data[0].to_csv(f'trade_{config_info["feature_count"]}.csv')
    loginfo = f'{config_info["market"]}: ' + model_name + ' from ' + test_start_date + ' to ' + end_date
    dump_stats_cerebro(cerebro, stats, loginfo)
    return cerebro.broker.getvalue() / 10000000, stats, cerebro


def test_add_model_assisted_stratergy(alpha_type, test_start_date, end_date, model_name):
    df = load_stock_data(alpha_type, config_info['market'])

    predictor = load_model(model_name)
    factor_names = get_feature_names(alpha_type, config_info['market'], config_info['period_n'])
    test_data = prepare_data_for_model(df, factor_names, config_info['period_n'], test_start_date,
                                       end_date)
    predicted_data = predict_proba(predictor, test_data)
    pred_data = prepare_bt_pred_data(predicted_data, df, 0)

    assist_pred_data = pd.read_csv(f'{config_info["market"]}_assist_pred_data.csv', parse_dates=['date'],
                                   dtype={'sec_id': str})

    cerebro = prepare_data_for_cerebro(df, end_date, test_start_date)

    if os.path.exists(f'trade_{config_info["feature_count"]}.csv'):
        trade_data = [pd.read_csv(f'trade_{config_info["feature_count"]}.csv', index_col=0, parse_dates=True)]
    else:
        trade_data = [pd.DataFrame(columns=['sec_id', 'date'])]
    cerebro.addstrategy(CombinedTopNStratergy, trade_data=trade_data,
                        topn=config_info['topn'],
                        max_down=config_info['max_down'],
                        min_hold=1000,
                        printlog=config_info['trade_log'],
                        pred_data=pred_data,
                        pred_data_assist=assist_pred_data,
                        train_year=config_info['train_year'],
                        period=config_info['period_n'])
    prepare_cerebro(cerebro)
    stats = cerebro.run(optreturn=True)
    if config_info['trade_log'] and not config_info['use_rank_model']:
        trade_data[0].to_csv(f'trade_{config_info["feature_count"]}.csv')
    loginfo = 'test serialized stratergy with model: ' + model_name + ' from ' + test_start_date + ' to ' + end_date
    dump_stats_cerebro(cerebro, stats, loginfo)
    return cerebro.broker.getvalue() / 10000000, stats, cerebro


def test_combined_model_stratergy(alpha_type, test_start_date, end_date, model_name, assist_model_name):
    df = load_stock_data(alpha_type, config_info['market'])

    predictor = load_model(model_name)
    factor_names = get_feature_names(alpha_type, config_info['market'], config_info['period_n'])
    test_data = prepare_data_for_model(df, factor_names, config_info['period_n'], test_start_date,
                                       end_date)
    predicted_data = predict_proba(predictor, test_data)
    pred_data = prepare_bt_pred_data(predicted_data, df, 0)

    assist_predictor = load_model(assist_model_name)
    factor_names = get_feature_names(alpha_type, config_info['market'], config_info['assist_period_n'],
                                     config_info['assist_feature_count'])
    test_data = prepare_data_for_model(df, factor_names, config_info['period_n'], test_start_date,
                                       end_date)

    assist_predicted_data = predict_proba(assist_predictor, test_data)
    assist_pred_data = prepare_bt_pred_data(assist_predicted_data, df, 0)

    cerebro = prepare_data_for_cerebro(df, end_date, test_start_date)

    if os.path.exists(f'trade_{config_info["feature_count"]}.csv'):
        trade_data = [pd.read_csv(f'trade_{config_info["feature_count"]}.csv', index_col=0, parse_dates=True)]
    else:
        trade_data = [pd.DataFrame(columns=['sec_id', 'date'])]
    cerebro.addstrategy(CombinedTopNStratergy, trade_data=trade_data,
                        topn=config_info['topn'],
                        max_down=config_info['max_down'],
                        min_hold=1000,
                        printlog=config_info['trade_log'],
                        pred_data=pred_data,
                        pred_data_assist=assist_pred_data,
                        train_year=config_info['train_year'],
                        period=config_info['period_n'])
    prepare_cerebro(cerebro)
    stats = cerebro.run(optreturn=True)
    if config_info['trade_log'] and not config_info['use_rank_model']:
        trade_data[0].to_csv(f'trade_{config_info["feature_count"]}.csv')
    loginfo = 'test serialized stratergy with model: ' + model_name + ' from ' + test_start_date + ' to ' + end_date
    dump_stats_cerebro(cerebro, stats, loginfo)
    return cerebro.broker.getvalue() / 10000000, stats, cerebro
