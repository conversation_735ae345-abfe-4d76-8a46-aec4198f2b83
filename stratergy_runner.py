import shutil
from itertools import combinations
import argparse
import ast
import os.path
import pickle
import re
import sys

import numpy as np
import pandas as pd
import torch

import util
from alpha_158 import prepare_alpha158_data, combined_with_a300
from data_preparer import prepare_data_for_all_year
from news_scrawler import select_stock
from serialized_stratergy import (
    test_serialized_stratergy_s,
    test_serialized_combined_stratergy_s,
    test_model_stratergy,
    test_combined_model_stratergy,
    test_add_model_assisted_stratergy
)
from stock_recommander import (
    get_return_infos_of_range,
    recommend_for_today,
    get_relative_returns,
    research_alpha
)
from tools.index_assessment import calculate_annual_equal_weight_return
from train_evaluator import *
from util import *


def is_assist_model_needed():
    return (config_info['assist_feature_count'] != config_info['feature_count']) or \
        (config_info['period_n'] != config_info['assist_period_n'])


def prepare_ic(year):
    config_info['ic_year'] = year


def get_model_name(year, prefix=''):
    """根据传入的year和prefix生成model_name。"""
    ret = (
        f'{prefix}{config_info["feature_count"]}_sel_{config_info["presets"][0]}_'
        f'{config_info["train_type"]}_{config_info["alpha_type"]}_{config_info["period_n"]}_ends_{year - 1}_t{config_info["tuning_days"]}'
    )
    return ret if config_info['remove_from_fid'] == 0 else f'{ret}_r{config_info["remove_from_fid"]}'


def get_assist_model_name(year, prefix=''):
    """根据传入的year和prefix生成assist_model_name。"""
    ret = (
        f'{prefix}{config_info["assist_feature_count"]}_sel_{config_info["presets"][0]}_'
        f'{config_info["train_type"]}_{config_info["alpha_type"]}_{config_info["assist_period_n"]}_ends_{year - 1}_t{config_info["tuning_days"]}'
    )
    return ret if config_info['remove_from_fid'] == 0 else f'{ret}_r{config_info["remove_from_fid"]}'


def run_all_sub_model(start_date, end_date, model_name):
    predictor = load_model(model_name)
    sub_names = predictor.model_names()
    rets = {}
    cur_best_return = -1
    for sub_name in sub_names:
        ret,_,_ = test_model_stratergy(config_info['alpha_type'], start_date, end_date, model_name, sub_name)
        rets[sub_name] = ret
        if ret > cur_best_return:
            cur_best = sub_name
            cur_best_return = ret
            logging.info(f'{cur_best} -> {cur_best_return}')
    rets_df = pd.DataFrame(rets, index=[0])
    rets_df.to_csv(f'sub_model_rets_{model_name}.csv', index=False)


def run_serialized_stratergy(start_date, end_date, interval=60, prefix=''):
    """根据当前的config_info，运行序列化策略并返回收益和统计信息。"""
    if config_info['fixed_model'] is not None:
        model_name = config_info['fixed_model']
        if config_info['add_info']:
            return test_add_model_assisted_stratergy(config_info['alpha_type'], start_date, end_date, model_name)
        elif is_assist_model_needed():
            return test_combined_model_stratergy(config_info['alpha_type'], start_date, end_date, model_name,
                                                 config_info['assist_model_name'])
        else:
            return test_model_stratergy(config_info['alpha_type'], start_date, end_date, model_name)
    elif (config_info['assist_feature_count'] == config_info['feature_count']) and \
            (config_info['assist_train_type'] == config_info['train_type']):
        return test_serialized_stratergy_s(start_date, end_date, interval, prefix)
    else:
        return test_serialized_combined_stratergy_s(start_date, end_date, interval, prefix, prefix)


def run_and_analyze_strategy(start_date, end_date, interval, prefix=''):
    """运行策略并获取收益及drawdown分析信息"""
    ret, stats, _ = run_serialized_stratergy(start_date, end_date, interval, prefix)
    analysis = stats[0].analyzers.drawdown.get_analysis()
    drawdown_len = analysis['len']
    drawdown_max = analysis['max']['drawdown']
    sharpe_ratio = stats[0].analyzers.sharperatio.get_analysis()['sharperatio']
    sortino_ratio = stats[0].analyzers.annualizedsortinoratio.get_analysis()['sortino']
    return ret, drawdown_len, drawdown_max, sharpe_ratio, sortino_ratio


def prepare_and_train_model(year, prefix=''):
    """在回测前准备IC并训练模型（如果需要）"""
    prepare_ic(year - 1)
    config_info['model_name'] = config_info['fixed_model'] = get_model_name(year, prefix)
    if util.load_model(config_info['model_name']) is None:
        train(config_info['alpha_type'], config_info['period_n'], f'{year - 1}1231', config_info['model_name'])

    if is_assist_model_needed():
        config_info['assist_model_name'] = config_info['assist_fixed_model'] = get_assist_model_name(year, prefix)
        if util.load_model(config_info['assist_model_name']) is None:
            train(config_info['alpha_type'], config_info['assist_period_n'], f'{year - 1}1231',
                  config_info['assist_model_name'])


def update_returns_csv(df, ret_df, file_name='returns_fc.csv'):
    """将新的收益结果合并到csv文件中并保存"""
    result_df = pd.concat([df, ret_df], ignore_index=True)
    result_df.to_csv(file_name)


def notify_results(title, attachment=None):
    """使用邮件通知结果"""
    logging.getLogger().info(title)
    notify_by_email(title)
    if attachment is not None:
        notify_by_email_with_attachment(title, attachment)


class YearBacktraderListener:
    def __init__(self, prefix='', interval=250):
        self.prefix = prefix
        self.year_interval = int(interval / 250)
        self.cur_interval = 0
        self.enabled = config_info['fixed_model'] is None

    def on_pre_year_backtrader(self, year):
        if not self.enabled:
            return
        if self.year_interval < 1:
            prepare_ic(year - 1)
            return

        if self.cur_interval == 0:
            # 第一次进入这个区间时训练模型
            prepare_and_train_model(year, self.prefix)
        self.cur_interval += 1
        if self.cur_interval >= self.year_interval:
            self.cur_interval = 0


def run_current_year_backtrade(prefix=''):
    """运行当前年份的回测，按整年进行。"""
    interval = config_info['interval']
    now = get_last_time()
    listener = YearBacktraderListener(prefix, interval)
    listener.on_pre_year_backtrader(now.year)

    start_date = f'{now.year}0101'
    end_date = now.strftime('%Y%m%d')

    ret_cur_year, drawdown_len, drawdown_max, sharpe_ratio, sortino_ratio = run_and_analyze_strategy(start_date,
                                                                                                     end_date, interval,
                                                                                                     prefix)

    title = (f"{now.year} fc:{config_info['feature_count']}_{config_info['assist_feature_count']} "
             f"interval:{interval} period:{config_info['period_n']} cur_revenue {1} year_revenue {ret_cur_year}")
    logging.getLogger().info(title)
    return ret_cur_year


def get_returns_for_model_series_by_season(prefix=""):
    """获取模型按季度的收益情况（如果需要，可保留或删除）"""
    file_name = 'returns_fc.csv'
    df = pd.read_csv(file_name, index_col=0) if os.path.exists(file_name) else pd.DataFrame()
    ret_df = pd.Series(dtype=float)
    ret_df['feature_count'] = config_info['feature_count']

    files = os.listdir('models')
    pattern = re.compile(r'.*?(\d{8})_t0(?:_.*)?_(\d{8})$')
    datelist = []
    for file in files:
        if not file.startswith(prefix):
            continue
        match = pattern.match(file)
        if match:
            _, date_str = match.groups()
            date = datetime.datetime.strptime(date_str, '%Y%m%d')
            datelist.append((date, file))
    sorted_files = sorted(datelist, key=lambda x: x[0])
    if not sorted_files:
        return

    this_year = sorted_files[0][0].year
    total_ret = 1
    this_year_ret = 1
    mail_msg = ''
    holding_days = int(config_info['topn'] / config_info['max_sell'])
    for i in range(0, len(sorted_files)):
        model_path = sorted_files[i][1]
        start_date = sorted_files[i][0] + datetime.timedelta(days=1)
        prepare_ic(start_date.year - 1)
        end_date = sorted_files[i + 1][0] if (i + 1) < len(sorted_files) else datetime.datetime.today()
        ret = get_relative_returns(start_date, end_date, model_path, config_info['topn'])
        cur_ret = ret.prod() ** (1 / holding_days)
        total_ret *= cur_ret
        if start_date.year == this_year:
            this_year_ret *= cur_ret
        else:
            msg = f'{this_year} ret is {this_year_ret}'
            ret_df[str(this_year)] = this_year_ret
            mail_msg += msg + '\n'
            logging.info(msg)
            this_year = start_date.year
            this_year_ret = cur_ret

    msg = f'{this_year} ret is {this_year_ret}'
    mail_msg += msg + '\n'
    ret_df[str(this_year)] = this_year_ret
    msg = f'total_ret is {total_ret}'
    mail_msg += msg + '\n'
    logging.info(msg)
    ret_df['total'] = total_ret
    ret_df = pd.DataFrame(ret_df).T
    update_returns_csv(df, ret_df, file_name='returns_fc.csv')
    if config_info['notify_by_mail']:
        notify_by_email_with_attachment(f'get_returns_{config_info["feature_count"]}_{config_info["interval"]}',
                                        'returns_fc.csv')


def get_returns_for_model_series_by_year(start_year=2020, end_year=2025, prefix="", select_alpha=None):
    """获取模型按年度的收益情况，按整年进行回测。"""
    file_name = 'returns_fc.csv'
    df = pd.read_csv(file_name, index_col=0) if os.path.exists(file_name) else pd.DataFrame()
    ret_df = pd.Series(dtype=float)

    def skip_year_fn(y):
        return (len(config_info['selected_year']) > 0) and (str(y) not in config_info['selected_year'])

    if select_alpha is not None:
        if select_alpha.startswith('_'):
            ret_df['feature_count'] = f'{config_info["feature_count"]}_-{select_alpha}'
        else:
            ret_df['feature_count'] = f'{config_info["feature_count"]}_{select_alpha}'
    else:
        ret_df['feature_count'] = config_info['feature_count']
    holding_days = int(config_info['topn'] / config_info['max_sell'])
    if holding_days > 1:
        ret_df['feature_count'] = f'{ret_df["feature_count"]}_h{holding_days}'
    rets = []
    today = datetime.datetime.today()
    for year in range(start_year, end_year + 1):
        if skip_year_fn(year):
            continue
        if config_info['fixed_model'] is None:
            prepare_and_train_model(year, prefix)

        end_date = f'{year}1231' if year != today.year else today.strftime('%Y%m%d')
        ret = get_relative_returns(f'{year}0101', end_date,
                                   config_info['fixed_model'] if config_info['fixed_model'] is not None else
                                   config_info['model_name'], config_info['topn'],
                                   selected_alpha=select_alpha)
        cur_ret = ret.prod() ** (1 / holding_days)
        rets.append(cur_ret)
        logging.info(f'{year} ret is {cur_ret}')
        ret_df[str(year)] = cur_ret

    total_ret = 1
    for r in rets:
        total_ret *= r
    logging.info(f'total ret is {total_ret}')
    ret_df['total'] = total_ret
    ret_df = pd.DataFrame(ret_df).T
    update_returns_csv(df, ret_df, file_name='returns_fc.csv')
    if config_info['notify_by_mail']:
        notify_by_email_with_attachment(f'get_returns_{config_info["feature_count"]}_{config_info["interval"]}',
                                        'returns_fc.csv')
    return rets, total_ret


def analysis_selected_stocks_by_dates(start, end, model_name, topn=5):
    """分析选定日期范围内的股票收益。"""
    ret = get_return_infos_of_range(start, end, model_name, topn=topn)
    return ret, ret.describe()


def analysis_gpt_stocks_by_dates(start, end, model_name, topn=10):
    """使用GPT分析选定日期范围内的股票收益。"""
    data, _ = analysis_selected_stocks_by_dates(start, end, model_name, topn=topn)
    ret = pd.DataFrame()
    for date in data.index.get_level_values(0).unique():
        data_in_date = data.loc[data.index.get_level_values(0) == date]
        codes = data_in_date.index.get_level_values(1).unique()
        selected_codes = select_stock(codes, date + pd.DateOffset(days=1))
        selected = data_in_date.loc[data_in_date.index.get_level_values(1).isin(selected_codes)]
        ret = pd.concat([ret, selected], axis=0)
    return ret, ret.describe()


def print_ret_range_info(ret):
    """打印收益范围的详细信息。"""
    print('---------------return describe --------------------')
    print(ret.describe())
    print('---------------return rate statics--------------------')
    print(ret.groupby('date').mean().mean())


def modify_model_name(model_name: str):
    """根据市场修改model_name。"""
    market = configs.convert_to_csi_code(config_info['market'])
    if market == 'csi300':
        return model_name
    else:
        index = model_name.rfind('_')
        return model_name[:index] + '_' + market + model_name[index:]


def seed_everything(seed):
    """设置随机种子以确保结果可复现。"""
    random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    if not config_info['cpu']:
        torch.use_deterministic_algorithms(True)


def draw_weights_vs_returns(start_year, end_year):
    """绘制weight列随return列变化的散点图"""
    import matplotlib.pyplot as plt
    import seaborn as sns

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    print(f"正在加载 {start_year} 到 {end_year} 年的股票数据...")

    # 加载股票数据
    df = util.load_stock_data(config_info['alpha_type'], config_info['market'])
    factors = util.get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'])

    all_data = []

    for year in range(start_year, end_year + 1):
        print(f"处理 {year} 年数据...")
        start_date = f'{year}0101'
        end_date = f'{year}1231'

        # 调用prepare_data_for_model
        year_data = util.prepare_data_for_model(df, factors, config_info['period_n'], start_date, end_date)

        if 'weight' in year_data.columns and 'return' in year_data.columns:
            # 只保留有效数据
            valid_data = year_data[['weight', 'return']].dropna()
            if not valid_data.empty:
                valid_data['year'] = year
                all_data.append(valid_data)
                print(f"  {year} 年有效数据点: {len(valid_data)}")
        else:
            print(f"  {year} 年数据中缺少 weight 或 return 列")

    if not all_data:
        print("没有找到包含 weight 和 return 列的有效数据")
        return

    # 合并所有年份的数据
    combined_data = pd.concat(all_data, ignore_index=True)
    print(f"总共有效数据点: {len(combined_data)}")

    # 创建图表
    plt.figure(figsize=(12, 8))

    # 绘制散点图
    scatter = plt.scatter(combined_data['return'], combined_data['weight'],
                         c=combined_data['year'], cmap='viridis', alpha=0.6, s=20)

    # 添加颜色条
    cbar = plt.colorbar(scatter)
    cbar.set_label('年份', fontsize=12)

    # 计算相关系数
    correlation = combined_data['return'].corr(combined_data['weight'])

    # 添加趋势线
    z = np.polyfit(combined_data['return'], combined_data['weight'], 1)
    p = np.poly1d(z)
    plt.plot(combined_data['return'], p(combined_data['return']), "r--", alpha=0.8, linewidth=2)

    # 设置标签和标题
    plt.xlabel('Return', fontsize=12)
    plt.ylabel('Weight', fontsize=12)
    plt.title(f'Weight vs Return 散点图 ({start_year}-{end_year})\n相关系数: {correlation:.4f}', fontsize=14)
    plt.grid(True, alpha=0.3)

    # 保存图片
    filename = f'weight_vs_return_{start_year}_{end_year}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"图表已保存为: {filename}")

    # 显示统计信息
    print(f"\n统计信息:")
    print(f"Return 范围: {combined_data['return'].min():.6f} 到 {combined_data['return'].max():.6f}")
    print(f"Weight 范围: {combined_data['weight'].min():.6f} 到 {combined_data['weight'].max():.6f}")
    print(f"相关系数: {correlation:.6f}")

    # 按年份显示统计
    print(f"\n按年份统计:")
    for year in range(start_year, end_year + 1):
        year_data = combined_data[combined_data['year'] == year]
        if not year_data.empty:
            year_corr = year_data['return'].corr(year_data['weight'])
            print(f"  {year}: 数据点 {len(year_data)}, 相关系数 {year_corr:.6f}")

    # 按weights分为10等段统计
    print(f"\n按weights分段统计 (10等段):")
    print("=" * 80)

    # 按weights排序
    sorted_data = combined_data.sort_values('weight')

    # 分为10等段
    n_segments = 10
    segment_size = len(sorted_data) // n_segments

    segment_stats = []

    for i in range(n_segments):
        start_idx = i * segment_size
        if i == n_segments - 1:  # 最后一段包含所有剩余数据
            end_idx = len(sorted_data)
        else:
            end_idx = (i + 1) * segment_size

        segment_data = sorted_data.iloc[start_idx:end_idx]

        if not segment_data.empty:
            # 计算return绝对值的统计信息
            abs_returns = segment_data['return'].abs()

            stats = {
                'segment': i + 1,
                'count': len(segment_data),
                'weight_min': segment_data['weight'].min(),
                'weight_max': segment_data['weight'].max(),
                'weight_mean': segment_data['weight'].mean(),
                'return_abs_mean': abs_returns.mean(),
                'return_abs_var': abs_returns.var(),
                'return_abs_std': abs_returns.std(),
                'return_mean': segment_data['return'].mean(),
                'return_std': segment_data['return'].std()
            }

            segment_stats.append(stats)

            print(f"第 {i+1:2d} 段: 数据点 {len(segment_data):6d} | "
                  f"Weight范围 [{stats['weight_min']:8.6f}, {stats['weight_max']:8.6f}] | "
                  f"Weight均值 {stats['weight_mean']:8.6f}")
            print(f"        |Return|均值: {stats['return_abs_mean']:8.6f} | "
                  f"|Return|方差: {stats['return_abs_var']:8.6f} | "
                  f"|Return|标准差: {stats['return_abs_std']:8.6f}")
            print(f"        Return均值: {stats['return_mean']:8.6f} | "
                  f"Return标准差: {stats['return_std']:8.6f}")
            print("-" * 80)

    # 创建分段统计的可视化
    if segment_stats:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        segments = [s['segment'] for s in segment_stats]
        weight_means = [s['weight_mean'] for s in segment_stats]
        return_abs_means = [s['return_abs_mean'] for s in segment_stats]
        return_abs_vars = [s['return_abs_var'] for s in segment_stats]
        return_means = [s['return_mean'] for s in segment_stats]

        # 子图1: Weight均值 vs 段数
        ax1.bar(segments, weight_means, alpha=0.7, color='skyblue')
        ax1.set_xlabel('分段')
        ax1.set_ylabel('Weight均值')
        ax1.set_title('各段Weight均值')
        ax1.grid(True, alpha=0.3)

        # 子图2: |Return|均值 vs 段数
        ax2.bar(segments, return_abs_means, alpha=0.7, color='lightcoral')
        ax2.set_xlabel('分段')
        ax2.set_ylabel('|Return|均值')
        ax2.set_title('各段|Return|均值')
        ax2.grid(True, alpha=0.3)

        # 子图3: |Return|方差 vs 段数
        ax3.bar(segments, return_abs_vars, alpha=0.7, color='lightgreen')
        ax3.set_xlabel('分段')
        ax3.set_ylabel('|Return|方差')
        ax3.set_title('各段|Return|方差')
        ax3.grid(True, alpha=0.3)

        # 子图4: Weight均值 vs |Return|均值
        ax4.scatter(weight_means, return_abs_means, s=100, alpha=0.7, color='purple')
        for i, (x, y) in enumerate(zip(weight_means, return_abs_means)):
            ax4.annotate(f'{i+1}', (x, y), xytext=(5, 5), textcoords='offset points')

        # 添加趋势线
        if len(weight_means) > 1:
            z = np.polyfit(weight_means, return_abs_means, 1)
            p = np.poly1d(z)
            ax4.plot(weight_means, p(weight_means), "r--", alpha=0.8)

            # 计算相关系数
            segment_corr = np.corrcoef(weight_means, return_abs_means)[0, 1]
            ax4.set_title(f'Weight均值 vs |Return|均值\n(相关系数: {segment_corr:.4f})')
        else:
            ax4.set_title('Weight均值 vs |Return|均值')

        ax4.set_xlabel('Weight均值')
        ax4.set_ylabel('|Return|均值')
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存分段统计图
        segment_filename = f'weight_segments_analysis_{start_year}_{end_year}.png'
        plt.savefig(segment_filename, dpi=300, bbox_inches='tight')
        print(f"\n分段统计图已保存为: {segment_filename}")

        # 保存统计数据到CSV
        segment_df = pd.DataFrame(segment_stats)
        csv_filename = f'weight_segments_stats_{start_year}_{end_year}.csv'
        segment_df.to_csv(csv_filename, index=False)
        print(f"分段统计数据已保存为: {csv_filename}")

        plt.show()

    plt.show()

def find_best_random_trained_model(prefix="", start_year=2018, end_year=None):
    for year in range(start_year, end_year + 1):
        max_ret = -1
        max_model_name = None
        for train_irr in range(0, 10):
            model_name = get_model_name(year, prefix=f'{prefix}_{train_irr}_')
            config_info['train_seed'] = train_irr
            if load_model(model_name) is None:
                train(config_info['alpha_type'], config_info['period_n'], f'{year - 1}1231', model_name)
            pre_test_year = year - 1
            ret = get_relative_returns(f'{pre_test_year}0101', f'{pre_test_year}1231', model_name, config_info['topn'],
                                       tuning_sec_only=True)
            holding_days = int(config_info['topn'] / config_info['max_sell'])
            cur_ret = ret.prod() ** (1 / holding_days)
            if cur_ret > max_ret:
                max_ret = cur_ret
                max_model_name = model_name
                logging.info(f'select {train_irr} as best model')
        shutil.copytree(f'models/{max_model_name}', f'models/{get_model_name(year, prefix)}')


def current_best_backtrade(market=None, prefix="", start_year=2018, save_path='fc_res.csv',
                           end_year=None):
    """运行当前最佳策略的回测，按整年进行。"""
    interval = config_info['interval']
    if end_year is None:
        end_year = datetime.date.today().year
    if market is None:
        market = config_info['market']

    if config_info['enable_min_res']:
        min_res = calculate_annual_equal_weight_return(configs.convert_to_csi_code(market), start_year, end_year)
        print('基准收益率:', min_res)
        min_res = {k: max(v + 1, 1) for k, v in min_res.items()}
        if isinstance(config_info['enable_min_res'], str):
            min_res = ast.literal_eval(config_info['enable_min_res'])


    if os.path.exists(save_path):
        df = pd.read_csv(save_path, index_col=0)
    else:
        df = pd.DataFrame()

    trade_file = f'trade_{config_info["feature_count"]}.csv'
    if os.path.exists(trade_file) and not config_info['use_rank_model'] and config_info['trade_log']:
        os.remove(trade_file)

    init_qlib()
    config_info['market'] = market
    ret = 1
    now = get_last_time()
    ret_df = pd.Series(dtype=float)

    # 设置策略名称
    if config_info['fixed_model'] is not None:
        ret_df['name'] = config_info['fixed_model']
    elif config_info['assist_feature_count'] != config_info['feature_count']:
        ret_df['name'] = f'{prefix}{market}_combine_{config_info["feature_count"]}'
    else:
        ret_df['name'] = f'{prefix}{market}_{config_info["feature_count"]}'
    if config_info['add_info']:
        ret_df['name'] += '_addinfo'
    if config_info['added_alpha_for_stratergy'] is not None:
        ret_df['name'] += f"_{config_info['added_alpha_for_stratergy']}"
    ret_df['name'] += f'_p{config_info["period_n"]}_top_{config_info["topn"]}'
    if config_info['cpu']:
        ret_df['name'] += '_cpu'

    listener = YearBacktraderListener(prefix, interval)

    # 处理所有年份回测
    years_to_test = config_info['selected_year'] if len(config_info['selected_year']) > 0 else range(end_year,
                                                                                                     start_year - 1, -1)
    years_to_test = [y for y in years_to_test if int(y) in range(end_year, start_year - 1, -1)]
    for test_year in years_to_test:
        test_year = int(test_year)
        # 当年特殊处理
        if test_year == now.year:
            start_date = f'{test_year}0101'
            end_date = now.strftime('%Y%m%d')
        else:
            start_date = f'{test_year}0101'
            end_date = f'{test_year}1231'

        listener.on_pre_year_backtrader(test_year)
        ret_cur_year, drawdown_len, drawdown_max, sharpe_ratio, sortino_ratio = run_and_analyze_strategy(start_date,
                                                                                                         end_date,
                                                                                                         interval,
                                                                                                         prefix)

        ret *= ret_cur_year
        ret_df[str(test_year)] = ret_cur_year
        ret_df[str(test_year) + '_drawdown_len'] = drawdown_len
        ret_df[str(test_year) + '_drawdown_max'] = drawdown_max
        ret_df[str(test_year) + '_sharpe_ratio'] = sharpe_ratio
        ret_df[str(test_year) + '_sortino_ratio'] = sortino_ratio

        # 当年需要发送更详细的通知
        if test_year == now.year:
            title = (f"{test_year} fc:{config_info['feature_count']}_{config_info['assist_feature_count']} "
                     f"interval:{interval} period:{config_info['period_n']} total_revenue {ret} "
                     f"year_revenue {ret_cur_year}")
        else:
            title = f'{test_year} interval {interval} total_revenue {ret} year_revenue {ret_cur_year}'
        notify_results(title, attachment=None)

        if config_info['enable_min_res'] and ret_cur_year < min_res.get(test_year, 0):
            break

    title = (f"fc:{config_info['feature_count']}_{config_info['assist_feature_count']} "
             f"interval:{interval} period:{config_info['period_n']} total_revenue {ret}")
    notify_results(title)

    ret_df['total'] = ret
    ret_df['feature_count'] = config_info['feature_count']
    ret_df['drawdown_len'] = ret_df.filter(like='_drawdown_len').sum()
    ret_df['drawdown_max'] = ret_df.filter(like='_drawdown_max').max()
    ret_df['sharpe_ratio'] = ret_df.filter(like='_sharpe_ratio').mean()
    ret_df['sortino_ratio'] = ret_df.filter(like='_sortino_ratio').mean()
    ret_df = pd.DataFrame(ret_df).T
    pd.concat([df, ret_df], ignore_index=True).to_csv(save_path)
    if config_info['notify_by_mail']:
        dump_fc_res(f'{save_path}_{config_info["feature_count"]}', save_path)
    return ret, ret_df


def dump_fc_res(title, fc_res_path):
    df = pd.read_csv(fc_res_path, index_col=0)
    last_total = df.iloc[-1]['total'] if 'total' in df.columns else None
    last_name = df.iloc[-1]['name'] if 'name' in df.columns else None

    max_total_row = df.loc[df['total'].idxmax()] if 'total' in df.columns else None
    max_total = max_total_row['total'] if max_total_row is not None else None
    max_total_name = max_total_row['name'] if max_total_row is not None else None

    msg = f"Last: {last_total}, Name: {last_name}"
    msg += f"\nMax : {max_total}, Name: {max_total_name}"

    send_email_attachment(title, config_info['mail'], msg, fc_res_path)


def get_feature_names_from_fc_res(fc_res_path):
    fc_res_file = pd.read_csv(fc_res_path)
    feature_names = []
    for name in fc_res_file['name']:
        parts = name.split('_')
        stock_code_idx = -1
        for i, part in enumerate(parts):
            if len(part) == 6 and part.isdigit():
                stock_code_idx = i
                break

        feature_name = parts[stock_code_idx + 2]
        if len(feature_name) == 0:
            feature_name = f'_{parts[stock_code_idx + 3]}'
        feature_names.append(feature_name)
    return feature_names


def save_ensemble_results_to_csv(ensemble_results, file_name='ensemble_results.csv'):
    """
    将模型组合的测试结果保存到CSV文件中

    Args:
        ensemble_results: 包含模型组合测试结果的列表，每个元素是(ensemble_name, return, model_combo)的元组
        file_name: 保存结果的CSV文件名
    """
    # 转换ensemble_results为DataFrame
    result_data = []
    for name, ret, models in ensemble_results:
        result_data.append({
            'ensemble_name': name,
            'return': ret,
            'model_combo': str(models),
            'timestamp': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
        })

    result_df = pd.DataFrame(result_data)

    result_df.to_csv(file_name, index=False)


def train_all_and_ensemble(alpha_type, period, train_end_date, model_name):
    """使用ALL_HYPERPARAMETERS中的所有参数训练模型，然后创建三三组合的模型集成"""
    logging.getLogger().info(f"开始使用所有超参数训练模型 {model_name}")

    # 加载数据
    df = load_stock_data(alpha_type, config_info['market'])
    factors = get_feature_names(alpha_type, config_info['market'], period)

    # 准备训练数据
    train_end = pd.to_datetime(train_end_date)
    train_start = train_end - pd.DateOffset(years=config_info['train_year'])
    train_start = train_start.replace(month=1, day=1)

    # 检查是否有足够的模型
    predictor = load_model(model_name)
    if predictor is None:
        logging.getLogger().info(f"没有找到模型 {model_name}，开始训练所有超参数的模型")
        predictor = train_model(df, factors, period, train_start, train_end, model_name)

    if predictor is None or not predictor.is_fit:
        logging.getLogger().error(f"模型 {model_name} 训练失败")
        return None, None

    # 获取所有L1模型名称
    all_models = predictor.model_names()
    l1_models = [m for m in all_models if 'WeightedEnsemble' not in m]

    logging.getLogger().info(f"找到 {len(l1_models)} 个L1模型，开始创建三三组合模型")

    # 获取测试数据范围
    test_start_date = train_end + pd.DateOffset(days=1)
    test_end_date = test_start_date + pd.DateOffset(days=365)

    # 用于保存结果的数据结构
    ensemble_results = []

    all_combos = []
    all_combos.extend(combinations(l1_models, 3))
    for model_combo in all_combos:
        ensemble_name = f"Ensemble_{'_'.join(model_combo)}"
        logging.getLogger().info(f"测试模型组合: {ensemble_name}")
        if f'WeightedEnsemble_L2{ensemble_name}' not in all_models:
            predictor.fit_weighted_ensemble(
                base_models=list(model_combo),
                name_suffix=ensemble_name)

        try:
            ret, stats, _ = test_model_stratergy(alpha_type,
                                                 test_start_date.strftime('%Y%m%d'),
                                                 test_end_date.strftime('%Y%m%d'),
                                                 model_name, f'WeightedEnsemble_L2{ensemble_name}')
            ensemble_results.append((ensemble_name, ret, model_combo))
            logging.getLogger().info(f"模型组合 {ensemble_name} 回报率: {ret}")
            save_ensemble_results_to_csv(ensemble_results)
        except Exception as e:
            logging.getLogger().error(f"测试模型组合 {ensemble_name} 失败: {str(e)}")

    # 找出最佳组合
    if ensemble_results:
        best_ensemble = max(ensemble_results, key=lambda x: x[1])
        logging.getLogger().info(f"最佳模型组合: {best_ensemble[0]}")
        logging.getLogger().info(f"回报率: {best_ensemble[1]}")
        logging.getLogger().info(f"组成模型: {best_ensemble[2]}")
        # 将最佳组合保存为单独的模型
        best_model_path = f"{model_name}_best_{best_ensemble[0]}"
        return best_model_path, best_ensemble
    else:
        logging.getLogger().error("没有成功测试任何模型组合")
        return None, None


def find_best_model_combination(year):
    logging.getLogger().info("开始测试模型组合")

    original_models_to_use = config_info['models_to_use'].copy() if isinstance(config_info['models_to_use'], list) else \
        config_info['models_to_use'].split(',')
    original_gbm_to_use = config_info['gbm_to_use'].copy() if isinstance(config_info['gbm_to_use'], list) else [int(x)
                                                                                                                for x in
                                                                                                                config_info[
                                                                                                                    'gbm_to_use'].split(
                                                                                                                    ',')]

    all_model_options = []

    if 'GBM' in original_models_to_use:
        for gbm_version in original_gbm_to_use:
            all_model_options.append(f'GBM{gbm_version}')

    for model in original_models_to_use:
        if model != 'GBM':
            all_model_options.append(model)

    logging.getLogger().info(f"可用模型选项: {all_model_options}")

    combinations_results = []
    env_file = os.path.join(os.getcwd(), '.env')
    backup_env(env_file)

    all_combos = []
    all_combos.extend(combinations(all_model_options, 3))
    for combo in all_combos:
        current_models = []
        current_gbm_versions = []

        for model in combo:
            if model.startswith('GBM'):
                current_models.append('GBM')
                if len(model) > 3:  # 如果有版本号
                    current_gbm_versions.append(int(model[3:]))
            else:
                current_models.append(model)

        current_models = list(set(current_models))
        current_gbm_versions = list(set(current_gbm_versions))

        config_info['models_to_use'] = current_models
        if current_gbm_versions:
            config_info['gbm_to_use'] = current_gbm_versions

        write_env(current_gbm_versions, current_models, env_file)

        prefix = "_".join(combo)
        prefix = f"{prefix}_"

        logging.getLogger().info(f"测试模型组合: {prefix}")
        logging.getLogger().info(f"当前模型设置: models_to_use={current_models}, gbm_to_use={current_gbm_versions}")

        try:

            ret, _ = current_best_backtrade(config_info['market'], prefix, start_year=year, end_year=year)
            config_info['fixed_model'] = None
            combo_result = (
                prefix,
                ret,
                {
                    'models': current_models,
                    'gbm_versions': current_gbm_versions,
                }
            )
            combinations_results.append(combo_result)

            save_ensemble_results_to_csv(combinations_results, file_name='model_combinations_results.csv')

            logging.getLogger().info(f"组合 {prefix} 测试完成: 收益率={ret}")
        except Exception as e:
            logging.getLogger().error(f"测试组合 {prefix} 时出错: {str(e)}")

    config_info['models_to_use'] = original_models_to_use
    config_info['gbm_to_use'] = original_gbm_to_use

    restore_env(env_file)

    if combinations_results:
        best_combo = max(combinations_results, key=lambda x: x[1])
        logging.getLogger().info(f"最佳模型组合: {best_combo[0]}")
        logging.getLogger().info(f"最佳收益率: {best_combo[1]}")
        logging.getLogger().info(f"最佳组合配置: {best_combo[2]}")

        config_info['models_to_use'] = best_combo[2]['models']
        config_info['gbm_to_use'] = best_combo[2]['gbm_versions']

        return best_combo
    else:
        logging.getLogger().error("没有成功测试任何模型组合")
        return None


def restore_env(env_file):
    if os.path.exists(env_file):
        os.remove(env_file)
    backup_env_file = os.path.join(os.getcwd(), '.env.bak')
    if os.path.exists(backup_env_file):
        os.rename(backup_env_file, env_file)


def backup_env(env_file):
    if os.path.exists(env_file):
        backup_env_file = os.path.join(os.getcwd(), '.env.bak')
        with open(env_file, 'r') as f:
            env_content = f.read()
        with open(backup_env_file, 'w') as f:
            f.write(env_content)


def write_env(current_gbm_versions, current_models, env_file):
    env_vars = {}
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                key, _, value = line.strip().partition('=')
                env_vars[key] = value
    # 更新或添加新的值
    env_vars['models_to_use'] = ','.join(current_models)
    env_vars['gbm_to_use'] = ','.join(map(str, current_gbm_versions))
    # 写回 .env 文件
    with open(env_file, 'w') as f:
        for key, value in env_vars.items():
            f.write(f"{key}={value}\n")


def strategy_runner_main():
    """主策略运行函数，解析参数并执行相应操作。"""
    seed_everything(0)
    parser = argparse.ArgumentParser()
    parser.add_argument('-c', '--feature_count', type=int, help='feature count')
    parser.add_argument('--afc', type=int, help='assist feature count')
    parser.add_argument('-t', '--today', action='store_true', help='find stock for today')
    parser.add_argument('-r', '--run_current_best', action='store_true', help='run current best strategy')
    parser.add_argument('--run_all_sub_model', action='store_true', help='run all sub model')

    parser.add_argument('--run_select_alpha', action='store_true', help='select alpha')
    parser.add_argument('--get_returns', action='store_true', help='get_returns')
    parser.add_argument('--evaluate_alpha', action='store_true', help='evaluate_alpha')
    parser.add_argument('--run_cur_year', action='store_true', help='run cur year strategy')
    parser.add_argument('--prepare_model', action='store_true', help='prepare model')
    parser.add_argument('-i', '--compute_ic_table', action='store_true', help='compute ic table')
    parser.add_argument('-g', '--generate_all', type=str, help='generate all data')
    parser.add_argument('-o', '--hyperparameter_tune_off', action='store_true', help='hyperparameter_tune_off')
    parser.add_argument('--cpu', action='store_true', help='cpu mode')
    parser.add_argument('--rec', type=int, help='recommend mode')
    parser.add_argument('--dis_weights421', action='store_true', help='disable weights 421')
    parser.add_argument('-q', '--quality', type=str, help='quality')
    parser.add_argument('-y', '--year', type=int, help='start year')
    parser.add_argument('--end_year', type=int, help='end year')
    parser.add_argument('--curve', type=float, help='curve')
    parser.add_argument('--interval', type=int, help='interval')
    parser.add_argument('--delay', type=int, help='delay')
    parser.add_argument('-d', '--download', action='store_true', help='download data')
    parser.add_argument('--alpha_type', type=str, help='alpha type')
    parser.add_argument('--fixed_model', type=str, help='fixed model name')
    parser.add_argument('--assist_alpha_type', type=str, help='assist alpha type')
    parser.add_argument('--cls_type', action='store_true', help='use cls type')
    parser.add_argument('--assist_cls_type', action='store_true', help='use assist cls type')
    parser.add_argument('--fc_res', type=str, help='fc_res path')
    parser.add_argument('-m', '--mail', type=str, help='mail address')
    parser.add_argument('--market', type=str, help='market')
    parser.add_argument('--model', type=str, help='model name')
    parser.add_argument('--alpha', type=str, help='model name')
    parser.add_argument('--assist_model', type=str, help='assist model name')
    parser.add_argument('--ret_of_day', type=str, help='return of day')
    parser.add_argument('--ret_of_range', type=str, help='return of range')
    parser.add_argument('--relative', action='store_true', help='relative return of range')
    parser.add_argument('--topn', type=int, help='topn')
    parser.add_argument('--tuning_days', type=int, help='tuning_days')
    parser.add_argument('--period_n', type=int, help='period_n')
    parser.add_argument('--assist_period_n', type=int, help='assist_period_n')
    parser.add_argument('--train', type=str, help='train model')
    parser.add_argument('--auto_feature_select', action='store_true', help='auto_feature_select')
    parser.add_argument('--refine', type=str, help='refine data')
    parser.add_argument('--train_year', type=int, help='train year')
    parser.add_argument('--prefix', type=str, help='prefix')
    parser.add_argument('--select_alpha', type=str, help='select_alpha')
    parser.add_argument('--export', action='store_true', help='export data')
    parser.add_argument('--evaluate', action='store_true', help='evaluate')
    parser.add_argument('--notify_by_mail', action='store_true', help='notify by mail')
    parser.add_argument('--train_all_ensemble', action='store_true', help='使用所有超参数训练模型并创建三三组合集成')
    parser.add_argument('--find_best_comb', action='store_true', help='find_best_comb')
    parser.add_argument('--draw_weights', action='store_true', help='draw weight vs return scatter plot')
    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)
    args = parser.parse_args()

    # 初始化
    init_qlib()
    init_logger()
    init_gpt()

    # 配置参数
    config_info['download_data'] = args.download
    config_info['recommend_mode'] = config_info['download_data']
    config_info['fixed_model'] = args.fixed_model
    start_test_year = args.year if args.year is not None else 2020
    end_test_year = args.end_year if args.end_year is not None else datetime.date.today().year
    if args.curve is not None:
        config_info['curve'] = args.curve
    if args.market:
        config_info['market'] = args.market
    if args.notify_by_mail:
        config_info['notify_by_mail'] = True
    fc_res_path = args.fc_res + '.csv' if args.fc_res is not None else 'fc_res.csv'

    interval = args.interval if args.interval is not None else config_info['interval']
    config_info['interval'] = interval
    delay = args.delay if args.delay is not None else config_info['train_delay']
    config_info['train_delay'] = delay

    if args.export:
        dt = pd.read_pickle(f'refined_a458_{config_info["market"]}.pkl')
        dt = dt[dt.index.get_level_values(0) >= '20231001']
        dt.to_csv(f'refined_a458_{config_info["market"]}.csv')
        return

    if args.model is not None:
        config_info['model_name'] = args.model
        config_info['assist_model_name'] = None

    if args.assist_model is not None:
        config_info['assist_model_name'] = args.assist_model

    if args.market is not None and args.model is None:
        config_info['model_name'] = modify_model_name(config_info['model_name'])
        if config_info['assist_model_name']:
            config_info['assist_model_name'] = modify_model_name(config_info['assist_model_name'])

    config_info['weights421'] = not args.dis_weights421

    if args.train_year is not None:
        config_info['train_year'] = args.train_year

    if args.auto_feature_select:
        config_info['auto_feature_select'] = True

    if args.hyperparameter_tune_off:
        config_info['hyperparameter_tune_kwargs'] = False

    if args.mail is not None:
        config_info['mail'] = args.mail

    prefix = args.prefix if args.prefix is not None else ''

    if args.topn is not None:
        config_info['topn'] = args.topn

    if args.period_n is not None:
        config_info['assist_period_n'] = config_info['period_n'] = args.period_n
    if args.assist_period_n is not None:
        config_info['assist_period_n'] = args.assist_period_n

    if args.tuning_days is not None:
        config_info['tuning_days'] = args.tuning_days

    if args.quality is not None:
        config_info['presets'] = args.quality

    if args.cpu:
        config_info['cpu'] = True

    if args.feature_count is not None:
        config_info['feature_count'] = args.feature_count
        config_info['assist_feature_count'] = args.feature_count

    if args.afc is not None:
        config_info['assist_feature_count'] = args.afc

    if args.alpha_type is not None:
        config_info['alpha_type'] = args.alpha_type

    if args.assist_alpha_type is not None:
        config_info['assist_alpha_type'] = args.assist_alpha_type

    if args.cls_type:
        config_info['train_type'] = 'cls'

    if args.assist_cls_type:
        config_info['assist_train_type'] = 'cls'

    if args.refine is not None:
        gates = ast.literal_eval(args.refine)
        df = pd.read_pickle(f'refined_{config_info["alpha_type"]}_{config_info["market"]}.pkl')
        df = refine_stock_df(df, gates)
        df.to_pickle(f'refined_{config_info["alpha_type"]}_{config_info["market"]}.pkl')

    if args.evaluate:
        evaluate_model(config_info['alpha_type'], config_info['period_n'],
                       f'{start_test_year}0101', f'{end_test_year}1231', config_info['model_name'])

    if args.generate_all:
        date = datetime.date.today() if args.generate_all == 'today' \
            else datetime.datetime.strptime(args.generate_all, '%Y%m%d').date()
        fc = config_info['feature_count']
        config_info['generate_all_features'] = True
        config_info['feature_count'] = MAX_FEATURE_COUNT
        prepare_data_for_all_year(end_date=date)
        config_info['feature_count'] = fc
        if config_info['alpha_158']:
            logging.getLogger().info("开始处理alpha_158数据准备")
            prepare_alpha158_data(datetime.date(2008, 1, 1), date)
            logging.getLogger().info("正在与a300数据合并")
            combined_with_a300()
            logging.getLogger().info("Alpha_158数据准备完成")
        else:
            logging.getLogger().info(f"正在加载精炼数据: refined_{config_info['alpha_type']}_{config_info['market']}.pkl")
            df = pd.read_pickle(f'refined_{config_info["alpha_type"]}_{config_info["market"]}.pkl')
            logging.getLogger().info(f"正在精炼股票数据框，共{len(df)}行")
            df = refine_stock_df(df, with_flag=False)
            logging.getLogger().info(f"正在保存精炼数据到pickle文件")
            df.to_pickle(f'refined_{config_info["alpha_type"]}_{config_info["market"]}.pkl')

        if os.path.exists('basic_data.pkl'):
            logging.getLogger().info("发现basic_data.pkl文件，开始合并流程")
            logging.getLogger().info(f"正在加载refined_a458_{config_info['market']}.pkl")
            df = pd.read_pickle(f'refined_a458_{config_info["market"]}.pkl')
            logging.getLogger().info(f"正在加载basic_data.pkl")

            basic_info = pd.read_pickle('basic_data.pkl')
            basic_info.columns = [f'basic_{col}' for col in basic_info.columns]

            logging.getLogger().info(f"正在合并数据框: df形状{df.shape}, basic_info形状{basic_info.shape}")
            df = pd.merge(df, basic_info, how='left', left_index=True, right_index=True)
            logging.getLogger().info(f"合并完成，最终形状: {df.shape}")
            df.to_pickle(f'refined_a458_{config_info["market"]}.pkl')

        # 合并市场状态数据
        regime_file = f'{config_info["market"]}_regimeID.csv'
        if os.path.exists(regime_file):
            logging.getLogger().info(f"发现市场状态文件: {regime_file}，开始合并市场状态数据")
            logging.getLogger().info(f"正在加载refined_a458_{config_info['market']}.pkl用于状态合并")
            df = pd.read_pickle(f'refined_a458_{config_info["market"]}.pkl')
            logging.getLogger().info(f"正在从{regime_file}加载市场状态数据")
            regime_data = pd.read_csv(regime_file, parse_dates=['date'])
            logging.getLogger().info(f"市场状态数据已加载，共{len(regime_data)}行，日期范围: {regime_data['date'].min()} 到 {regime_data['date'].max()}")
            regime_data.set_index('date', inplace=True)

            # 将df重置索引以便合并
            logging.getLogger().info("正在重置df索引以进行合并操作")
            df_reset = df.reset_index()
            # 合并regime数据，基于date列
            logging.getLogger().info(f"正在合并市场状态数据: df形状{df_reset.shape}, 状态数据形状{regime_data.shape}")
            df_merged = pd.merge(df_reset, regime_data, left_on='date', right_index=True, how='left')
            logging.getLogger().info(f"市场状态合并完成，合并后形状: {df_merged.shape}")
            # 恢复原始索引
            logging.getLogger().info("正在恢复原始多重索引")
            df_merged.set_index(['date', 'sec_id'], inplace=True)
            df_merged.to_pickle(f'refined_a458_{config_info["market"]}.pkl')
            logging.getLogger().info("市场状态数据合并完成并已保存")
        else:
            logging.getLogger().info(f"未找到市场状态文件{regime_file}，跳过市场状态数据合并")


    elif args.compute_ic_table:
        start_year_ic = int(args.year)
        end_year_ic = int(args.end_year)
        for year in range(start_year_ic, end_year_ic + 1):
            df = load_stock_data(config_info['alpha_type'], config_info['market'])
            end = datetime.datetime(year, 12, 31)
            start = end - datetime.timedelta(days=365 * args.train_year)
            end = pd.to_datetime(end)
            start = pd.to_datetime(start)

            # Load existing IC file if it exists
            ic_file_path = f'{config_info["alpha_type"]}_{config_info["market"]}_ic_{year}.csv'
            if os.path.exists(ic_file_path):
                existing_ic = pd.read_csv(ic_file_path, index_col=0)
            else:
                existing_ic = None
            ic = compute_ic_table_for_data(df, start, end,
                                           existing_ic.index.to_list() if existing_ic is not None else None)

            # Merge with existing results if they exist
            if existing_ic is not None:
                ic = pd.concat([existing_ic, ic])

            # Sort by feature sequence if available
            try:
                with open('feature_seq.txt', 'r') as file:
                    index_order = [line.strip() for line in file]
                    leftover_index = ic.index.difference(index_order)
                    new_index = list(index_order) + list(leftover_index)
                    ic = ic.reindex(new_index)
            except FileNotFoundError:
                pass

            ic.to_csv(ic_file_path)

    if args.run_current_best:
        current_best_backtrade(start_year=start_test_year, end_year=end_test_year,
                               save_path=fc_res_path, prefix=prefix)
    if args.run_all_sub_model:
        run_all_sub_model(f'{args.year}0101', f'{args.year}1231', args.model)
    if args.prepare_model:
        find_best_random_trained_model(start_year=start_test_year, end_year=end_test_year, prefix=prefix)

    if args.get_returns:
        if config_info['interval'] >= 250:
            get_returns_for_model_series_by_year(start_test_year, end_test_year, prefix, args.select_alpha)
        else:
            get_returns_for_model_series_by_season(prefix)

    if args.run_select_alpha:
        if os.path.exists('fc_res_prev.csv'):
            feature_names = get_feature_names_from_fc_res('fc_res_prev.csv')
            from_prev = True
        else:
            feature_names = get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'],
                                              1000)
            from_prev = False

        if os.path.exists(fc_res_path):
            exists_feature_names = get_feature_names_from_fc_res(fc_res_path)
        else:
            exists_feature_names = []

        for fn in feature_names:
            if fn in exists_feature_names:
                continue
            print('-------------------', fn)
            config_info['added_alpha_for_stratergy'] = fn
            current_best_backtrade(start_year=start_test_year, end_year=end_test_year, save_path=fc_res_path,
                                   prefix=prefix)
            config_info['fixed_model'] = None

            if not from_prev:
                config_info['added_alpha_for_stratergy'] = f'_{fn}'
                current_best_backtrade(start_year=start_test_year, end_year=end_test_year, save_path=fc_res_path,
                                       prefix=prefix)
                config_info['fixed_model'] = None

    if args.evaluate_alpha:
        research_alpha(f'{args.year}0101', f'{args.year}1231', args.alpha, args.model)

    if args.run_cur_year:
        run_current_year_backtrade(prefix)

    if args.rec is not None:
        if os.path.exists('dibeili_set.pkl'):
            config_info['interest'] = config_info['interest'] + pickle.load(open('dibeili_set.pkl', 'rb'))
        recommend_for_today(with_gpt=(args.rec == 1), topn=10)
    elif args.ret_of_day is not None:
        ret = get_return_infos_of_range(args.ret_of_day, args.ret_of_day, config_info['model_name'],
                                        topn=config_info['topn'],
                                        assist_model_name=config_info['assist_model_name'])
        print(ret)
        print(ret.mean())
    elif args.ret_of_range is not None:
        days = args.ret_of_range.split('-')
        ret = get_return_infos_of_range(days[0], days[1], config_info['model_name'], topn=config_info['topn'],
                                        assist_model_name=config_info['assist_model_name'])
        print_ret_range_info(ret)
        if 'assist_return' in ret.columns:
            ret = ret[ret['assist_return'] > 0.6]
            print('---------------return detail of assisted model --------------------')
            print_ret_range_info(ret)
    if args.train:
        train_date = pd.to_datetime(str(args.train))
        train_date = train_date - datetime.timedelta(days=1)
        train(config_info['alpha_type'], config_info['period_n'], train_date, config_info['model_name'])

    if args.find_best_comb:
        ret = find_best_model_combination(start_test_year)
        logging.info(ret)

    if args.train_all_ensemble:
        end_date = args.end_year if args.end_year is not None else datetime.date.today().year
        end_date = datetime.date(end_date, 12, 31)
        train_all_and_ensemble(
            config_info['alpha_type'],
            config_info['period_n'],
            end_date,
            config_info['model_name']
        )

    if args.draw_weights:
        draw_weights_vs_returns(start_test_year, end_test_year)


if __name__ == '__main__':
    strategy_runner_main()
