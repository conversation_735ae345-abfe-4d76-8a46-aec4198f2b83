#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TrendPositionTopNStrategy策略的功能
"""

import pandas as pd
import numpy as np
import backtrader as bt
import logging
from datetime import datetime, timedelta
import sys
import os

# 添加当前目录到路径
sys.path.append(os.getcwd())

# 创建一个模拟的D对象用于测试，避免qlib依赖
class MockD:
    @staticmethod
    def list_instruments(*args, **kwargs):
        return [f'00000{i}' for i in range(1, 21)]

    @staticmethod
    def instruments(*args, **kwargs):
        return "mock_market"

# 导入util模块并替换D对象
import util
util.D = MockD()
print("使用模拟的qlib数据接口")

from util import TrendPositionTopNStrategy
import configs
from configs import config_info, convert_to_csi_code

# 添加缺失的函数
def predict_ranks(model, stocks, date):
    """模拟的排名预测函数"""
    return pd.DataFrame()

def prepare_bt_pred_data(predictions, param1, param2):
    """模拟的预测数据准备函数"""
    return pd.DataFrame(columns=['sec_id', 'rank'])

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_test_data():
    """创建测试用的股票数据和预测数据"""
    
    # 创建测试的指数数据
    dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
    # 过滤掉周末
    dates = dates[dates.weekday < 5]
    
    # 生成模拟的指数数据
    np.random.seed(42)
    n_days = len(dates)
    
    # 生成价格数据（随机游走）
    returns = np.random.normal(0.001, 0.02, n_days)  # 日收益率
    prices = 3000 * np.cumprod(1 + returns)  # 从3000点开始
    
    # 生成OHLC数据
    opens = prices * (1 + np.random.normal(0, 0.005, n_days))
    highs = np.maximum(opens, prices) * (1 + np.abs(np.random.normal(0, 0.01, n_days)))
    lows = np.minimum(opens, prices) * (1 - np.abs(np.random.normal(0, 0.01, n_days)))
    volumes = np.random.lognormal(15, 0.5, n_days)  # 成交量
    
    index_data = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': prices,
        'volume': volumes
    }, index=dates)
    
    # 保存指数数据
    index_file = f'{config_info["market"]}.csv'
    index_data.to_csv(index_file)
    print(f"创建测试指数数据: {index_file}")
    
    # 创建测试的预测数据
    pred_dates = dates[::5]  # 每5天一个预测
    stocks = [f'00000{i}' for i in range(1, 21)]  # 20只股票
    
    pred_data_list = []
    for date in pred_dates:
        for i, stock in enumerate(stocks):
            # 生成随机的预测收益和排名
            pred_return = np.random.normal(0.02, 0.05)
            rank = i + 1 + np.random.randint(-2, 3)  # 添加一些随机性
            rank = max(1, min(len(stocks), rank))
            
            pred_data_list.append({
                'date': date,
                'sec_id': stock,
                'return': pred_return,
                'rank': rank
            })
    
    pred_data = pd.DataFrame(pred_data_list)
    return index_data, pred_data

def create_test_stock_data(pred_data, index_data):
    """创建测试用的股票价格数据"""
    stocks = pred_data['sec_id'].unique()
    dates = index_data.index
    
    stock_data = {}
    
    for stock in stocks:
        # 生成与指数相关的股票价格
        np.random.seed(hash(stock) % 1000)  # 为每只股票设置不同的随机种子
        
        # 股票收益率与指数收益率相关
        index_returns = index_data['close'].pct_change().fillna(0)
        beta = np.random.uniform(0.8, 1.5)  # 股票的beta值
        alpha = np.random.normal(0, 0.001)  # 股票的alpha
        
        stock_returns = alpha + beta * index_returns + np.random.normal(0, 0.01, len(dates))
        stock_prices = 10 * np.cumprod(1 + stock_returns)  # 从10元开始
        
        # 生成OHLC数据
        opens = stock_prices * (1 + np.random.normal(0, 0.005, len(dates)))
        highs = np.maximum(opens, stock_prices) * (1 + np.abs(np.random.normal(0, 0.015, len(dates))))
        lows = np.minimum(opens, stock_prices) * (1 - np.abs(np.random.normal(0, 0.015, len(dates))))
        volumes = np.random.lognormal(12, 0.5, len(dates))
        
        stock_df = pd.DataFrame({
            'Open': opens,
            'High': highs,
            'Low': lows,
            'Close': stock_prices,
            'Volume': volumes
        }, index=dates)
        
        stock_data[stock] = stock_df
    
    return stock_data

class TestCerebro:
    """简化的测试环境"""
    
    def __init__(self):
        self.cerebro = bt.Cerebro()
        self.cerebro.broker.setcash(1000000)  # 100万初始资金
        self.cerebro.broker.setcommission(commission=0.001)  # 0.1%手续费
        
    def add_strategy_and_data(self, pred_data, stock_data, index_data):
        """添加策略和数据"""
        
        # 添加策略
        self.cerebro.addstrategy(
            TrendPositionTopNStrategy,
            topn=10,
            printlog=True,
            pred_data=pred_data,
            trade_data=[pd.DataFrame(columns=['sec_id', 'date'])],
            enable_trend_control=True,
            trend_lookback=20,
            position_levels=[0, 0.4, 0.7, 1.0],
            trend_thresholds=[-0.5, 0, 0.5],
            rebalance_threshold=0.1
        )
        
        # 添加股票数据
        for stock_code, stock_df in stock_data.items():
            data = bt.feeds.PandasData(dataname=stock_df)
            self.cerebro.adddata(data, name=stock_code)
        
        # 添加指数数据
        index_df = index_data.copy()
        index_df.columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        data = bt.feeds.PandasData(dataname=index_df)
        self.cerebro.adddata(data, name=config_info["market"])
        
    def run_test(self):
        """运行测试"""
        print("开始运行策略测试...")
        print(f"初始资金: {self.cerebro.broker.getvalue():,.2f}")
        
        results = self.cerebro.run()
        
        print(f"最终资金: {self.cerebro.broker.getvalue():,.2f}")
        print(f"总收益: {(self.cerebro.broker.getvalue() / 1000000 - 1) * 100:.2f}%")
        
        return results

def main():
    """主测试函数"""
    print("=== TrendPositionTopNStrategy 测试 ===")
    
    # 初始化配置
    configs.init_config()
    
    try:
        # 创建测试数据
        print("1. 创建测试数据...")
        index_data, pred_data = create_test_data()
        stock_data = create_test_stock_data(pred_data, index_data)
        
        print(f"   - 指数数据: {len(index_data)} 天")
        print(f"   - 预测数据: {len(pred_data)} 条记录")
        print(f"   - 股票数据: {len(stock_data)} 只股票")
        
        # 创建测试环境
        print("2. 设置测试环境...")
        test_cerebro = TestCerebro()
        test_cerebro.add_strategy_and_data(pred_data, stock_data, index_data)
        
        # 运行测试
        print("3. 运行策略测试...")
        results = test_cerebro.run_test()
        
        print("4. 测试完成!")
        
        # 清理测试文件
        index_file = f'{config_info["market"]}.csv'
        if os.path.exists(index_file):
            os.remove(index_file)
            print(f"清理测试文件: {index_file}")
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
