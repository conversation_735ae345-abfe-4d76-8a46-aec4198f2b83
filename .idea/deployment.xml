<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PublishConfigData" autoUpload="Always" serverName="ray@10.0.10.145:22 agent" remoteFilesAllowedToDisappearOnAutoupload="false" confirmBeforeUploading="false">
    <option name="confirmBeforeUploading" value="false" />
    <serverData>
      <paths name="ray@10.0.10.145:22 agent">
        <serverdata>
          <mappings>
            <mapping deploy="/home/<USER>/ai_retry" local="$PROJECT_DIR$" />
          </mappings>
          <excludedPaths>
            <excludedPath local="true" path="$PROJECT_DIR$/900010" />
            <excludedPath local="true" path="$PROJECT_DIR$/900020" />
            <excludedPath local="true" path="$PROJECT_DIR$/900020_bk" />
          </excludedPaths>
        </serverdata>
      </paths>
      <paths name="ray@10.0.10.156:22 agent">
        <serverdata>
          <mappings>
            <mapping deploy="/home/<USER>/ai_retry" local="$PROJECT_DIR$" />
          </mappings>
          <excludedPaths>
            <excludedPath local="true" path="$PROJECT_DIR$/900010" />
            <excludedPath local="true" path="$PROJECT_DIR$/900020" />
            <excludedPath local="true" path="$PROJECT_DIR$/900020_bk" />
          </excludedPaths>
        </serverdata>
      </paths>
      <paths name="ray@10.0.13.30:22 agent">
        <serverdata>
          <mappings>
            <mapping deploy="/home/<USER>/ai_retry" local="$PROJECT_DIR$" />
          </mappings>
          <excludedPaths>
            <excludedPath local="true" path="$PROJECT_DIR$/900010" />
            <excludedPath local="true" path="$PROJECT_DIR$/900020" />
            <excludedPath local="true" path="$PROJECT_DIR$/900020_bk" />
          </excludedPaths>
        </serverdata>
      </paths>
      <paths name="ray@10.0.13.30:22 agent (2)">
        <serverdata>
          <mappings>
            <mapping deploy="/home/<USER>/ai_retry" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
    </serverData>
    <option name="myAutoUpload" value="ALWAYS" />
  </component>
</project>