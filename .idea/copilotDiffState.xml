<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CopilotDiffPersistence">
    <option name="pendingDiffs">
      <map>
        <entry key="$PROJECT_DIR$/stratergy_runner.py">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/stratergy_runner.py" />
              <option name="originalContent" value="import shutil&#10;from itertools import combinations&#10;import argparse&#10;import ast&#10;import os.path&#10;import pickle&#10;import re&#10;import sys&#10;&#10;import numpy as np&#10;import pandas as pd&#10;import torch&#10;&#10;import util&#10;from alpha_158 import prepare_alpha158_data, combined_with_a300&#10;from data_preparer import prepare_data_for_all_year&#10;from news_scrawler import select_stock&#10;from serialized_stratergy import (&#10;    test_serialized_stratergy_s,&#10;    test_serialized_combined_stratergy_s,&#10;    test_model_stratergy,&#10;    test_combined_model_stratergy,&#10;    test_add_model_assisted_stratergy&#10;)&#10;from stock_recommander import (&#10;    get_return_infos_of_range,&#10;    recommend_for_today,&#10;    get_relative_returns,&#10;    research_alpha&#10;)&#10;from tools.index_assessment import calculate_annual_equal_weight_return&#10;from train_evaluator import *&#10;from util import *&#10;&#10;&#10;def is_assist_model_needed():&#10;    return (config_info['assist_feature_count'] != config_info['feature_count']) or \&#10;        (config_info['period_n'] != config_info['assist_period_n'])&#10;&#10;&#10;def prepare_ic(year):&#10;    config_info['ic_year'] = year&#10;&#10;&#10;def get_model_name(year, prefix=''):&#10;    &quot;&quot;&quot;根据传入的year和prefix生成model_name。&quot;&quot;&quot;&#10;    ret = (&#10;        f'{prefix}{config_info[&quot;feature_count&quot;]}_sel_{config_info[&quot;presets&quot;][0]}_'&#10;        f'{config_info[&quot;train_type&quot;]}_{config_info[&quot;alpha_type&quot;]}_{config_info[&quot;period_n&quot;]}_ends_{year - 1}_t{config_info[&quot;tuning_days&quot;]}'&#10;    )&#10;    return ret if config_info['remove_from_fid'] == 0 else f'{ret}_r{config_info[&quot;remove_from_fid&quot;]}'&#10;&#10;&#10;def get_assist_model_name(year, prefix=''):&#10;    &quot;&quot;&quot;根据传入的year和prefix生成assist_model_name。&quot;&quot;&quot;&#10;    ret = (&#10;        f'{prefix}{config_info[&quot;assist_feature_count&quot;]}_sel_{config_info[&quot;presets&quot;][0]}_'&#10;        f'{config_info[&quot;train_type&quot;]}_{config_info[&quot;alpha_type&quot;]}_{config_info[&quot;assist_period_n&quot;]}_ends_{year - 1}_t{config_info[&quot;tuning_days&quot;]}'&#10;    )&#10;    return ret if config_info['remove_from_fid'] == 0 else f'{ret}_r{config_info[&quot;remove_from_fid&quot;]}'&#10;&#10;&#10;def run_all_sub_model(start_date, end_date, model_name):&#10;    predictor = load_model(model_name)&#10;    sub_names = predictor.model_names()&#10;    rets = {}&#10;    cur_best_return = -1&#10;    for sub_name in sub_names:&#10;        ret,_,_ = test_model_stratergy(config_info['alpha_type'], start_date, end_date, model_name, sub_name)&#10;        rets[sub_name] = ret&#10;        if ret &gt; cur_best_return:&#10;            cur_best = sub_name&#10;            cur_best_return = ret&#10;            logging.info(f'{cur_best} -&gt; {cur_best_return}')&#10;    rets_df = pd.DataFrame(rets, index=[0])&#10;    rets_df.to_csv(f'sub_model_rets_{model_name}.csv', index=False)&#10;&#10;&#10;def run_serialized_stratergy(start_date, end_date, interval=60, prefix=''):&#10;    &quot;&quot;&quot;根据当前的config_info，运行序列化策略并返回收益和统计信息。&quot;&quot;&quot;&#10;    if config_info['fixed_model'] is not None:&#10;        model_name = config_info['fixed_model']&#10;        if config_info['add_info']:&#10;            return test_add_model_assisted_stratergy(config_info['alpha_type'], start_date, end_date, model_name)&#10;        elif is_assist_model_needed():&#10;            return test_combined_model_stratergy(config_info['alpha_type'], start_date, end_date, model_name,&#10;                                                 config_info['assist_model_name'])&#10;        else:&#10;            return test_model_stratergy(config_info['alpha_type'], start_date, end_date, model_name)&#10;    elif (config_info['assist_feature_count'] == config_info['feature_count']) and \&#10;            (config_info['assist_train_type'] == config_info['train_type']):&#10;        return test_serialized_stratergy_s(start_date, end_date, interval, prefix)&#10;    else:&#10;        return test_serialized_combined_stratergy_s(start_date, end_date, interval, prefix, prefix)&#10;&#10;&#10;def run_and_analyze_strategy(start_date, end_date, interval, prefix=''):&#10;    &quot;&quot;&quot;运行策略并获取收益及drawdown分析信息&quot;&quot;&quot;&#10;    ret, stats, _ = run_serialized_stratergy(start_date, end_date, interval, prefix)&#10;    analysis = stats[0].analyzers.drawdown.get_analysis()&#10;    drawdown_len = analysis['len']&#10;    drawdown_max = analysis['max']['drawdown']&#10;    sharpe_ratio = stats[0].analyzers.sharperatio.get_analysis()['sharperatio']&#10;    sortino_ratio = stats[0].analyzers.annualizedsortinoratio.get_analysis()['sortino']&#10;    return ret, drawdown_len, drawdown_max, sharpe_ratio, sortino_ratio&#10;&#10;&#10;def prepare_and_train_model(year, prefix=''):&#10;    &quot;&quot;&quot;在回测前准备IC并训练模型（如果需要）&quot;&quot;&quot;&#10;    prepare_ic(year - 1)&#10;    config_info['model_name'] = config_info['fixed_model'] = get_model_name(year, prefix)&#10;    if util.load_model(config_info['model_name']) is None:&#10;        train(config_info['alpha_type'], config_info['period_n'], f'{year - 1}1231', config_info['model_name'])&#10;&#10;    if is_assist_model_needed():&#10;        config_info['assist_model_name'] = config_info['assist_fixed_model'] = get_assist_model_name(year, prefix)&#10;        if util.load_model(config_info['assist_model_name']) is None:&#10;            train(config_info['alpha_type'], config_info['assist_period_n'], f'{year - 1}1231',&#10;                  config_info['assist_model_name'])&#10;&#10;&#10;def update_returns_csv(df, ret_df, file_name='returns_fc.csv'):&#10;    &quot;&quot;&quot;将新的收益结果合并到csv文件中并保存&quot;&quot;&quot;&#10;    result_df = pd.concat([df, ret_df], ignore_index=True)&#10;    result_df.to_csv(file_name)&#10;&#10;&#10;def notify_results(title, attachment=None):&#10;    &quot;&quot;&quot;使用邮件通知结果&quot;&quot;&quot;&#10;    logging.getLogger().info(title)&#10;    notify_by_email(title)&#10;    if attachment is not None:&#10;        notify_by_email_with_attachment(title, attachment)&#10;&#10;&#10;class YearBacktraderListener:&#10;    def __init__(self, prefix='', interval=250):&#10;        self.prefix = prefix&#10;        self.year_interval = int(interval / 250)&#10;        self.cur_interval = 0&#10;        self.enabled = config_info['fixed_model'] is None&#10;&#10;    def on_pre_year_backtrader(self, year):&#10;        if not self.enabled:&#10;            return&#10;        if self.year_interval &lt; 1:&#10;            prepare_ic(year - 1)&#10;            return&#10;&#10;        if self.cur_interval == 0:&#10;            # 第一次进入这个区间时训练模型&#10;            prepare_and_train_model(year, self.prefix)&#10;        self.cur_interval += 1&#10;        if self.cur_interval &gt;= self.year_interval:&#10;            self.cur_interval = 0&#10;&#10;&#10;def run_current_year_backtrade(prefix=''):&#10;    &quot;&quot;&quot;运行当前年份的回测，按整年进行。&quot;&quot;&quot;&#10;    interval = config_info['interval']&#10;    now = get_last_time()&#10;    listener = YearBacktraderListener(prefix, interval)&#10;    listener.on_pre_year_backtrader(now.year)&#10;&#10;    start_date = f'{now.year}0101'&#10;    end_date = now.strftime('%Y%m%d')&#10;&#10;    ret_cur_year, drawdown_len, drawdown_max, sharpe_ratio, sortino_ratio = run_and_analyze_strategy(start_date,&#10;                                                                                                     end_date, interval,&#10;                                                                                                     prefix)&#10;&#10;    title = (f&quot;{now.year} fc:{config_info['feature_count']}_{config_info['assist_feature_count']} &quot;&#10;             f&quot;interval:{interval} period:{config_info['period_n']} cur_revenue {1} year_revenue {ret_cur_year}&quot;)&#10;    logging.getLogger().info(title)&#10;    return ret_cur_year&#10;&#10;&#10;def get_returns_for_model_series_by_season(prefix=&quot;&quot;):&#10;    &quot;&quot;&quot;获取模型按季度的收益情况（如果需要，可保留或删除）&quot;&quot;&quot;&#10;    file_name = 'returns_fc.csv'&#10;    df = pd.read_csv(file_name, index_col=0) if os.path.exists(file_name) else pd.DataFrame()&#10;    ret_df = pd.Series(dtype=float)&#10;    ret_df['feature_count'] = config_info['feature_count']&#10;&#10;    files = os.listdir('models')&#10;    pattern = re.compile(r'.*?(\d{8})_t0(?:_.*)?_(\d{8})$')&#10;    datelist = []&#10;    for file in files:&#10;        if not file.startswith(prefix):&#10;            continue&#10;        match = pattern.match(file)&#10;        if match:&#10;            _, date_str = match.groups()&#10;            date = datetime.datetime.strptime(date_str, '%Y%m%d')&#10;            datelist.append((date, file))&#10;    sorted_files = sorted(datelist, key=lambda x: x[0])&#10;    if not sorted_files:&#10;        return&#10;&#10;    this_year = sorted_files[0][0].year&#10;    total_ret = 1&#10;    this_year_ret = 1&#10;    mail_msg = ''&#10;    holding_days = int(config_info['topn'] / config_info['max_sell'])&#10;    for i in range(0, len(sorted_files)):&#10;        model_path = sorted_files[i][1]&#10;        start_date = sorted_files[i][0] + datetime.timedelta(days=1)&#10;        prepare_ic(start_date.year - 1)&#10;        end_date = sorted_files[i + 1][0] if (i + 1) &lt; len(sorted_files) else datetime.datetime.today()&#10;        ret = get_relative_returns(start_date, end_date, model_path, config_info['topn'])&#10;        cur_ret = ret.prod() ** (1 / holding_days)&#10;        total_ret *= cur_ret&#10;        if start_date.year == this_year:&#10;            this_year_ret *= cur_ret&#10;        else:&#10;            msg = f'{this_year} ret is {this_year_ret}'&#10;            ret_df[str(this_year)] = this_year_ret&#10;            mail_msg += msg + '\n'&#10;            logging.info(msg)&#10;            this_year = start_date.year&#10;            this_year_ret = cur_ret&#10;&#10;    msg = f'{this_year} ret is {this_year_ret}'&#10;    mail_msg += msg + '\n'&#10;    ret_df[str(this_year)] = this_year_ret&#10;    msg = f'total_ret is {total_ret}'&#10;    mail_msg += msg + '\n'&#10;    logging.info(msg)&#10;    ret_df['total'] = total_ret&#10;    ret_df = pd.DataFrame(ret_df).T&#10;    update_returns_csv(df, ret_df, file_name='returns_fc.csv')&#10;    if config_info['notify_by_mail']:&#10;        notify_by_email_with_attachment(f'get_returns_{config_info[&quot;feature_count&quot;]}_{config_info[&quot;interval&quot;]}',&#10;                                        'returns_fc.csv')&#10;&#10;&#10;def get_returns_for_model_series_by_year(start_year=2020, end_year=2025, prefix=&quot;&quot;, select_alpha=None):&#10;    &quot;&quot;&quot;获取模型按年度的收益情况，按整年进行回测。&quot;&quot;&quot;&#10;    file_name = 'returns_fc.csv'&#10;    df = pd.read_csv(file_name, index_col=0) if os.path.exists(file_name) else pd.DataFrame()&#10;    ret_df = pd.Series(dtype=float)&#10;&#10;    def skip_year_fn(y):&#10;        return (len(config_info['selected_year']) &gt; 0) and (str(y) not in config_info['selected_year'])&#10;&#10;    if select_alpha is not None:&#10;        if select_alpha.startswith('_'):&#10;            ret_df['feature_count'] = f'{config_info[&quot;feature_count&quot;]}_-{select_alpha}'&#10;        else:&#10;            ret_df['feature_count'] = f'{config_info[&quot;feature_count&quot;]}_{select_alpha}'&#10;    else:&#10;        ret_df['feature_count'] = config_info['feature_count']&#10;    holding_days = int(config_info['topn'] / config_info['max_sell'])&#10;    if holding_days &gt; 1:&#10;        ret_df['feature_count'] = f'{ret_df[&quot;feature_count&quot;]}_h{holding_days}'&#10;    rets = []&#10;    today = datetime.datetime.today()&#10;    for year in range(start_year, end_year + 1):&#10;        if skip_year_fn(year):&#10;            continue&#10;        if config_info['fixed_model'] is None:&#10;            prepare_and_train_model(year, prefix)&#10;&#10;        end_date = f'{year}1231' if year != today.year else today.strftime('%Y%m%d')&#10;        ret = get_relative_returns(f'{year}0101', end_date,&#10;                                   config_info['fixed_model'] if config_info['fixed_model'] is not None else&#10;                                   config_info['model_name'], config_info['topn'],&#10;                                   selected_alpha=select_alpha)&#10;        cur_ret = ret.prod() ** (1 / holding_days)&#10;        rets.append(cur_ret)&#10;        logging.info(f'{year} ret is {cur_ret}')&#10;        ret_df[str(year)] = cur_ret&#10;&#10;    total_ret = 1&#10;    for r in rets:&#10;        total_ret *= r&#10;    logging.info(f'total ret is {total_ret}')&#10;    ret_df['total'] = total_ret&#10;    ret_df = pd.DataFrame(ret_df).T&#10;    update_returns_csv(df, ret_df, file_name='returns_fc.csv')&#10;    if config_info['notify_by_mail']:&#10;        notify_by_email_with_attachment(f'get_returns_{config_info[&quot;feature_count&quot;]}_{config_info[&quot;interval&quot;]}',&#10;                                        'returns_fc.csv')&#10;    return rets, total_ret&#10;&#10;&#10;def analysis_selected_stocks_by_dates(start, end, model_name, topn=5):&#10;    &quot;&quot;&quot;分析选定日期范围内的股票收益。&quot;&quot;&quot;&#10;    ret = get_return_infos_of_range(start, end, model_name, topn=topn)&#10;    return ret, ret.describe()&#10;&#10;&#10;def analysis_gpt_stocks_by_dates(start, end, model_name, topn=10):&#10;    &quot;&quot;&quot;使用GPT分析选定日期范围内的股票收益。&quot;&quot;&quot;&#10;    data, _ = analysis_selected_stocks_by_dates(start, end, model_name, topn=topn)&#10;    ret = pd.DataFrame()&#10;    for date in data.index.get_level_values(0).unique():&#10;        data_in_date = data.loc[data.index.get_level_values(0) == date]&#10;        codes = data_in_date.index.get_level_values(1).unique()&#10;        selected_codes = select_stock(codes, date + pd.DateOffset(days=1))&#10;        selected = data_in_date.loc[data_in_date.index.get_level_values(1).isin(selected_codes)]&#10;        ret = pd.concat([ret, selected], axis=0)&#10;    return ret, ret.describe()&#10;&#10;&#10;def print_ret_range_info(ret):&#10;    &quot;&quot;&quot;打印收益范围的详细信息。&quot;&quot;&quot;&#10;    print('---------------return describe --------------------')&#10;    print(ret.describe())&#10;    print('---------------return rate statics--------------------')&#10;    print(ret.groupby('date').mean().mean())&#10;&#10;&#10;def modify_model_name(model_name: str):&#10;    &quot;&quot;&quot;根据市场修改model_name。&quot;&quot;&quot;&#10;    market = configs.convert_to_csi_code(config_info['market'])&#10;    if market == 'csi300':&#10;        return model_name&#10;    else:&#10;        index = model_name.rfind('_')&#10;        return model_name[:index] + '_' + market + model_name[index:]&#10;&#10;&#10;def seed_everything(seed):&#10;    &quot;&quot;&quot;设置随机种子以确保结果可复现。&quot;&quot;&quot;&#10;    random.seed(seed)&#10;    os.environ['PYTHONHASHSEED'] = str(seed)&#10;    np.random.seed(seed)&#10;    torch.manual_seed(seed)&#10;    torch.cuda.manual_seed(seed)&#10;    torch.cuda.manual_seed_all(seed)&#10;    torch.backends.cudnn.deterministic = True&#10;    torch.backends.cudnn.benchmark = False&#10;    if not config_info['cpu']:&#10;        torch.use_deterministic_algorithms(True)&#10;&#10;&#10;def draw_weights_vs_returns(start_year, end_year):&#10;    &quot;&quot;&quot;绘制weight列随return列变化的散点图&quot;&quot;&quot;&#10;    import matplotlib.pyplot as plt&#10;    import seaborn as sns&#10;&#10;    # 设置中文字体&#10;    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']&#10;    plt.rcParams['axes.unicode_minus'] = False&#10;&#10;    print(f&quot;正在加载 {start_year} 到 {end_year} 年的股票数据...&quot;)&#10;&#10;    # 加载股票数据&#10;    df = util.load_stock_data(config_info['alpha_type'], config_info['market'])&#10;    factors = util.get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'])&#10;&#10;    all_data = []&#10;&#10;    for year in range(start_year, end_year + 1):&#10;        print(f&quot;处理 {year} 年数据...&quot;)&#10;        start_date = f'{year}0101'&#10;        end_date = f'{year}1231'&#10;&#10;        # 调用prepare_data_for_model&#10;        year_data = util.prepare_data_for_model(df, factors, config_info['period_n'], start_date, end_date)&#10;&#10;        if 'weight' in year_data.columns and 'return' in year_data.columns:&#10;            # 只保留有效数据&#10;            valid_data = year_data[['weight', 'return']].dropna()&#10;            if not valid_data.empty:&#10;                valid_data['year'] = year&#10;                all_data.append(valid_data)&#10;                print(f&quot;  {year} 年有效数据点: {len(valid_data)}&quot;)&#10;        else:&#10;            print(f&quot;  {year} 年数据中缺少 weight 或 return 列&quot;)&#10;&#10;    if not all_data:&#10;        print(&quot;没有找到包含 weight 和 return 列的有效数据&quot;)&#10;        return&#10;&#10;    # 合并所有年份的数据&#10;    combined_data = pd.concat(all_data, ignore_index=True)&#10;    print(f&quot;总共有效数据点: {len(combined_data)}&quot;)&#10;&#10;    # 创建图表&#10;    plt.figure(figsize=(12, 8))&#10;&#10;    # 绘制散点图&#10;    scatter = plt.scatter(combined_data['return'], combined_data['weight'],&#10;                         c=combined_data['year'], cmap='viridis', alpha=0.6, s=20)&#10;&#10;    # 添加颜色条&#10;    cbar = plt.colorbar(scatter)&#10;    cbar.set_label('年份', fontsize=12)&#10;&#10;    # 计算相关系数&#10;    correlation = combined_data['return'].corr(combined_data['weight'])&#10;&#10;    # 添加趋势线&#10;    z = np.polyfit(combined_data['return'], combined_data['weight'], 1)&#10;    p = np.poly1d(z)&#10;    plt.plot(combined_data['return'], p(combined_data['return']), &quot;r--&quot;, alpha=0.8, linewidth=2)&#10;&#10;    # 设置标签和标题&#10;    plt.xlabel('Return', fontsize=12)&#10;    plt.ylabel('Weight', fontsize=12)&#10;    plt.title(f'Weight vs Return 散点图 ({start_year}-{end_year})\n相关系数: {correlation:.4f}', fontsize=14)&#10;    plt.grid(True, alpha=0.3)&#10;&#10;    # 保存图片&#10;    filename = f'weight_vs_return_{start_year}_{end_year}.png'&#10;    plt.savefig(filename, dpi=300, bbox_inches='tight')&#10;    print(f&quot;图表已保存为: {filename}&quot;)&#10;&#10;    # 显示统计信息&#10;    print(f&quot;\n统计信息:&quot;)&#10;    print(f&quot;Return 范围: {combined_data['return'].min():.6f} 到 {combined_data['return'].max():.6f}&quot;)&#10;    print(f&quot;Weight 范围: {combined_data['weight'].min():.6f} 到 {combined_data['weight'].max():.6f}&quot;)&#10;    print(f&quot;相关系数: {correlation:.6f}&quot;)&#10;&#10;    # 按年份显示统计&#10;    print(f&quot;\n按年份统计:&quot;)&#10;    for year in range(start_year, end_year + 1):&#10;        year_data = combined_data[combined_data['year'] == year]&#10;        if not year_data.empty:&#10;            year_corr = year_data['return'].corr(year_data['weight'])&#10;            print(f&quot;  {year}: 数据点 {len(year_data)}, 相关系数 {year_corr:.6f}&quot;)&#10;&#10;    # 按weights分为10等段统计&#10;    print(f&quot;\n按weights分段统计 (10等段):&quot;)&#10;    print(&quot;=&quot; * 80)&#10;&#10;    # 按weights排序&#10;    sorted_data = combined_data.sort_values('weight')&#10;&#10;    # 分为10等段&#10;    n_segments = 10&#10;    segment_size = len(sorted_data) // n_segments&#10;&#10;    segment_stats = []&#10;&#10;    for i in range(n_segments):&#10;        start_idx = i * segment_size&#10;        if i == n_segments - 1:  # 最后一段包含所有剩余数据&#10;            end_idx = len(sorted_data)&#10;        else:&#10;            end_idx = (i + 1) * segment_size&#10;&#10;        segment_data = sorted_data.iloc[start_idx:end_idx]&#10;&#10;        if not segment_data.empty:&#10;            # 计算return绝对值的统计信息&#10;            abs_returns = segment_data['return'].abs()&#10;&#10;            stats = {&#10;                'segment': i + 1,&#10;                'count': len(segment_data),&#10;                'weight_min': segment_data['weight'].min(),&#10;                'weight_max': segment_data['weight'].max(),&#10;                'weight_mean': segment_data['weight'].mean(),&#10;                'return_abs_mean': abs_returns.mean(),&#10;                'return_abs_var': abs_returns.var(),&#10;                'return_abs_std': abs_returns.std(),&#10;                'return_mean': segment_data['return'].mean(),&#10;                'return_std': segment_data['return'].std()&#10;            }&#10;&#10;            segment_stats.append(stats)&#10;&#10;            print(f&quot;第 {i+1:2d} 段: 数据点 {len(segment_data):6d} | &quot;&#10;                  f&quot;Weight范围 [{stats['weight_min']:8.6f}, {stats['weight_max']:8.6f}] | &quot;&#10;                  f&quot;Weight均值 {stats['weight_mean']:8.6f}&quot;)&#10;            print(f&quot;        |Return|均值: {stats['return_abs_mean']:8.6f} | &quot;&#10;                  f&quot;|Return|方差: {stats['return_abs_var']:8.6f} | &quot;&#10;                  f&quot;|Return|标准差: {stats['return_abs_std']:8.6f}&quot;)&#10;            print(f&quot;        Return均值: {stats['return_mean']:8.6f} | &quot;&#10;                  f&quot;Return标准差: {stats['return_std']:8.6f}&quot;)&#10;            print(&quot;-&quot; * 80)&#10;&#10;    # 创建分段统计的可视化&#10;    if segment_stats:&#10;        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))&#10;&#10;        segments = [s['segment'] for s in segment_stats]&#10;        weight_means = [s['weight_mean'] for s in segment_stats]&#10;        return_abs_means = [s['return_abs_mean'] for s in segment_stats]&#10;        return_abs_vars = [s['return_abs_var'] for s in segment_stats]&#10;        return_means = [s['return_mean'] for s in segment_stats]&#10;&#10;        # 子图1: Weight均值 vs 段数&#10;        ax1.bar(segments, weight_means, alpha=0.7, color='skyblue')&#10;        ax1.set_xlabel('分段')&#10;        ax1.set_ylabel('Weight均值')&#10;        ax1.set_title('各段Weight均值')&#10;        ax1.grid(True, alpha=0.3)&#10;&#10;        # 子图2: |Return|均值 vs 段数&#10;        ax2.bar(segments, return_abs_means, alpha=0.7, color='lightcoral')&#10;        ax2.set_xlabel('分段')&#10;        ax2.set_ylabel('|Return|均值')&#10;        ax2.set_title('各段|Return|均值')&#10;        ax2.grid(True, alpha=0.3)&#10;&#10;        # 子图3: |Return|方差 vs 段数&#10;        ax3.bar(segments, return_abs_vars, alpha=0.7, color='lightgreen')&#10;        ax3.set_xlabel('分段')&#10;        ax3.set_ylabel('|Return|方差')&#10;        ax3.set_title('各段|Return|方差')&#10;        ax3.grid(True, alpha=0.3)&#10;&#10;        # 子图4: Weight均值 vs |Return|均值&#10;        ax4.scatter(weight_means, return_abs_means, s=100, alpha=0.7, color='purple')&#10;        for i, (x, y) in enumerate(zip(weight_means, return_abs_means)):&#10;            ax4.annotate(f'{i+1}', (x, y), xytext=(5, 5), textcoords='offset points')&#10;&#10;        # 添加趋势线&#10;        if len(weight_means) &gt; 1:&#10;            z = np.polyfit(weight_means, return_abs_means, 1)&#10;            p = np.poly1d(z)&#10;            ax4.plot(weight_means, p(weight_means), &quot;r--&quot;, alpha=0.8)&#10;&#10;            # 计算相关系数&#10;            segment_corr = np.corrcoef(weight_means, return_abs_means)[0, 1]&#10;            ax4.set_title(f'Weight均值 vs |Return|均值\n(相关系数: {segment_corr:.4f})')&#10;        else:&#10;            ax4.set_title('Weight均值 vs |Return|均值')&#10;&#10;        ax4.set_xlabel('Weight均值')&#10;        ax4.set_ylabel('|Return|均值')&#10;        ax4.grid(True, alpha=0.3)&#10;&#10;        plt.tight_layout()&#10;&#10;        # 保存分段统计图&#10;        segment_filename = f'weight_segments_analysis_{start_year}_{end_year}.png'&#10;        plt.savefig(segment_filename, dpi=300, bbox_inches='tight')&#10;        print(f&quot;\n分段统计图已保存为: {segment_filename}&quot;)&#10;&#10;        # 保存统计数据到CSV&#10;        segment_df = pd.DataFrame(segment_stats)&#10;        csv_filename = f'weight_segments_stats_{start_year}_{end_year}.csv'&#10;        segment_df.to_csv(csv_filename, index=False)&#10;        print(f&quot;分段统计数据已保存为: {csv_filename}&quot;)&#10;&#10;        plt.show()&#10;&#10;    plt.show()&#10;&#10;def find_best_random_trained_model(prefix=&quot;&quot;, start_year=2018, end_year=None):&#10;    for year in range(start_year, end_year + 1):&#10;        max_ret = -1&#10;        max_model_name = None&#10;        for train_irr in range(0, 10):&#10;            model_name = get_model_name(year, prefix=f'{prefix}_{train_irr}_')&#10;            config_info['train_seed'] = train_irr&#10;            if load_model(model_name) is None:&#10;                train(config_info['alpha_type'], config_info['period_n'], f'{year - 1}1231', model_name)&#10;            pre_test_year = year - 1&#10;            ret = get_relative_returns(f'{pre_test_year}0101', f'{pre_test_year}1231', model_name, config_info['topn'],&#10;                                       tuning_sec_only=True)&#10;            holding_days = int(config_info['topn'] / config_info['max_sell'])&#10;            cur_ret = ret.prod() ** (1 / holding_days)&#10;            if cur_ret &gt; max_ret:&#10;                max_ret = cur_ret&#10;                max_model_name = model_name&#10;                logging.info(f'select {train_irr} as best model')&#10;        shutil.copytree(f'models/{max_model_name}', f'models/{get_model_name(year, prefix)}')&#10;&#10;&#10;def current_best_backtrade(market=None, prefix=&quot;&quot;, start_year=2018, save_path='fc_res.csv',&#10;                           end_year=None):&#10;    &quot;&quot;&quot;运行当前最佳策略的回测，按整年进行。&quot;&quot;&quot;&#10;    interval = config_info['interval']&#10;    if end_year is None:&#10;        end_year = datetime.date.today().year&#10;    if market is None:&#10;        market = config_info['market']&#10;&#10;    min_res = calculate_annual_equal_weight_return(configs.convert_to_csi_code(market), start_year, end_year)&#10;    print('基准收益率:', min_res)&#10;    min_res = {k: max(v + 1, 1) for k, v in min_res.items()}&#10;    if config_info['enable_min_res']:&#10;        if isinstance(config_info['enable_min_res'], str):&#10;            min_res = ast.literal_eval(config_info['enable_min_res'])&#10;&#10;&#10;    if os.path.exists(save_path):&#10;        df = pd.read_csv(save_path, index_col=0)&#10;    else:&#10;        df = pd.DataFrame()&#10;&#10;    trade_file = f'trade_{config_info[&quot;feature_count&quot;]}.csv'&#10;    if os.path.exists(trade_file) and not config_info['use_rank_model'] and config_info['trade_log']:&#10;        os.remove(trade_file)&#10;&#10;    init_qlib()&#10;    config_info['market'] = market&#10;    ret = 1&#10;    now = get_last_time()&#10;    ret_df = pd.Series(dtype=float)&#10;&#10;    # 设置策略名称&#10;    if config_info['fixed_model'] is not None:&#10;        ret_df['name'] = config_info['fixed_model']&#10;    elif config_info['assist_feature_count'] != config_info['feature_count']:&#10;        ret_df['name'] = f'{prefix}{market}_combine_{config_info[&quot;feature_count&quot;]}'&#10;    else:&#10;        ret_df['name'] = f'{prefix}{market}_{config_info[&quot;feature_count&quot;]}'&#10;    if config_info['add_info']:&#10;        ret_df['name'] += '_addinfo'&#10;    if config_info['added_alpha_for_stratergy'] is not None:&#10;        ret_df['name'] += f&quot;_{config_info['added_alpha_for_stratergy']}&quot;&#10;    ret_df['name'] += f'_p{config_info[&quot;period_n&quot;]}_top_{config_info[&quot;topn&quot;]}'&#10;    if config_info['cpu']:&#10;        ret_df['name'] += '_cpu'&#10;&#10;    listener = YearBacktraderListener(prefix, interval)&#10;&#10;    # 处理所有年份回测&#10;    years_to_test = config_info['selected_year'] if len(config_info['selected_year']) &gt; 0 else range(end_year,&#10;                                                                                                     start_year - 1, -1)&#10;    years_to_test = [y for y in years_to_test if int(y) in range(end_year, start_year - 1, -1)]&#10;    for test_year in years_to_test:&#10;        test_year = int(test_year)&#10;        # 当年特殊处理&#10;        if test_year == now.year:&#10;            start_date = f'{test_year}0101'&#10;            end_date = now.strftime('%Y%m%d')&#10;        else:&#10;            start_date = f'{test_year}0101'&#10;            end_date = f'{test_year}1231'&#10;&#10;        listener.on_pre_year_backtrader(test_year)&#10;        ret_cur_year, drawdown_len, drawdown_max, sharpe_ratio, sortino_ratio = run_and_analyze_strategy(start_date,&#10;                                                                                                         end_date,&#10;                                                                                                         interval,&#10;                                                                                                         prefix)&#10;&#10;        ret *= ret_cur_year&#10;        ret_df[str(test_year)] = ret_cur_year&#10;        ret_df[str(test_year) + '_drawdown_len'] = drawdown_len&#10;        ret_df[str(test_year) + '_drawdown_max'] = drawdown_max&#10;        ret_df[str(test_year) + '_sharpe_ratio'] = sharpe_ratio&#10;        ret_df[str(test_year) + '_sortino_ratio'] = sortino_ratio&#10;&#10;        # 当年需要发送更详细的通知&#10;        if test_year == now.year:&#10;            title = (f&quot;{test_year} fc:{config_info['feature_count']}_{config_info['assist_feature_count']} &quot;&#10;                     f&quot;interval:{interval} period:{config_info['period_n']} total_revenue {ret} &quot;&#10;                     f&quot;year_revenue {ret_cur_year}&quot;)&#10;        else:&#10;            title = f'{test_year} interval {interval} total_revenue {ret} year_revenue {ret_cur_year}'&#10;        notify_results(title, attachment=None)&#10;&#10;        if config_info['enable_min_res'] and ret_cur_year &lt; min_res.get(test_year, 0):&#10;            break&#10;&#10;    title = (f&quot;fc:{config_info['feature_count']}_{config_info['assist_feature_count']} &quot;&#10;             f&quot;interval:{interval} period:{config_info['period_n']} total_revenue {ret}&quot;)&#10;    notify_results(title)&#10;&#10;    ret_df['total'] = ret&#10;    ret_df['feature_count'] = config_info['feature_count']&#10;    ret_df['drawdown_len'] = ret_df.filter(like='_drawdown_len').sum()&#10;    ret_df['drawdown_max'] = ret_df.filter(like='_drawdown_max').max()&#10;    ret_df['sharpe_ratio'] = ret_df.filter(like='_sharpe_ratio').mean()&#10;    ret_df['sortino_ratio'] = ret_df.filter(like='_sortino_ratio').mean()&#10;    ret_df = pd.DataFrame(ret_df).T&#10;    pd.concat([df, ret_df], ignore_index=True).to_csv(save_path)&#10;    if config_info['notify_by_mail']:&#10;        dump_fc_res(f'{save_path}_{config_info[&quot;feature_count&quot;]}', save_path)&#10;    return ret, ret_df&#10;&#10;&#10;def dump_fc_res(title, fc_res_path):&#10;    df = pd.read_csv(fc_res_path, index_col=0)&#10;    last_total = df.iloc[-1]['total'] if 'total' in df.columns else None&#10;    last_name = df.iloc[-1]['name'] if 'name' in df.columns else None&#10;&#10;    max_total_row = df.loc[df['total'].idxmax()] if 'total' in df.columns else None&#10;    max_total = max_total_row['total'] if max_total_row is not None else None&#10;    max_total_name = max_total_row['name'] if max_total_row is not None else None&#10;&#10;    msg = f&quot;Last: {last_total}, Name: {last_name}&quot;&#10;    msg += f&quot;\nMax : {max_total}, Name: {max_total_name}&quot;&#10;&#10;    send_email_attachment(title, config_info['mail'], msg, fc_res_path)&#10;&#10;&#10;def get_feature_names_from_fc_res(fc_res_path):&#10;    fc_res_file = pd.read_csv(fc_res_path)&#10;    feature_names = []&#10;    for name in fc_res_file['name']:&#10;        parts = name.split('_')&#10;        stock_code_idx = -1&#10;        for i, part in enumerate(parts):&#10;            if len(part) == 6 and part.isdigit():&#10;                stock_code_idx = i&#10;                break&#10;&#10;        feature_name = parts[stock_code_idx + 2]&#10;        if len(feature_name) == 0:&#10;            feature_name = f'_{parts[stock_code_idx + 3]}'&#10;        feature_names.append(feature_name)&#10;    return feature_names&#10;&#10;&#10;def save_ensemble_results_to_csv(ensemble_results, file_name='ensemble_results.csv'):&#10;    &quot;&quot;&quot;&#10;    将模型组合的测试结果保存到CSV文件中&#10;&#10;    Args:&#10;        ensemble_results: 包含模型组合测试结果的列表，每个元素是(ensemble_name, return, model_combo)的元组&#10;        file_name: 保存结果的CSV文件名&#10;    &quot;&quot;&quot;&#10;    # 转换ensemble_results为DataFrame&#10;    result_data = []&#10;    for name, ret, models in ensemble_results:&#10;        result_data.append({&#10;            'ensemble_name': name,&#10;            'return': ret,&#10;            'model_combo': str(models),&#10;            'timestamp': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')&#10;        })&#10;&#10;    result_df = pd.DataFrame(result_data)&#10;&#10;    result_df.to_csv(file_name, index=False)&#10;&#10;&#10;def train_all_and_ensemble(alpha_type, period, train_end_date, model_name):&#10;    &quot;&quot;&quot;使用ALL_HYPERPARAMETERS中的所有参数训练模型，然后创建三三组合的模型集成&quot;&quot;&quot;&#10;    logging.getLogger().info(f&quot;开始使用所有超参数训练模型 {model_name}&quot;)&#10;&#10;    # 加载数据&#10;    df = load_stock_data(alpha_type, config_info['market'])&#10;    factors = get_feature_names(alpha_type, config_info['market'], period)&#10;&#10;    # 准备训练数据&#10;    train_end = pd.to_datetime(train_end_date)&#10;    train_start = train_end - pd.DateOffset(years=config_info['train_year'])&#10;    train_start = train_start.replace(month=1, day=1)&#10;&#10;    # 检查是否有足够的模型&#10;    predictor = load_model(model_name)&#10;    if predictor is None:&#10;        logging.getLogger().info(f&quot;没有找到模型 {model_name}，开始训练所有超参数的模型&quot;)&#10;        predictor = train_model(df, factors, period, train_start, train_end, model_name)&#10;&#10;    if predictor is None or not predictor.is_fit:&#10;        logging.getLogger().error(f&quot;模型 {model_name} 训练失败&quot;)&#10;        return None, None&#10;&#10;    # 获取所有L1模型名称&#10;    all_models = predictor.model_names()&#10;    l1_models = [m for m in all_models if 'WeightedEnsemble' not in m]&#10;&#10;    logging.getLogger().info(f&quot;找到 {len(l1_models)} 个L1模型，开始创建三三组合模型&quot;)&#10;&#10;    # 获取测试数据范围&#10;    test_start_date = train_end + pd.DateOffset(days=1)&#10;    test_end_date = test_start_date + pd.DateOffset(days=365)&#10;&#10;    # 用于保存结果的数据结构&#10;    ensemble_results = []&#10;&#10;    all_combos = []&#10;    all_combos.extend(combinations(l1_models, 3))&#10;    for model_combo in all_combos:&#10;        ensemble_name = f&quot;Ensemble_{'_'.join(model_combo)}&quot;&#10;        logging.getLogger().info(f&quot;测试模型组合: {ensemble_name}&quot;)&#10;        if f'WeightedEnsemble_L2{ensemble_name}' not in all_models:&#10;            predictor.fit_weighted_ensemble(&#10;                base_models=list(model_combo),&#10;                name_suffix=ensemble_name)&#10;&#10;        try:&#10;            ret, stats, _ = test_model_stratergy(alpha_type,&#10;                                                 test_start_date.strftime('%Y%m%d'),&#10;                                                 test_end_date.strftime('%Y%m%d'),&#10;                                                 model_name, f'WeightedEnsemble_L2{ensemble_name}')&#10;            ensemble_results.append((ensemble_name, ret, model_combo))&#10;            logging.getLogger().info(f&quot;模型组合 {ensemble_name} 回报率: {ret}&quot;)&#10;            save_ensemble_results_to_csv(ensemble_results)&#10;        except Exception as e:&#10;            logging.getLogger().error(f&quot;测试模型组合 {ensemble_name} 失败: {str(e)}&quot;)&#10;&#10;    # 找出最佳组合&#10;    if ensemble_results:&#10;        best_ensemble = max(ensemble_results, key=lambda x: x[1])&#10;        logging.getLogger().info(f&quot;最佳模型组合: {best_ensemble[0]}&quot;)&#10;        logging.getLogger().info(f&quot;回报率: {best_ensemble[1]}&quot;)&#10;        logging.getLogger().info(f&quot;组成模型: {best_ensemble[2]}&quot;)&#10;        # 将最佳组合保存为单独的模型&#10;        best_model_path = f&quot;{model_name}_best_{best_ensemble[0]}&quot;&#10;        return best_model_path, best_ensemble&#10;    else:&#10;        logging.getLogger().error(&quot;没有成功测试任何模型组合&quot;)&#10;        return None, None&#10;&#10;&#10;def find_best_model_combination(year):&#10;    logging.getLogger().info(&quot;开始测试模型组合&quot;)&#10;&#10;    original_models_to_use = config_info['models_to_use'].copy() if isinstance(config_info['models_to_use'], list) else \&#10;        config_info['models_to_use'].split(',')&#10;    original_gbm_to_use = config_info['gbm_to_use'].copy() if isinstance(config_info['gbm_to_use'], list) else [int(x)&#10;                                                                                                                for x in&#10;                                                                                                                config_info[&#10;                                                                                                                    'gbm_to_use'].split(&#10;                                                                                                                    ',')]&#10;&#10;    all_model_options = []&#10;&#10;    if 'GBM' in original_models_to_use:&#10;        for gbm_version in original_gbm_to_use:&#10;            all_model_options.append(f'GBM{gbm_version}')&#10;&#10;    for model in original_models_to_use:&#10;        if model != 'GBM':&#10;            all_model_options.append(model)&#10;&#10;    logging.getLogger().info(f&quot;可用模型选项: {all_model_options}&quot;)&#10;&#10;    combinations_results = []&#10;    env_file = os.path.join(os.getcwd(), '.env')&#10;    backup_env(env_file)&#10;&#10;    all_combos = []&#10;    all_combos.extend(combinations(all_model_options, 3))&#10;    for combo in all_combos:&#10;        current_models = []&#10;        current_gbm_versions = []&#10;&#10;        for model in combo:&#10;            if model.startswith('GBM'):&#10;                current_models.append('GBM')&#10;                if len(model) &gt; 3:  # 如果有版本号&#10;                    current_gbm_versions.append(int(model[3:]))&#10;            else:&#10;                current_models.append(model)&#10;&#10;        current_models = list(set(current_models))&#10;        current_gbm_versions = list(set(current_gbm_versions))&#10;&#10;        config_info['models_to_use'] = current_models&#10;        if current_gbm_versions:&#10;            config_info['gbm_to_use'] = current_gbm_versions&#10;&#10;        write_env(current_gbm_versions, current_models, env_file)&#10;&#10;        prefix = &quot;_&quot;.join(combo)&#10;        prefix = f&quot;{prefix}_&quot;&#10;&#10;        logging.getLogger().info(f&quot;测试模型组合: {prefix}&quot;)&#10;        logging.getLogger().info(f&quot;当前模型设置: models_to_use={current_models}, gbm_to_use={current_gbm_versions}&quot;)&#10;&#10;        try:&#10;&#10;            ret, _ = current_best_backtrade(config_info['market'], prefix, start_year=year, end_year=year)&#10;            config_info['fixed_model'] = None&#10;            combo_result = (&#10;                prefix,&#10;                ret,&#10;                {&#10;                    'models': current_models,&#10;                    'gbm_versions': current_gbm_versions,&#10;                }&#10;            )&#10;            combinations_results.append(combo_result)&#10;&#10;            save_ensemble_results_to_csv(combinations_results, file_name='model_combinations_results.csv')&#10;&#10;            logging.getLogger().info(f&quot;组合 {prefix} 测试完成: 收益率={ret}&quot;)&#10;        except Exception as e:&#10;            logging.getLogger().error(f&quot;测试组合 {prefix} 时出错: {str(e)}&quot;)&#10;&#10;    config_info['models_to_use'] = original_models_to_use&#10;    config_info['gbm_to_use'] = original_gbm_to_use&#10;&#10;    restore_env(env_file)&#10;&#10;    if combinations_results:&#10;        best_combo = max(combinations_results, key=lambda x: x[1])&#10;        logging.getLogger().info(f&quot;最佳模型组合: {best_combo[0]}&quot;)&#10;        logging.getLogger().info(f&quot;最佳收益率: {best_combo[1]}&quot;)&#10;        logging.getLogger().info(f&quot;最佳组合配置: {best_combo[2]}&quot;)&#10;&#10;        config_info['models_to_use'] = best_combo[2]['models']&#10;        config_info['gbm_to_use'] = best_combo[2]['gbm_versions']&#10;&#10;        return best_combo&#10;    else:&#10;        logging.getLogger().error(&quot;没有成功测试任何模型组合&quot;)&#10;        return None&#10;&#10;&#10;def restore_env(env_file):&#10;    if os.path.exists(env_file):&#10;        os.remove(env_file)&#10;    backup_env_file = os.path.join(os.getcwd(), '.env.bak')&#10;    if os.path.exists(backup_env_file):&#10;        os.rename(backup_env_file, env_file)&#10;&#10;&#10;def backup_env(env_file):&#10;    if os.path.exists(env_file):&#10;        backup_env_file = os.path.join(os.getcwd(), '.env.bak')&#10;        with open(env_file, 'r') as f:&#10;            env_content = f.read()&#10;        with open(backup_env_file, 'w') as f:&#10;            f.write(env_content)&#10;&#10;&#10;def write_env(current_gbm_versions, current_models, env_file):&#10;    env_vars = {}&#10;    if os.path.exists(env_file):&#10;        with open(env_file, 'r') as f:&#10;            for line in f:&#10;                key, _, value = line.strip().partition('=')&#10;                env_vars[key] = value&#10;    # 更新或添加新的值&#10;    env_vars['models_to_use'] = ','.join(current_models)&#10;    env_vars['gbm_to_use'] = ','.join(map(str, current_gbm_versions))&#10;    # 写回 .env 文件&#10;    with open(env_file, 'w') as f:&#10;        for key, value in env_vars.items():&#10;            f.write(f&quot;{key}={value}\n&quot;)&#10;&#10;&#10;def strategy_runner_main():&#10;    &quot;&quot;&quot;主策略运行函数，解析参数并执行相应操作。&quot;&quot;&quot;&#10;    seed_everything(0)&#10;    parser = argparse.ArgumentParser()&#10;    parser.add_argument('-c', '--feature_count', type=int, help='feature count')&#10;    parser.add_argument('--afc', type=int, help='assist feature count')&#10;    parser.add_argument('-t', '--today', action='store_true', help='find stock for today')&#10;    parser.add_argument('-r', '--run_current_best', action='store_true', help='run current best strategy')&#10;    parser.add_argument('--run_all_sub_model', action='store_true', help='run all sub model')&#10;&#10;    parser.add_argument('--run_select_alpha', action='store_true', help='select alpha')&#10;    parser.add_argument('--get_returns', action='store_true', help='get_returns')&#10;    parser.add_argument('--evaluate_alpha', action='store_true', help='evaluate_alpha')&#10;    parser.add_argument('--run_cur_year', action='store_true', help='run cur year strategy')&#10;    parser.add_argument('--prepare_model', action='store_true', help='prepare model')&#10;    parser.add_argument('-i', '--compute_ic_table', action='store_true', help='compute ic table')&#10;    parser.add_argument('-g', '--generate_all', type=str, help='generate all data')&#10;    parser.add_argument('-o', '--hyperparameter_tune_off', action='store_true', help='hyperparameter_tune_off')&#10;    parser.add_argument('--cpu', action='store_true', help='cpu mode')&#10;    parser.add_argument('--rec', type=int, help='recommend mode')&#10;    parser.add_argument('--dis_weights421', action='store_true', help='disable weights 421')&#10;    parser.add_argument('-q', '--quality', type=str, help='quality')&#10;    parser.add_argument('-y', '--year', type=int, help='start year')&#10;    parser.add_argument('--end_year', type=int, help='end year')&#10;    parser.add_argument('--curve', type=float, help='curve')&#10;    parser.add_argument('--interval', type=int, help='interval')&#10;    parser.add_argument('--delay', type=int, help='delay')&#10;    parser.add_argument('-d', '--download', action='store_true', help='download data')&#10;    parser.add_argument('--alpha_type', type=str, help='alpha type')&#10;    parser.add_argument('--fixed_model', type=str, help='fixed model name')&#10;    parser.add_argument('--assist_alpha_type', type=str, help='assist alpha type')&#10;    parser.add_argument('--cls_type', action='store_true', help='use cls type')&#10;    parser.add_argument('--assist_cls_type', action='store_true', help='use assist cls type')&#10;    parser.add_argument('--fc_res', type=str, help='fc_res path')&#10;    parser.add_argument('-m', '--mail', type=str, help='mail address')&#10;    parser.add_argument('--market', type=str, help='market')&#10;    parser.add_argument('--model', type=str, help='model name')&#10;    parser.add_argument('--alpha', type=str, help='model name')&#10;    parser.add_argument('--assist_model', type=str, help='assist model name')&#10;    parser.add_argument('--ret_of_day', type=str, help='return of day')&#10;    parser.add_argument('--ret_of_range', type=str, help='return of range')&#10;    parser.add_argument('--relative', action='store_true', help='relative return of range')&#10;    parser.add_argument('--topn', type=int, help='topn')&#10;    parser.add_argument('--tuning_days', type=int, help='tuning_days')&#10;    parser.add_argument('--period_n', type=int, help='period_n')&#10;    parser.add_argument('--assist_period_n', type=int, help='assist_period_n')&#10;    parser.add_argument('--train', type=str, help='train model')&#10;    parser.add_argument('--auto_feature_select', action='store_true', help='auto_feature_select')&#10;    parser.add_argument('--refine', type=str, help='refine data')&#10;    parser.add_argument('--train_year', type=int, help='train year')&#10;    parser.add_argument('--prefix', type=str, help='prefix')&#10;    parser.add_argument('--select_alpha', type=str, help='select_alpha')&#10;    parser.add_argument('--export', action='store_true', help='export data')&#10;    parser.add_argument('--evaluate', action='store_true', help='evaluate')&#10;    parser.add_argument('--notify_by_mail', action='store_true', help='notify by mail')&#10;    parser.add_argument('--train_all_ensemble', action='store_true', help='使用所有超参数训练模型并创建三三组合集成')&#10;    parser.add_argument('--find_best_comb', action='store_true', help='find_best_comb')&#10;    parser.add_argument('--draw_weights', action='store_true', help='draw weight vs return scatter plot')&#10;    if len(sys.argv) == 1:&#10;        parser.print_help()&#10;        sys.exit(1)&#10;    args = parser.parse_args()&#10;&#10;    # 初始化&#10;    init_qlib()&#10;    init_logger()&#10;    init_gpt()&#10;&#10;    # 配置参数&#10;    config_info['download_data'] = args.download&#10;    config_info['recommend_mode'] = config_info['download_data']&#10;    config_info['fixed_model'] = args.fixed_model&#10;    start_test_year = args.year if args.year is not None else 2020&#10;    end_test_year = args.end_year if args.end_year is not None else datetime.date.today().year&#10;    if args.curve is not None:&#10;        config_info['curve'] = args.curve&#10;    if args.market:&#10;        config_info['market'] = args.market&#10;    if args.notify_by_mail:&#10;        config_info['notify_by_mail'] = True&#10;    fc_res_path = args.fc_res + '.csv' if args.fc_res is not None else 'fc_res.csv'&#10;&#10;    interval = args.interval if args.interval is not None else config_info['interval']&#10;    config_info['interval'] = interval&#10;    delay = args.delay if args.delay is not None else config_info['train_delay']&#10;    config_info['train_delay'] = delay&#10;&#10;    if args.export:&#10;        dt = pd.read_pickle(f'refined_a458_{config_info[&quot;market&quot;]}.pkl')&#10;        dt = dt[dt.index.get_level_values(0) &gt;= '20231001']&#10;        dt.to_csv(f'refined_a458_{config_info[&quot;market&quot;]}.csv')&#10;        return&#10;&#10;    if args.model is not None:&#10;        config_info['model_name'] = args.model&#10;        config_info['assist_model_name'] = None&#10;&#10;    if args.assist_model is not None:&#10;        config_info['assist_model_name'] = args.assist_model&#10;&#10;    if args.market is not None and args.model is None:&#10;        config_info['model_name'] = modify_model_name(config_info['model_name'])&#10;        if config_info['assist_model_name']:&#10;            config_info['assist_model_name'] = modify_model_name(config_info['assist_model_name'])&#10;&#10;    config_info['weights421'] = not args.dis_weights421&#10;&#10;    if args.train_year is not None:&#10;        config_info['train_year'] = args.train_year&#10;&#10;    if args.auto_feature_select:&#10;        config_info['auto_feature_select'] = True&#10;&#10;    if args.hyperparameter_tune_off:&#10;        config_info['hyperparameter_tune_kwargs'] = False&#10;&#10;    if args.mail is not None:&#10;        config_info['mail'] = args.mail&#10;&#10;    prefix = args.prefix if args.prefix is not None else ''&#10;&#10;    if args.topn is not None:&#10;        config_info['topn'] = args.topn&#10;&#10;    if args.period_n is not None:&#10;        config_info['assist_period_n'] = config_info['period_n'] = args.period_n&#10;    if args.assist_period_n is not None:&#10;        config_info['assist_period_n'] = args.assist_period_n&#10;&#10;    if args.tuning_days is not None:&#10;        config_info['tuning_days'] = args.tuning_days&#10;&#10;    if args.quality is not None:&#10;        config_info['presets'] = args.quality&#10;&#10;    if args.cpu:&#10;        config_info['cpu'] = True&#10;&#10;    if args.feature_count is not None:&#10;        config_info['feature_count'] = args.feature_count&#10;        config_info['assist_feature_count'] = args.feature_count&#10;&#10;    if args.afc is not None:&#10;        config_info['assist_feature_count'] = args.afc&#10;&#10;    if args.alpha_type is not None:&#10;        config_info['alpha_type'] = args.alpha_type&#10;&#10;    if args.assist_alpha_type is not None:&#10;        config_info['assist_alpha_type'] = args.assist_alpha_type&#10;&#10;    if args.cls_type:&#10;        config_info['train_type'] = 'cls'&#10;&#10;    if args.assist_cls_type:&#10;        config_info['assist_train_type'] = 'cls'&#10;&#10;    if args.refine is not None:&#10;        gates = ast.literal_eval(args.refine)&#10;        df = pd.read_pickle(f'refined_{config_info[&quot;alpha_type&quot;]}_{config_info[&quot;market&quot;]}.pkl')&#10;        df = refine_stock_df(df, gates)&#10;        df.to_pickle(f'refined_{config_info[&quot;alpha_type&quot;]}_{config_info[&quot;market&quot;]}.pkl')&#10;&#10;    if args.evaluate:&#10;        evaluate_model(config_info['alpha_type'], config_info['period_n'],&#10;                       f'{start_test_year}0101', f'{end_test_year}1231', config_info['model_name'])&#10;&#10;    if args.generate_all:&#10;        date = datetime.date.today() if args.generate_all == 'today' \&#10;            else datetime.datetime.strptime(args.generate_all, '%Y%m%d').date()&#10;        fc = config_info['feature_count']&#10;        config_info['generate_all_features'] = True&#10;        config_info['feature_count'] = MAX_FEATURE_COUNT&#10;        prepare_data_for_all_year(end_date=date)&#10;        config_info['feature_count'] = fc&#10;        if config_info['alpha_158']:&#10;            logging.getLogger().info(&quot;开始处理alpha_158数据准备&quot;)&#10;            prepare_alpha158_data(datetime.date(2008, 1, 1), date)&#10;            logging.getLogger().info(&quot;正在与a300数据合并&quot;)&#10;            combined_with_a300()&#10;            logging.getLogger().info(&quot;Alpha_158数据准备完成&quot;)&#10;        else:&#10;            logging.getLogger().info(f&quot;正在加载精炼数据: refined_{config_info['alpha_type']}_{config_info['market']}.pkl&quot;)&#10;            df = pd.read_pickle(f'refined_{config_info[&quot;alpha_type&quot;]}_{config_info[&quot;market&quot;]}.pkl')&#10;            logging.getLogger().info(f&quot;正在精炼股票数据框，共{len(df)}行&quot;)&#10;            df = refine_stock_df(df, with_flag=False)&#10;            logging.getLogger().info(f&quot;正在保存精炼数据到pickle文件&quot;)&#10;            df.to_pickle(f'refined_{config_info[&quot;alpha_type&quot;]}_{config_info[&quot;market&quot;]}.pkl')&#10;&#10;        if os.path.exists('basic_data.pkl'):&#10;            logging.getLogger().info(&quot;发现basic_data.pkl文件，开始合并流程&quot;)&#10;            logging.getLogger().info(f&quot;正在加载refined_a458_{config_info['market']}.pkl&quot;)&#10;            df = pd.read_pickle(f'refined_a458_{config_info[&quot;market&quot;]}.pkl')&#10;            logging.getLogger().info(f&quot;正在加载basic_data.pkl&quot;)&#10;            basic_info = pd.read_pickle('basic_data.pkl')&#10;            &#10;            # 给basic_info的所有列名添加basic_前缀&#10;            basic_info.columns = [f'basic_{col}' for col in basic_info.columns]&#10;            logging.getLogger().info(f&quot;已为basic_info的列名添加basic_前缀，列名: {list(basic_info.columns)}&quot;)&#10;            &#10;            logging.getLogger().info(f&quot;正在合并数据框: df形状{df.shape}, basic_info形状{basic_info.shape}&quot;)&#10;            df = pd.merge(df, basic_info, how='left', left_index=True, right_index=True)&#10;            logging.getLogger().info(f&quot;合并完成，最终形状: {df.shape}&quot;)&#10;            df.to_pickle(f'refined_a458_{config_info[&quot;market&quot;]}.pkl')&#10;            logging.getLogger().info(&quot;基础数据合并完成并已保存&quot;)&#10;&#10;        # 合并市场状态数据&#10;        regime_file = f'{config_info[&quot;market&quot;]}_regimeID.csv'&#10;        if os.path.exists(regime_file):&#10;            logging.getLogger().info(f&quot;发现市场状态文件: {regime_file}，开始合并市场状态数据&quot;)&#10;            logging.getLogger().info(f&quot;正在加载refined_a458_{config_info['market']}.pkl用于状态合并&quot;)&#10;            df = pd.read_pickle(f'refined_a458_{config_info[&quot;market&quot;]}.pkl')&#10;            logging.getLogger().info(f&quot;正在从{regime_file}加载市场状态数据&quot;)&#10;            regime_data = pd.read_csv(regime_file, parse_dates=['date'])&#10;            logging.getLogger().info(f&quot;市场状态数据已加载，共{len(regime_data)}行，日期范围: {regime_data['date'].min()} 到 {regime_data['date'].max()}&quot;)&#10;            regime_data.set_index('date', inplace=True)&#10;&#10;            # 将df重置索引以便合并&#10;            logging.getLogger().info(&quot;正在重置df索引以进行合并操作&quot;)&#10;            df_reset = df.reset_index()&#10;            # 合并regime数据，基于date列&#10;            logging.getLogger().info(f&quot;正在合并市场状态数据: df形状{df_reset.shape}, 状态数据形状{regime_data.shape}&quot;)&#10;            df_merged = pd.merge(df_reset, regime_data, left_on='date', right_index=True, how='left')&#10;            logging.getLogger().info(f&quot;市场状态合并完成，合并后形状: {df_merged.shape}&quot;)&#10;            # 恢复原始索引&#10;            logging.getLogger().info(&quot;正在恢复原始多重索引&quot;)&#10;            df_merged.set_index(['date', 'sec_id'], inplace=True)&#10;            df_merged.to_pickle(f'refined_a458_{config_info[&quot;market&quot;]}.pkl')&#10;            logging.getLogger().info(&quot;市场状态数据合并完成并已保存&quot;)&#10;        else:&#10;            logging.getLogger().info(f&quot;未找到市场状态文件{regime_file}，跳过市场状态数据合并&quot;)&#10;&#10;&#10;    elif args.compute_ic_table:&#10;        start_year_ic = int(args.year)&#10;        end_year_ic = int(args.end_year)&#10;        for year in range(start_year_ic, end_year_ic + 1):&#10;            df = load_stock_data(config_info['alpha_type'], config_info['market'])&#10;            end = datetime.datetime(year, 12, 31)&#10;            start = end - datetime.timedelta(days=365 * args.train_year)&#10;            end = pd.to_datetime(end)&#10;            start = pd.to_datetime(start)&#10;&#10;            # Load existing IC file if it exists&#10;            ic_file_path = f'{config_info[&quot;alpha_type&quot;]}_{config_info[&quot;market&quot;]}_ic_{year}.csv'&#10;            if os.path.exists(ic_file_path):&#10;                existing_ic = pd.read_csv(ic_file_path, index_col=0)&#10;            else:&#10;                existing_ic = None&#10;            ic = compute_ic_table_for_data(df, start, end,&#10;                                           existing_ic.index.to_list() if existing_ic is not None else None)&#10;&#10;            # Merge with existing results if they exist&#10;            if existing_ic is not None:&#10;                ic = pd.concat([existing_ic, ic])&#10;&#10;            # Sort by feature sequence if available&#10;            try:&#10;                with open('feature_seq.txt', 'r') as file:&#10;                    index_order = [line.strip() for line in file]&#10;                    leftover_index = ic.index.difference(index_order)&#10;                    new_index = list(index_order) + list(leftover_index)&#10;                    ic = ic.reindex(new_index)&#10;            except FileNotFoundError:&#10;                pass&#10;&#10;            ic.to_csv(ic_file_path)&#10;&#10;    if args.run_current_best:&#10;        current_best_backtrade(start_year=start_test_year, end_year=end_test_year,&#10;                               save_path=fc_res_path, prefix=prefix)&#10;    if args.run_all_sub_model:&#10;        run_all_sub_model(f'{args.year}0101', f'{args.year}1231', args.model)&#10;    if args.prepare_model:&#10;        find_best_random_trained_model(start_year=start_test_year, end_year=end_test_year, prefix=prefix)&#10;&#10;    if args.get_returns:&#10;        if config_info['interval'] &gt;= 250:&#10;            get_returns_for_model_series_by_year(start_test_year, end_test_year, prefix, args.select_alpha)&#10;        else:&#10;            get_returns_for_model_series_by_season(prefix)&#10;&#10;    if args.run_select_alpha:&#10;        if os.path.exists('fc_res_prev.csv'):&#10;            feature_names = get_feature_names_from_fc_res('fc_res_prev.csv')&#10;            from_prev = True&#10;        else:&#10;            feature_names = get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'],&#10;                                              1000)&#10;            from_prev = False&#10;&#10;        if os.path.exists(fc_res_path):&#10;            exists_feature_names = get_feature_names_from_fc_res(fc_res_path)&#10;        else:&#10;            exists_feature_names = []&#10;&#10;        for fn in feature_names:&#10;            if fn in exists_feature_names:&#10;                continue&#10;            print('-------------------', fn)&#10;            config_info['added_alpha_for_stratergy'] = fn&#10;            current_best_backtrade(start_year=start_test_year, end_year=end_test_year, save_path=fc_res_path,&#10;                                   prefix=prefix)&#10;            config_info['fixed_model'] = None&#10;&#10;            if not from_prev:&#10;                config_info['added_alpha_for_stratergy'] = f'_{fn}'&#10;                current_best_backtrade(start_year=start_test_year, end_year=end_test_year, save_path=fc_res_path,&#10;                                       prefix=prefix)&#10;                config_info['fixed_model'] = None&#10;&#10;    if args.evaluate_alpha:&#10;        research_alpha(f'{args.year}0101', f'{args.year}1231', args.alpha, args.model)&#10;&#10;    if args.run_cur_year:&#10;        run_current_year_backtrade(prefix)&#10;&#10;    if args.rec is not None:&#10;        if os.path.exists('dibeili_set.pkl'):&#10;            config_info['interest'] = config_info['interest'] + pickle.load(open('dibeili_set.pkl', 'rb'))&#10;        recommend_for_today(with_gpt=(args.rec == 1), topn=10)&#10;    elif args.ret_of_day is not None:&#10;        ret = get_return_infos_of_range(args.ret_of_day, args.ret_of_day, config_info['model_name'],&#10;                                        topn=config_info['topn'],&#10;                                        assist_model_name=config_info['assist_model_name'])&#10;        print(ret)&#10;        print(ret.mean())&#10;    elif args.ret_of_range is not None:&#10;        days = args.ret_of_range.split('-')&#10;        ret = get_return_infos_of_range(days[0], days[1], config_info['model_name'], topn=config_info['topn'],&#10;                                        assist_model_name=config_info['assist_model_name'])&#10;        print_ret_range_info(ret)&#10;        if 'assist_return' in ret.columns:&#10;            ret = ret[ret['assist_return'] &gt; 0.6]&#10;            print('---------------return detail of assisted model --------------------')&#10;            print_ret_range_info(ret)&#10;    if args.train:&#10;        train_date = pd.to_datetime(str(args.train))&#10;        train_date = train_date - datetime.timedelta(days=1)&#10;        train(config_info['alpha_type'], config_info['period_n'], train_date, config_info['model_name'])&#10;&#10;    if args.find_best_comb:&#10;        ret = find_best_model_combination(start_test_year)&#10;        logging.info(ret)&#10;&#10;    if args.train_all_ensemble:&#10;        end_date = args.end_year if args.end_year is not None else datetime.date.today().year&#10;        end_date = datetime.date(end_date, 12, 31)&#10;        train_all_and_ensemble(&#10;            config_info['alpha_type'],&#10;            config_info['period_n'],&#10;            end_date,&#10;            config_info['model_name']&#10;        )&#10;&#10;    if args.draw_weights:&#10;        draw_weights_vs_returns(start_test_year, end_test_year)&#10;&#10;&#10;if __name__ == '__main__':&#10;    strategy_runner_main()&#10;" />
              <option name="updatedContent" value="import shutil&#10;from itertools import combinations&#10;import argparse&#10;import ast&#10;import os.path&#10;import pickle&#10;import re&#10;import sys&#10;&#10;import numpy as np&#10;import pandas as pd&#10;import torch&#10;&#10;import util&#10;from alpha_158 import prepare_alpha158_data, combined_with_a300&#10;from data_preparer import prepare_data_for_all_year&#10;from news_scrawler import select_stock&#10;from serialized_stratergy import (&#10;    test_serialized_stratergy_s,&#10;    test_serialized_combined_stratergy_s,&#10;    test_model_stratergy,&#10;    test_combined_model_stratergy,&#10;    test_add_model_assisted_stratergy&#10;)&#10;from stock_recommander import (&#10;    get_return_infos_of_range,&#10;    recommend_for_today,&#10;    get_relative_returns,&#10;    research_alpha&#10;)&#10;from tools.index_assessment import calculate_annual_equal_weight_return&#10;from train_evaluator import *&#10;from util import *&#10;&#10;&#10;def is_assist_model_needed():&#10;    return (config_info['assist_feature_count'] != config_info['feature_count']) or \&#10;        (config_info['period_n'] != config_info['assist_period_n'])&#10;&#10;&#10;def prepare_ic(year):&#10;    config_info['ic_year'] = year&#10;&#10;&#10;def get_model_name(year, prefix=''):&#10;    &quot;&quot;&quot;根据传入的year和prefix生成model_name。&quot;&quot;&quot;&#10;    ret = (&#10;        f'{prefix}{config_info[&quot;feature_count&quot;]}_sel_{config_info[&quot;presets&quot;][0]}_'&#10;        f'{config_info[&quot;train_type&quot;]}_{config_info[&quot;alpha_type&quot;]}_{config_info[&quot;period_n&quot;]}_ends_{year - 1}_t{config_info[&quot;tuning_days&quot;]}'&#10;    )&#10;    return ret if config_info['remove_from_fid'] == 0 else f'{ret}_r{config_info[&quot;remove_from_fid&quot;]}'&#10;&#10;&#10;def get_assist_model_name(year, prefix=''):&#10;    &quot;&quot;&quot;根据传入的year和prefix生成assist_model_name。&quot;&quot;&quot;&#10;    ret = (&#10;        f'{prefix}{config_info[&quot;assist_feature_count&quot;]}_sel_{config_info[&quot;presets&quot;][0]}_'&#10;        f'{config_info[&quot;train_type&quot;]}_{config_info[&quot;alpha_type&quot;]}_{config_info[&quot;assist_period_n&quot;]}_ends_{year - 1}_t{config_info[&quot;tuning_days&quot;]}'&#10;    )&#10;    return ret if config_info['remove_from_fid'] == 0 else f'{ret}_r{config_info[&quot;remove_from_fid&quot;]}'&#10;&#10;&#10;def run_all_sub_model(start_date, end_date, model_name):&#10;    predictor = load_model(model_name)&#10;    sub_names = predictor.model_names()&#10;    rets = {}&#10;    cur_best_return = -1&#10;    for sub_name in sub_names:&#10;        ret,_,_ = test_model_stratergy(config_info['alpha_type'], start_date, end_date, model_name, sub_name)&#10;        rets[sub_name] = ret&#10;        if ret &gt; cur_best_return:&#10;            cur_best = sub_name&#10;            cur_best_return = ret&#10;            logging.info(f'{cur_best} -&gt; {cur_best_return}')&#10;    rets_df = pd.DataFrame(rets, index=[0])&#10;    rets_df.to_csv(f'sub_model_rets_{model_name}.csv', index=False)&#10;&#10;&#10;def run_serialized_stratergy(start_date, end_date, interval=60, prefix=''):&#10;    &quot;&quot;&quot;根据当前的config_info，运行序列化策略并返回收益和统计信息。&quot;&quot;&quot;&#10;    if config_info['fixed_model'] is not None:&#10;        model_name = config_info['fixed_model']&#10;        if config_info['add_info']:&#10;            return test_add_model_assisted_stratergy(config_info['alpha_type'], start_date, end_date, model_name)&#10;        elif is_assist_model_needed():&#10;            return test_combined_model_stratergy(config_info['alpha_type'], start_date, end_date, model_name,&#10;                                                 config_info['assist_model_name'])&#10;        else:&#10;            return test_model_stratergy(config_info['alpha_type'], start_date, end_date, model_name)&#10;    elif (config_info['assist_feature_count'] == config_info['feature_count']) and \&#10;            (config_info['assist_train_type'] == config_info['train_type']):&#10;        return test_serialized_stratergy_s(start_date, end_date, interval, prefix)&#10;    else:&#10;        return test_serialized_combined_stratergy_s(start_date, end_date, interval, prefix, prefix)&#10;&#10;&#10;def run_and_analyze_strategy(start_date, end_date, interval, prefix=''):&#10;    &quot;&quot;&quot;运行策略并获取收益及drawdown分析信息&quot;&quot;&quot;&#10;    ret, stats, _ = run_serialized_stratergy(start_date, end_date, interval, prefix)&#10;    analysis = stats[0].analyzers.drawdown.get_analysis()&#10;    drawdown_len = analysis['len']&#10;    drawdown_max = analysis['max']['drawdown']&#10;    sharpe_ratio = stats[0].analyzers.sharperatio.get_analysis()['sharperatio']&#10;    sortino_ratio = stats[0].analyzers.annualizedsortinoratio.get_analysis()['sortino']&#10;    return ret, drawdown_len, drawdown_max, sharpe_ratio, sortino_ratio&#10;&#10;&#10;def prepare_and_train_model(year, prefix=''):&#10;    &quot;&quot;&quot;在回测前准备IC并训练模型（如果需要）&quot;&quot;&quot;&#10;    prepare_ic(year - 1)&#10;    config_info['model_name'] = config_info['fixed_model'] = get_model_name(year, prefix)&#10;    if util.load_model(config_info['model_name']) is None:&#10;        train(config_info['alpha_type'], config_info['period_n'], f'{year - 1}1231', config_info['model_name'])&#10;&#10;    if is_assist_model_needed():&#10;        config_info['assist_model_name'] = config_info['assist_fixed_model'] = get_assist_model_name(year, prefix)&#10;        if util.load_model(config_info['assist_model_name']) is None:&#10;            train(config_info['alpha_type'], config_info['assist_period_n'], f'{year - 1}1231',&#10;                  config_info['assist_model_name'])&#10;&#10;&#10;def update_returns_csv(df, ret_df, file_name='returns_fc.csv'):&#10;    &quot;&quot;&quot;将新的收益结果合并到csv文件中并保存&quot;&quot;&quot;&#10;    result_df = pd.concat([df, ret_df], ignore_index=True)&#10;    result_df.to_csv(file_name)&#10;&#10;&#10;def notify_results(title, attachment=None):&#10;    &quot;&quot;&quot;使用邮件通知结果&quot;&quot;&quot;&#10;    logging.getLogger().info(title)&#10;    notify_by_email(title)&#10;    if attachment is not None:&#10;        notify_by_email_with_attachment(title, attachment)&#10;&#10;&#10;class YearBacktraderListener:&#10;    def __init__(self, prefix='', interval=250):&#10;        self.prefix = prefix&#10;        self.year_interval = int(interval / 250)&#10;        self.cur_interval = 0&#10;        self.enabled = config_info['fixed_model'] is None&#10;&#10;    def on_pre_year_backtrader(self, year):&#10;        if not self.enabled:&#10;            return&#10;        if self.year_interval &lt; 1:&#10;            prepare_ic(year - 1)&#10;            return&#10;&#10;        if self.cur_interval == 0:&#10;            # 第一次进入这个区间时训练模型&#10;            prepare_and_train_model(year, self.prefix)&#10;        self.cur_interval += 1&#10;        if self.cur_interval &gt;= self.year_interval:&#10;            self.cur_interval = 0&#10;&#10;&#10;def run_current_year_backtrade(prefix=''):&#10;    &quot;&quot;&quot;运行当前年份的回测，按整年进行。&quot;&quot;&quot;&#10;    interval = config_info['interval']&#10;    now = get_last_time()&#10;    listener = YearBacktraderListener(prefix, interval)&#10;    listener.on_pre_year_backtrader(now.year)&#10;&#10;    start_date = f'{now.year}0101'&#10;    end_date = now.strftime('%Y%m%d')&#10;&#10;    ret_cur_year, drawdown_len, drawdown_max, sharpe_ratio, sortino_ratio = run_and_analyze_strategy(start_date,&#10;                                                                                                     end_date, interval,&#10;                                                                                                     prefix)&#10;&#10;    title = (f&quot;{now.year} fc:{config_info['feature_count']}_{config_info['assist_feature_count']} &quot;&#10;             f&quot;interval:{interval} period:{config_info['period_n']} cur_revenue {1} year_revenue {ret_cur_year}&quot;)&#10;    logging.getLogger().info(title)&#10;    return ret_cur_year&#10;&#10;&#10;def get_returns_for_model_series_by_season(prefix=&quot;&quot;):&#10;    &quot;&quot;&quot;获取模型按季度的收益情况（如果需要，可保留或删除）&quot;&quot;&quot;&#10;    file_name = 'returns_fc.csv'&#10;    df = pd.read_csv(file_name, index_col=0) if os.path.exists(file_name) else pd.DataFrame()&#10;    ret_df = pd.Series(dtype=float)&#10;    ret_df['feature_count'] = config_info['feature_count']&#10;&#10;    files = os.listdir('models')&#10;    pattern = re.compile(r'.*?(\d{8})_t0(?:_.*)?_(\d{8})$')&#10;    datelist = []&#10;    for file in files:&#10;        if not file.startswith(prefix):&#10;            continue&#10;        match = pattern.match(file)&#10;        if match:&#10;            _, date_str = match.groups()&#10;            date = datetime.datetime.strptime(date_str, '%Y%m%d')&#10;            datelist.append((date, file))&#10;    sorted_files = sorted(datelist, key=lambda x: x[0])&#10;    if not sorted_files:&#10;        return&#10;&#10;    this_year = sorted_files[0][0].year&#10;    total_ret = 1&#10;    this_year_ret = 1&#10;    mail_msg = ''&#10;    holding_days = int(config_info['topn'] / config_info['max_sell'])&#10;    for i in range(0, len(sorted_files)):&#10;        model_path = sorted_files[i][1]&#10;        start_date = sorted_files[i][0] + datetime.timedelta(days=1)&#10;        prepare_ic(start_date.year - 1)&#10;        end_date = sorted_files[i + 1][0] if (i + 1) &lt; len(sorted_files) else datetime.datetime.today()&#10;        ret = get_relative_returns(start_date, end_date, model_path, config_info['topn'])&#10;        cur_ret = ret.prod() ** (1 / holding_days)&#10;        total_ret *= cur_ret&#10;        if start_date.year == this_year:&#10;            this_year_ret *= cur_ret&#10;        else:&#10;            msg = f'{this_year} ret is {this_year_ret}'&#10;            ret_df[str(this_year)] = this_year_ret&#10;            mail_msg += msg + '\n'&#10;            logging.info(msg)&#10;            this_year = start_date.year&#10;            this_year_ret = cur_ret&#10;&#10;    msg = f'{this_year} ret is {this_year_ret}'&#10;    mail_msg += msg + '\n'&#10;    ret_df[str(this_year)] = this_year_ret&#10;    msg = f'total_ret is {total_ret}'&#10;    mail_msg += msg + '\n'&#10;    logging.info(msg)&#10;    ret_df['total'] = total_ret&#10;    ret_df = pd.DataFrame(ret_df).T&#10;    update_returns_csv(df, ret_df, file_name='returns_fc.csv')&#10;    if config_info['notify_by_mail']:&#10;        notify_by_email_with_attachment(f'get_returns_{config_info[&quot;feature_count&quot;]}_{config_info[&quot;interval&quot;]}',&#10;                                        'returns_fc.csv')&#10;&#10;&#10;def get_returns_for_model_series_by_year(start_year=2020, end_year=2025, prefix=&quot;&quot;, select_alpha=None):&#10;    &quot;&quot;&quot;获取模型按年度的收益情况，按整年进行回测。&quot;&quot;&quot;&#10;    file_name = 'returns_fc.csv'&#10;    df = pd.read_csv(file_name, index_col=0) if os.path.exists(file_name) else pd.DataFrame()&#10;    ret_df = pd.Series(dtype=float)&#10;&#10;    def skip_year_fn(y):&#10;        return (len(config_info['selected_year']) &gt; 0) and (str(y) not in config_info['selected_year'])&#10;&#10;    if select_alpha is not None:&#10;        if select_alpha.startswith('_'):&#10;            ret_df['feature_count'] = f'{config_info[&quot;feature_count&quot;]}_-{select_alpha}'&#10;        else:&#10;            ret_df['feature_count'] = f'{config_info[&quot;feature_count&quot;]}_{select_alpha}'&#10;    else:&#10;        ret_df['feature_count'] = config_info['feature_count']&#10;    holding_days = int(config_info['topn'] / config_info['max_sell'])&#10;    if holding_days &gt; 1:&#10;        ret_df['feature_count'] = f'{ret_df[&quot;feature_count&quot;]}_h{holding_days}'&#10;    rets = []&#10;    today = datetime.datetime.today()&#10;    for year in range(start_year, end_year + 1):&#10;        if skip_year_fn(year):&#10;            continue&#10;        if config_info['fixed_model'] is None:&#10;            prepare_and_train_model(year, prefix)&#10;&#10;        end_date = f'{year}1231' if year != today.year else today.strftime('%Y%m%d')&#10;        ret = get_relative_returns(f'{year}0101', end_date,&#10;                                   config_info['fixed_model'] if config_info['fixed_model'] is not None else&#10;                                   config_info['model_name'], config_info['topn'],&#10;                                   selected_alpha=select_alpha)&#10;        cur_ret = ret.prod() ** (1 / holding_days)&#10;        rets.append(cur_ret)&#10;        logging.info(f'{year} ret is {cur_ret}')&#10;        ret_df[str(year)] = cur_ret&#10;&#10;    total_ret = 1&#10;    for r in rets:&#10;        total_ret *= r&#10;    logging.info(f'total ret is {total_ret}')&#10;    ret_df['total'] = total_ret&#10;    ret_df = pd.DataFrame(ret_df).T&#10;    update_returns_csv(df, ret_df, file_name='returns_fc.csv')&#10;    if config_info['notify_by_mail']:&#10;        notify_by_email_with_attachment(f'get_returns_{config_info[&quot;feature_count&quot;]}_{config_info[&quot;interval&quot;]}',&#10;                                        'returns_fc.csv')&#10;    return rets, total_ret&#10;&#10;&#10;def analysis_selected_stocks_by_dates(start, end, model_name, topn=5):&#10;    &quot;&quot;&quot;分析选定日期范围内的股票收益。&quot;&quot;&quot;&#10;    ret = get_return_infos_of_range(start, end, model_name, topn=topn)&#10;    return ret, ret.describe()&#10;&#10;&#10;def analysis_gpt_stocks_by_dates(start, end, model_name, topn=10):&#10;    &quot;&quot;&quot;使用GPT分析选定日期范围内的股票收益。&quot;&quot;&quot;&#10;    data, _ = analysis_selected_stocks_by_dates(start, end, model_name, topn=topn)&#10;    ret = pd.DataFrame()&#10;    for date in data.index.get_level_values(0).unique():&#10;        data_in_date = data.loc[data.index.get_level_values(0) == date]&#10;        codes = data_in_date.index.get_level_values(1).unique()&#10;        selected_codes = select_stock(codes, date + pd.DateOffset(days=1))&#10;        selected = data_in_date.loc[data_in_date.index.get_level_values(1).isin(selected_codes)]&#10;        ret = pd.concat([ret, selected], axis=0)&#10;    return ret, ret.describe()&#10;&#10;&#10;def print_ret_range_info(ret):&#10;    &quot;&quot;&quot;打印收益范围的详细信息。&quot;&quot;&quot;&#10;    print('---------------return describe --------------------')&#10;    print(ret.describe())&#10;    print('---------------return rate statics--------------------')&#10;    print(ret.groupby('date').mean().mean())&#10;&#10;&#10;def modify_model_name(model_name: str):&#10;    &quot;&quot;&quot;根据市场修改model_name。&quot;&quot;&quot;&#10;    market = configs.convert_to_csi_code(config_info['market'])&#10;    if market == 'csi300':&#10;        return model_name&#10;    else:&#10;        index = model_name.rfind('_')&#10;        return model_name[:index] + '_' + market + model_name[index:]&#10;&#10;&#10;def seed_everything(seed):&#10;    &quot;&quot;&quot;设置随机种子以确保结果可复现。&quot;&quot;&quot;&#10;    random.seed(seed)&#10;    os.environ['PYTHONHASHSEED'] = str(seed)&#10;    np.random.seed(seed)&#10;    torch.manual_seed(seed)&#10;    torch.cuda.manual_seed(seed)&#10;    torch.cuda.manual_seed_all(seed)&#10;    torch.backends.cudnn.deterministic = True&#10;    torch.backends.cudnn.benchmark = False&#10;    if not config_info['cpu']:&#10;        torch.use_deterministic_algorithms(True)&#10;&#10;&#10;def draw_weights_vs_returns(start_year, end_year):&#10;    &quot;&quot;&quot;绘制weight列随return列变化的散点图&quot;&quot;&quot;&#10;    import matplotlib.pyplot as plt&#10;    import seaborn as sns&#10;&#10;    # 设置中文字体&#10;    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']&#10;    plt.rcParams['axes.unicode_minus'] = False&#10;&#10;    print(f&quot;正在加载 {start_year} 到 {end_year} 年的股票数据...&quot;)&#10;&#10;    # 加载股票数据&#10;    df = util.load_stock_data(config_info['alpha_type'], config_info['market'])&#10;    factors = util.get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'])&#10;&#10;    all_data = []&#10;&#10;    for year in range(start_year, end_year + 1):&#10;        print(f&quot;处理 {year} 年数据...&quot;)&#10;        start_date = f'{year}0101'&#10;        end_date = f'{year}1231'&#10;&#10;        # 调用prepare_data_for_model&#10;        year_data = util.prepare_data_for_model(df, factors, config_info['period_n'], start_date, end_date)&#10;&#10;        if 'weight' in year_data.columns and 'return' in year_data.columns:&#10;            # 只保留有效数据&#10;            valid_data = year_data[['weight', 'return']].dropna()&#10;            if not valid_data.empty:&#10;                valid_data['year'] = year&#10;                all_data.append(valid_data)&#10;                print(f&quot;  {year} 年有效数据点: {len(valid_data)}&quot;)&#10;        else:&#10;            print(f&quot;  {year} 年数据中缺少 weight 或 return 列&quot;)&#10;&#10;    if not all_data:&#10;        print(&quot;没有找到包含 weight 和 return 列的有效数据&quot;)&#10;        return&#10;&#10;    # 合并所有年份的数据&#10;    combined_data = pd.concat(all_data, ignore_index=True)&#10;    print(f&quot;总共有效数据点: {len(combined_data)}&quot;)&#10;&#10;    # 创建图表&#10;    plt.figure(figsize=(12, 8))&#10;&#10;    # 绘制散点图&#10;    scatter = plt.scatter(combined_data['return'], combined_data['weight'],&#10;                         c=combined_data['year'], cmap='viridis', alpha=0.6, s=20)&#10;&#10;    # 添加颜色条&#10;    cbar = plt.colorbar(scatter)&#10;    cbar.set_label('年份', fontsize=12)&#10;&#10;    # 计算相关系数&#10;    correlation = combined_data['return'].corr(combined_data['weight'])&#10;&#10;    # 添加趋势线&#10;    z = np.polyfit(combined_data['return'], combined_data['weight'], 1)&#10;    p = np.poly1d(z)&#10;    plt.plot(combined_data['return'], p(combined_data['return']), &quot;r--&quot;, alpha=0.8, linewidth=2)&#10;&#10;    # 设置标签和标题&#10;    plt.xlabel('Return', fontsize=12)&#10;    plt.ylabel('Weight', fontsize=12)&#10;    plt.title(f'Weight vs Return 散点图 ({start_year}-{end_year})\n相关系数: {correlation:.4f}', fontsize=14)&#10;    plt.grid(True, alpha=0.3)&#10;&#10;    # 保存图片&#10;    filename = f'weight_vs_return_{start_year}_{end_year}.png'&#10;    plt.savefig(filename, dpi=300, bbox_inches='tight')&#10;    print(f&quot;图表已保存为: {filename}&quot;)&#10;&#10;    # 显示统计信息&#10;    print(f&quot;\n统计信息:&quot;)&#10;    print(f&quot;Return 范围: {combined_data['return'].min():.6f} 到 {combined_data['return'].max():.6f}&quot;)&#10;    print(f&quot;Weight 范围: {combined_data['weight'].min():.6f} 到 {combined_data['weight'].max():.6f}&quot;)&#10;    print(f&quot;相关系数: {correlation:.6f}&quot;)&#10;&#10;    # 按年份显示统计&#10;    print(f&quot;\n按年份统计:&quot;)&#10;    for year in range(start_year, end_year + 1):&#10;        year_data = combined_data[combined_data['year'] == year]&#10;        if not year_data.empty:&#10;            year_corr = year_data['return'].corr(year_data['weight'])&#10;            print(f&quot;  {year}: 数据点 {len(year_data)}, 相关系数 {year_corr:.6f}&quot;)&#10;&#10;    # 按weights分为10等段统计&#10;    print(f&quot;\n按weights分段统计 (10等段):&quot;)&#10;    print(&quot;=&quot; * 80)&#10;&#10;    # 按weights排序&#10;    sorted_data = combined_data.sort_values('weight')&#10;&#10;    # 分为10等段&#10;    n_segments = 10&#10;    segment_size = len(sorted_data) // n_segments&#10;&#10;    segment_stats = []&#10;&#10;    for i in range(n_segments):&#10;        start_idx = i * segment_size&#10;        if i == n_segments - 1:  # 最后一段包含所有剩余数据&#10;            end_idx = len(sorted_data)&#10;        else:&#10;            end_idx = (i + 1) * segment_size&#10;&#10;        segment_data = sorted_data.iloc[start_idx:end_idx]&#10;&#10;        if not segment_data.empty:&#10;            # 计算return绝对值的统计信息&#10;            abs_returns = segment_data['return'].abs()&#10;&#10;            stats = {&#10;                'segment': i + 1,&#10;                'count': len(segment_data),&#10;                'weight_min': segment_data['weight'].min(),&#10;                'weight_max': segment_data['weight'].max(),&#10;                'weight_mean': segment_data['weight'].mean(),&#10;                'return_abs_mean': abs_returns.mean(),&#10;                'return_abs_var': abs_returns.var(),&#10;                'return_abs_std': abs_returns.std(),&#10;                'return_mean': segment_data['return'].mean(),&#10;                'return_std': segment_data['return'].std()&#10;            }&#10;&#10;            segment_stats.append(stats)&#10;&#10;            print(f&quot;第 {i+1:2d} 段: 数据点 {len(segment_data):6d} | &quot;&#10;                  f&quot;Weight范围 [{stats['weight_min']:8.6f}, {stats['weight_max']:8.6f}] | &quot;&#10;                  f&quot;Weight均值 {stats['weight_mean']:8.6f}&quot;)&#10;            print(f&quot;        |Return|均值: {stats['return_abs_mean']:8.6f} | &quot;&#10;                  f&quot;|Return|方差: {stats['return_abs_var']:8.6f} | &quot;&#10;                  f&quot;|Return|标准差: {stats['return_abs_std']:8.6f}&quot;)&#10;            print(f&quot;        Return均值: {stats['return_mean']:8.6f} | &quot;&#10;                  f&quot;Return标准差: {stats['return_std']:8.6f}&quot;)&#10;            print(&quot;-&quot; * 80)&#10;&#10;    # 创建分段统计的可视化&#10;    if segment_stats:&#10;        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))&#10;&#10;        segments = [s['segment'] for s in segment_stats]&#10;        weight_means = [s['weight_mean'] for s in segment_stats]&#10;        return_abs_means = [s['return_abs_mean'] for s in segment_stats]&#10;        return_abs_vars = [s['return_abs_var'] for s in segment_stats]&#10;        return_means = [s['return_mean'] for s in segment_stats]&#10;&#10;        # 子图1: Weight均值 vs 段数&#10;        ax1.bar(segments, weight_means, alpha=0.7, color='skyblue')&#10;        ax1.set_xlabel('分段')&#10;        ax1.set_ylabel('Weight均值')&#10;        ax1.set_title('各段Weight均值')&#10;        ax1.grid(True, alpha=0.3)&#10;&#10;        # 子图2: |Return|均值 vs 段数&#10;        ax2.bar(segments, return_abs_means, alpha=0.7, color='lightcoral')&#10;        ax2.set_xlabel('分段')&#10;        ax2.set_ylabel('|Return|均值')&#10;        ax2.set_title('各段|Return|均值')&#10;        ax2.grid(True, alpha=0.3)&#10;&#10;        # 子图3: |Return|方差 vs 段数&#10;        ax3.bar(segments, return_abs_vars, alpha=0.7, color='lightgreen')&#10;        ax3.set_xlabel('分段')&#10;        ax3.set_ylabel('|Return|方差')&#10;        ax3.set_title('各段|Return|方差')&#10;        ax3.grid(True, alpha=0.3)&#10;&#10;        # 子图4: Weight均值 vs |Return|均值&#10;        ax4.scatter(weight_means, return_abs_means, s=100, alpha=0.7, color='purple')&#10;        for i, (x, y) in enumerate(zip(weight_means, return_abs_means)):&#10;            ax4.annotate(f'{i+1}', (x, y), xytext=(5, 5), textcoords='offset points')&#10;&#10;        # 添加趋势线&#10;        if len(weight_means) &gt; 1:&#10;            z = np.polyfit(weight_means, return_abs_means, 1)&#10;            p = np.poly1d(z)&#10;            ax4.plot(weight_means, p(weight_means), &quot;r--&quot;, alpha=0.8)&#10;&#10;            # 计算相关系数&#10;            segment_corr = np.corrcoef(weight_means, return_abs_means)[0, 1]&#10;            ax4.set_title(f'Weight均值 vs |Return|均值\n(相关系数: {segment_corr:.4f})')&#10;        else:&#10;            ax4.set_title('Weight均值 vs |Return|均值')&#10;&#10;        ax4.set_xlabel('Weight均值')&#10;        ax4.set_ylabel('|Return|均值')&#10;        ax4.grid(True, alpha=0.3)&#10;&#10;        plt.tight_layout()&#10;&#10;        # 保存分段统计图&#10;        segment_filename = f'weight_segments_analysis_{start_year}_{end_year}.png'&#10;        plt.savefig(segment_filename, dpi=300, bbox_inches='tight')&#10;        print(f&quot;\n分段统计图已保存为: {segment_filename}&quot;)&#10;&#10;        # 保存统计数据到CSV&#10;        segment_df = pd.DataFrame(segment_stats)&#10;        csv_filename = f'weight_segments_stats_{start_year}_{end_year}.csv'&#10;        segment_df.to_csv(csv_filename, index=False)&#10;        print(f&quot;分段统计数据已保存为: {csv_filename}&quot;)&#10;&#10;        plt.show()&#10;&#10;    plt.show()&#10;&#10;def find_best_random_trained_model(prefix=&quot;&quot;, start_year=2018, end_year=None):&#10;    for year in range(start_year, end_year + 1):&#10;        max_ret = -1&#10;        max_model_name = None&#10;        for train_irr in range(0, 10):&#10;            model_name = get_model_name(year, prefix=f'{prefix}_{train_irr}_')&#10;            config_info['train_seed'] = train_irr&#10;            if load_model(model_name) is None:&#10;                train(config_info['alpha_type'], config_info['period_n'], f'{year - 1}1231', model_name)&#10;            pre_test_year = year - 1&#10;            ret = get_relative_returns(f'{pre_test_year}0101', f'{pre_test_year}1231', model_name, config_info['topn'],&#10;                                       tuning_sec_only=True)&#10;            holding_days = int(config_info['topn'] / config_info['max_sell'])&#10;            cur_ret = ret.prod() ** (1 / holding_days)&#10;            if cur_ret &gt; max_ret:&#10;                max_ret = cur_ret&#10;                max_model_name = model_name&#10;                logging.info(f'select {train_irr} as best model')&#10;        shutil.copytree(f'models/{max_model_name}', f'models/{get_model_name(year, prefix)}')&#10;&#10;&#10;def current_best_backtrade(market=None, prefix=&quot;&quot;, start_year=2018, save_path='fc_res.csv',&#10;                           end_year=None):&#10;    &quot;&quot;&quot;运行当前最佳策略的回测，按整年进行。&quot;&quot;&quot;&#10;    interval = config_info['interval']&#10;    if end_year is None:&#10;        end_year = datetime.date.today().year&#10;    if market is None:&#10;        market = config_info['market']&#10;&#10;    min_res = calculate_annual_equal_weight_return(configs.convert_to_csi_code(market), start_year, end_year)&#10;    print('基准收益率:', min_res)&#10;    min_res = {k: max(v + 1, 1) for k, v in min_res.items()}&#10;    if config_info['enable_min_res']:&#10;        if isinstance(config_info['enable_min_res'], str):&#10;            min_res = ast.literal_eval(config_info['enable_min_res'])&#10;&#10;&#10;    if os.path.exists(save_path):&#10;        df = pd.read_csv(save_path, index_col=0)&#10;    else:&#10;        df = pd.DataFrame()&#10;&#10;    trade_file = f'trade_{config_info[&quot;feature_count&quot;]}.csv'&#10;    if os.path.exists(trade_file) and not config_info['use_rank_model'] and config_info['trade_log']:&#10;        os.remove(trade_file)&#10;&#10;    init_qlib()&#10;    config_info['market'] = market&#10;    ret = 1&#10;    now = get_last_time()&#10;    ret_df = pd.Series(dtype=float)&#10;&#10;    # 设置策略名称&#10;    if config_info['fixed_model'] is not None:&#10;        ret_df['name'] = config_info['fixed_model']&#10;    elif config_info['assist_feature_count'] != config_info['feature_count']:&#10;        ret_df['name'] = f'{prefix}{market}_combine_{config_info[&quot;feature_count&quot;]}'&#10;    else:&#10;        ret_df['name'] = f'{prefix}{market}_{config_info[&quot;feature_count&quot;]}'&#10;    if config_info['add_info']:&#10;        ret_df['name'] += '_addinfo'&#10;    if config_info['added_alpha_for_stratergy'] is not None:&#10;        ret_df['name'] += f&quot;_{config_info['added_alpha_for_stratergy']}&quot;&#10;    ret_df['name'] += f'_p{config_info[&quot;period_n&quot;]}_top_{config_info[&quot;topn&quot;]}'&#10;    if config_info['cpu']:&#10;        ret_df['name'] += '_cpu'&#10;&#10;    listener = YearBacktraderListener(prefix, interval)&#10;&#10;    # 处理所有年份回测&#10;    years_to_test = config_info['selected_year'] if len(config_info['selected_year']) &gt; 0 else range(end_year,&#10;                                                                                                     start_year - 1, -1)&#10;    years_to_test = [y for y in years_to_test if int(y) in range(end_year, start_year - 1, -1)]&#10;    for test_year in years_to_test:&#10;        test_year = int(test_year)&#10;        # 当年特殊处理&#10;        if test_year == now.year:&#10;            start_date = f'{test_year}0101'&#10;            end_date = now.strftime('%Y%m%d')&#10;        else:&#10;            start_date = f'{test_year}0101'&#10;            end_date = f'{test_year}1231'&#10;&#10;        listener.on_pre_year_backtrader(test_year)&#10;        ret_cur_year, drawdown_len, drawdown_max, sharpe_ratio, sortino_ratio = run_and_analyze_strategy(start_date,&#10;                                                                                                         end_date,&#10;                                                                                                         interval,&#10;                                                                                                         prefix)&#10;&#10;        ret *= ret_cur_year&#10;        ret_df[str(test_year)] = ret_cur_year&#10;        ret_df[str(test_year) + '_drawdown_len'] = drawdown_len&#10;        ret_df[str(test_year) + '_drawdown_max'] = drawdown_max&#10;        ret_df[str(test_year) + '_sharpe_ratio'] = sharpe_ratio&#10;        ret_df[str(test_year) + '_sortino_ratio'] = sortino_ratio&#10;&#10;        # 当年需要发送更详细的通知&#10;        if test_year == now.year:&#10;            title = (f&quot;{test_year} fc:{config_info['feature_count']}_{config_info['assist_feature_count']} &quot;&#10;                     f&quot;interval:{interval} period:{config_info['period_n']} total_revenue {ret} &quot;&#10;                     f&quot;year_revenue {ret_cur_year}&quot;)&#10;        else:&#10;            title = f'{test_year} interval {interval} total_revenue {ret} year_revenue {ret_cur_year}'&#10;        notify_results(title, attachment=None)&#10;&#10;        if config_info['enable_min_res'] and ret_cur_year &lt; min_res.get(test_year, 0):&#10;            break&#10;&#10;    title = (f&quot;fc:{config_info['feature_count']}_{config_info['assist_feature_count']} &quot;&#10;             f&quot;interval:{interval} period:{config_info['period_n']} total_revenue {ret}&quot;)&#10;    notify_results(title)&#10;&#10;    ret_df['total'] = ret&#10;    ret_df['feature_count'] = config_info['feature_count']&#10;    ret_df['drawdown_len'] = ret_df.filter(like='_drawdown_len').sum()&#10;    ret_df['drawdown_max'] = ret_df.filter(like='_drawdown_max').max()&#10;    ret_df['sharpe_ratio'] = ret_df.filter(like='_sharpe_ratio').mean()&#10;    ret_df['sortino_ratio'] = ret_df.filter(like='_sortino_ratio').mean()&#10;    ret_df = pd.DataFrame(ret_df).T&#10;    pd.concat([df, ret_df], ignore_index=True).to_csv(save_path)&#10;    if config_info['notify_by_mail']:&#10;        dump_fc_res(f'{save_path}_{config_info[&quot;feature_count&quot;]}', save_path)&#10;    return ret, ret_df&#10;&#10;&#10;def dump_fc_res(title, fc_res_path):&#10;    df = pd.read_csv(fc_res_path, index_col=0)&#10;    last_total = df.iloc[-1]['total'] if 'total' in df.columns else None&#10;    last_name = df.iloc[-1]['name'] if 'name' in df.columns else None&#10;&#10;    max_total_row = df.loc[df['total'].idxmax()] if 'total' in df.columns else None&#10;    max_total = max_total_row['total'] if max_total_row is not None else None&#10;    max_total_name = max_total_row['name'] if max_total_row is not None else None&#10;&#10;    msg = f&quot;Last: {last_total}, Name: {last_name}&quot;&#10;    msg += f&quot;\nMax : {max_total}, Name: {max_total_name}&quot;&#10;&#10;    send_email_attachment(title, config_info['mail'], msg, fc_res_path)&#10;&#10;&#10;def get_feature_names_from_fc_res(fc_res_path):&#10;    fc_res_file = pd.read_csv(fc_res_path)&#10;    feature_names = []&#10;    for name in fc_res_file['name']:&#10;        parts = name.split('_')&#10;        stock_code_idx = -1&#10;        for i, part in enumerate(parts):&#10;            if len(part) == 6 and part.isdigit():&#10;                stock_code_idx = i&#10;                break&#10;&#10;        feature_name = parts[stock_code_idx + 2]&#10;        if len(feature_name) == 0:&#10;            feature_name = f'_{parts[stock_code_idx + 3]}'&#10;        feature_names.append(feature_name)&#10;    return feature_names&#10;&#10;&#10;def save_ensemble_results_to_csv(ensemble_results, file_name='ensemble_results.csv'):&#10;    &quot;&quot;&quot;&#10;    将模型组合的测试结果保存到CSV文件中&#10;&#10;    Args:&#10;        ensemble_results: 包含模型组合测试结果的列表，每个元素是(ensemble_name, return, model_combo)的元组&#10;        file_name: 保存结果的CSV文件名&#10;    &quot;&quot;&quot;&#10;    # 转换ensemble_results为DataFrame&#10;    result_data = []&#10;    for name, ret, models in ensemble_results:&#10;        result_data.append({&#10;            'ensemble_name': name,&#10;            'return': ret,&#10;            'model_combo': str(models),&#10;            'timestamp': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')&#10;        })&#10;&#10;    result_df = pd.DataFrame(result_data)&#10;&#10;    result_df.to_csv(file_name, index=False)&#10;&#10;&#10;def train_all_and_ensemble(alpha_type, period, train_end_date, model_name):&#10;    &quot;&quot;&quot;使用ALL_HYPERPARAMETERS中的所有参数训练模型，然后创建三三组合的模型集成&quot;&quot;&quot;&#10;    logging.getLogger().info(f&quot;开始使用所有超参数训练模型 {model_name}&quot;)&#10;&#10;    # 加载数据&#10;    df = load_stock_data(alpha_type, config_info['market'])&#10;    factors = get_feature_names(alpha_type, config_info['market'], period)&#10;&#10;    # 准备训练数据&#10;    train_end = pd.to_datetime(train_end_date)&#10;    train_start = train_end - pd.DateOffset(years=config_info['train_year'])&#10;    train_start = train_start.replace(month=1, day=1)&#10;&#10;    # 检查是否有足够的模型&#10;    predictor = load_model(model_name)&#10;    if predictor is None:&#10;        logging.getLogger().info(f&quot;没有找到模型 {model_name}，开始训练所有超参数的模型&quot;)&#10;        predictor = train_model(df, factors, period, train_start, train_end, model_name)&#10;&#10;    if predictor is None or not predictor.is_fit:&#10;        logging.getLogger().error(f&quot;模型 {model_name} 训练失败&quot;)&#10;        return None, None&#10;&#10;    # 获取所有L1模型名称&#10;    all_models = predictor.model_names()&#10;    l1_models = [m for m in all_models if 'WeightedEnsemble' not in m]&#10;&#10;    logging.getLogger().info(f&quot;找到 {len(l1_models)} 个L1模型，开始创建三三组合模型&quot;)&#10;&#10;    # 获取测试数据范围&#10;    test_start_date = train_end + pd.DateOffset(days=1)&#10;    test_end_date = test_start_date + pd.DateOffset(days=365)&#10;&#10;    # 用于保存结果的数据结构&#10;    ensemble_results = []&#10;&#10;    all_combos = []&#10;    all_combos.extend(combinations(l1_models, 3))&#10;    for model_combo in all_combos:&#10;        ensemble_name = f&quot;Ensemble_{'_'.join(model_combo)}&quot;&#10;        logging.getLogger().info(f&quot;测试模型组合: {ensemble_name}&quot;)&#10;        if f'WeightedEnsemble_L2{ensemble_name}' not in all_models:&#10;            predictor.fit_weighted_ensemble(&#10;                base_models=list(model_combo),&#10;                name_suffix=ensemble_name)&#10;&#10;        try:&#10;            ret, stats, _ = test_model_stratergy(alpha_type,&#10;                                                 test_start_date.strftime('%Y%m%d'),&#10;                                                 test_end_date.strftime('%Y%m%d'),&#10;                                                 model_name, f'WeightedEnsemble_L2{ensemble_name}')&#10;            ensemble_results.append((ensemble_name, ret, model_combo))&#10;            logging.getLogger().info(f&quot;模型组合 {ensemble_name} 回报率: {ret}&quot;)&#10;            save_ensemble_results_to_csv(ensemble_results)&#10;        except Exception as e:&#10;            logging.getLogger().error(f&quot;测试模型组合 {ensemble_name} 失败: {str(e)}&quot;)&#10;&#10;    # 找出最佳组合&#10;    if ensemble_results:&#10;        best_ensemble = max(ensemble_results, key=lambda x: x[1])&#10;        logging.getLogger().info(f&quot;最佳模型组合: {best_ensemble[0]}&quot;)&#10;        logging.getLogger().info(f&quot;回报率: {best_ensemble[1]}&quot;)&#10;        logging.getLogger().info(f&quot;组成模型: {best_ensemble[2]}&quot;)&#10;        # 将最佳组合保存为单独的模型&#10;        best_model_path = f&quot;{model_name}_best_{best_ensemble[0]}&quot;&#10;        return best_model_path, best_ensemble&#10;    else:&#10;        logging.getLogger().error(&quot;没有成功测试任何模型组合&quot;)&#10;        return None, None&#10;&#10;&#10;def find_best_model_combination(year):&#10;    logging.getLogger().info(&quot;开始测试模型组合&quot;)&#10;&#10;    original_models_to_use = config_info['models_to_use'].copy() if isinstance(config_info['models_to_use'], list) else \&#10;        config_info['models_to_use'].split(',')&#10;    original_gbm_to_use = config_info['gbm_to_use'].copy() if isinstance(config_info['gbm_to_use'], list) else [int(x)&#10;                                                                                                                for x in&#10;                                                                                                                config_info[&#10;                                                                                                                    'gbm_to_use'].split(&#10;                                                                                                                    ',')]&#10;&#10;    all_model_options = []&#10;&#10;    if 'GBM' in original_models_to_use:&#10;        for gbm_version in original_gbm_to_use:&#10;            all_model_options.append(f'GBM{gbm_version}')&#10;&#10;    for model in original_models_to_use:&#10;        if model != 'GBM':&#10;            all_model_options.append(model)&#10;&#10;    logging.getLogger().info(f&quot;可用模型选项: {all_model_options}&quot;)&#10;&#10;    combinations_results = []&#10;    env_file = os.path.join(os.getcwd(), '.env')&#10;    backup_env(env_file)&#10;&#10;    all_combos = []&#10;    all_combos.extend(combinations(all_model_options, 3))&#10;    for combo in all_combos:&#10;        current_models = []&#10;        current_gbm_versions = []&#10;&#10;        for model in combo:&#10;            if model.startswith('GBM'):&#10;                current_models.append('GBM')&#10;                if len(model) &gt; 3:  # 如果有版本号&#10;                    current_gbm_versions.append(int(model[3:]))&#10;            else:&#10;                current_models.append(model)&#10;&#10;        current_models = list(set(current_models))&#10;        current_gbm_versions = list(set(current_gbm_versions))&#10;&#10;        config_info['models_to_use'] = current_models&#10;        if current_gbm_versions:&#10;            config_info['gbm_to_use'] = current_gbm_versions&#10;&#10;        write_env(current_gbm_versions, current_models, env_file)&#10;&#10;        prefix = &quot;_&quot;.join(combo)&#10;        prefix = f&quot;{prefix}_&quot;&#10;&#10;        logging.getLogger().info(f&quot;测试模型组合: {prefix}&quot;)&#10;        logging.getLogger().info(f&quot;当前模型设置: models_to_use={current_models}, gbm_to_use={current_gbm_versions}&quot;)&#10;&#10;        try:&#10;&#10;            ret, _ = current_best_backtrade(config_info['market'], prefix, start_year=year, end_year=year)&#10;            config_info['fixed_model'] = None&#10;            combo_result = (&#10;                prefix,&#10;                ret,&#10;                {&#10;                    'models': current_models,&#10;                    'gbm_versions': current_gbm_versions,&#10;                }&#10;            )&#10;            combinations_results.append(combo_result)&#10;&#10;            save_ensemble_results_to_csv(combinations_results, file_name='model_combinations_results.csv')&#10;&#10;            logging.getLogger().info(f&quot;组合 {prefix} 测试完成: 收益率={ret}&quot;)&#10;        except Exception as e:&#10;            logging.getLogger().error(f&quot;测试组合 {prefix} 时出错: {str(e)}&quot;)&#10;&#10;    config_info['models_to_use'] = original_models_to_use&#10;    config_info['gbm_to_use'] = original_gbm_to_use&#10;&#10;    restore_env(env_file)&#10;&#10;    if combinations_results:&#10;        best_combo = max(combinations_results, key=lambda x: x[1])&#10;        logging.getLogger().info(f&quot;最佳模型组合: {best_combo[0]}&quot;)&#10;        logging.getLogger().info(f&quot;最佳收益率: {best_combo[1]}&quot;)&#10;        logging.getLogger().info(f&quot;最佳组合配置: {best_combo[2]}&quot;)&#10;&#10;        config_info['models_to_use'] = best_combo[2]['models']&#10;        config_info['gbm_to_use'] = best_combo[2]['gbm_versions']&#10;&#10;        return best_combo&#10;    else:&#10;        logging.getLogger().error(&quot;没有成功测试任何模型组合&quot;)&#10;        return None&#10;&#10;&#10;def restore_env(env_file):&#10;    if os.path.exists(env_file):&#10;        os.remove(env_file)&#10;    backup_env_file = os.path.join(os.getcwd(), '.env.bak')&#10;    if os.path.exists(backup_env_file):&#10;        os.rename(backup_env_file, env_file)&#10;&#10;&#10;def backup_env(env_file):&#10;    if os.path.exists(env_file):&#10;        backup_env_file = os.path.join(os.getcwd(), '.env.bak')&#10;        with open(env_file, 'r') as f:&#10;            env_content = f.read()&#10;        with open(backup_env_file, 'w') as f:&#10;            f.write(env_content)&#10;&#10;&#10;def write_env(current_gbm_versions, current_models, env_file):&#10;    env_vars = {}&#10;    if os.path.exists(env_file):&#10;        with open(env_file, 'r') as f:&#10;            for line in f:&#10;                key, _, value = line.strip().partition('=')&#10;                env_vars[key] = value&#10;    # 更新或添加新的值&#10;    env_vars['models_to_use'] = ','.join(current_models)&#10;    env_vars['gbm_to_use'] = ','.join(map(str, current_gbm_versions))&#10;    # 写回 .env 文件&#10;    with open(env_file, 'w') as f:&#10;        for key, value in env_vars.items():&#10;            f.write(f&quot;{key}={value}\n&quot;)&#10;&#10;&#10;def strategy_runner_main():&#10;    &quot;&quot;&quot;主策略运行函数，解析参数并执行相应操作。&quot;&quot;&quot;&#10;    seed_everything(0)&#10;    parser = argparse.ArgumentParser()&#10;    parser.add_argument('-c', '--feature_count', type=int, help='feature count')&#10;    parser.add_argument('--afc', type=int, help='assist feature count')&#10;    parser.add_argument('-t', '--today', action='store_true', help='find stock for today')&#10;    parser.add_argument('-r', '--run_current_best', action='store_true', help='run current best strategy')&#10;    parser.add_argument('--run_all_sub_model', action='store_true', help='run all sub model')&#10;&#10;    parser.add_argument('--run_select_alpha', action='store_true', help='select alpha')&#10;    parser.add_argument('--get_returns', action='store_true', help='get_returns')&#10;    parser.add_argument('--evaluate_alpha', action='store_true', help='evaluate_alpha')&#10;    parser.add_argument('--run_cur_year', action='store_true', help='run cur year strategy')&#10;    parser.add_argument('--prepare_model', action='store_true', help='prepare model')&#10;    parser.add_argument('-i', '--compute_ic_table', action='store_true', help='compute ic table')&#10;    parser.add_argument('-g', '--generate_all', type=str, help='generate all data')&#10;    parser.add_argument('-o', '--hyperparameter_tune_off', action='store_true', help='hyperparameter_tune_off')&#10;    parser.add_argument('--cpu', action='store_true', help='cpu mode')&#10;    parser.add_argument('--rec', type=int, help='recommend mode')&#10;    parser.add_argument('--dis_weights421', action='store_true', help='disable weights 421')&#10;    parser.add_argument('-q', '--quality', type=str, help='quality')&#10;    parser.add_argument('-y', '--year', type=int, help='start year')&#10;    parser.add_argument('--end_year', type=int, help='end year')&#10;    parser.add_argument('--curve', type=float, help='curve')&#10;    parser.add_argument('--interval', type=int, help='interval')&#10;    parser.add_argument('--delay', type=int, help='delay')&#10;    parser.add_argument('-d', '--download', action='store_true', help='download data')&#10;    parser.add_argument('--alpha_type', type=str, help='alpha type')&#10;    parser.add_argument('--fixed_model', type=str, help='fixed model name')&#10;    parser.add_argument('--assist_alpha_type', type=str, help='assist alpha type')&#10;    parser.add_argument('--cls_type', action='store_true', help='use cls type')&#10;    parser.add_argument('--assist_cls_type', action='store_true', help='use assist cls type')&#10;    parser.add_argument('--fc_res', type=str, help='fc_res path')&#10;    parser.add_argument('-m', '--mail', type=str, help='mail address')&#10;    parser.add_argument('--market', type=str, help='market')&#10;    parser.add_argument('--model', type=str, help='model name')&#10;    parser.add_argument('--alpha', type=str, help='model name')&#10;    parser.add_argument('--assist_model', type=str, help='assist model name')&#10;    parser.add_argument('--ret_of_day', type=str, help='return of day')&#10;    parser.add_argument('--ret_of_range', type=str, help='return of range')&#10;    parser.add_argument('--relative', action='store_true', help='relative return of range')&#10;    parser.add_argument('--topn', type=int, help='topn')&#10;    parser.add_argument('--tuning_days', type=int, help='tuning_days')&#10;    parser.add_argument('--period_n', type=int, help='period_n')&#10;    parser.add_argument('--assist_period_n', type=int, help='assist_period_n')&#10;    parser.add_argument('--train', type=str, help='train model')&#10;    parser.add_argument('--auto_feature_select', action='store_true', help='auto_feature_select')&#10;    parser.add_argument('--refine', type=str, help='refine data')&#10;    parser.add_argument('--train_year', type=int, help='train year')&#10;    parser.add_argument('--prefix', type=str, help='prefix')&#10;    parser.add_argument('--select_alpha', type=str, help='select_alpha')&#10;    parser.add_argument('--export', action='store_true', help='export data')&#10;    parser.add_argument('--evaluate', action='store_true', help='evaluate')&#10;    parser.add_argument('--notify_by_mail', action='store_true', help='notify by mail')&#10;    parser.add_argument('--train_all_ensemble', action='store_true', help='使用所有超参数训练模型并创建三三组合集成')&#10;    parser.add_argument('--find_best_comb', action='store_true', help='find_best_comb')&#10;    parser.add_argument('--draw_weights', action='store_true', help='draw weight vs return scatter plot')&#10;    if len(sys.argv) == 1:&#10;        parser.print_help()&#10;        sys.exit(1)&#10;    args = parser.parse_args()&#10;&#10;    # 初始化&#10;    init_qlib()&#10;    init_logger()&#10;    init_gpt()&#10;&#10;    # 配置参数&#10;    config_info['download_data'] = args.download&#10;    config_info['recommend_mode'] = config_info['download_data']&#10;    config_info['fixed_model'] = args.fixed_model&#10;    start_test_year = args.year if args.year is not None else 2020&#10;    end_test_year = args.end_year if args.end_year is not None else datetime.date.today().year&#10;    if args.curve is not None:&#10;        config_info['curve'] = args.curve&#10;    if args.market:&#10;        config_info['market'] = args.market&#10;    if args.notify_by_mail:&#10;        config_info['notify_by_mail'] = True&#10;    fc_res_path = args.fc_res + '.csv' if args.fc_res is not None else 'fc_res.csv'&#10;&#10;    interval = args.interval if args.interval is not None else config_info['interval']&#10;    config_info['interval'] = interval&#10;    delay = args.delay if args.delay is not None else config_info['train_delay']&#10;    config_info['train_delay'] = delay&#10;&#10;    if args.export:&#10;        dt = pd.read_pickle(f'refined_a458_{config_info[&quot;market&quot;]}.pkl')&#10;        dt = dt[dt.index.get_level_values(0) &gt;= '20231001']&#10;        dt.to_csv(f'refined_a458_{config_info[&quot;market&quot;]}.csv')&#10;        return&#10;&#10;    if args.model is not None:&#10;        config_info['model_name'] = args.model&#10;        config_info['assist_model_name'] = None&#10;&#10;    if args.assist_model is not None:&#10;        config_info['assist_model_name'] = args.assist_model&#10;&#10;    if args.market is not None and args.model is None:&#10;        config_info['model_name'] = modify_model_name(config_info['model_name'])&#10;        if config_info['assist_model_name']:&#10;            config_info['assist_model_name'] = modify_model_name(config_info['assist_model_name'])&#10;&#10;    config_info['weights421'] = not args.dis_weights421&#10;&#10;    if args.train_year is not None:&#10;        config_info['train_year'] = args.train_year&#10;&#10;    if args.auto_feature_select:&#10;        config_info['auto_feature_select'] = True&#10;&#10;    if args.hyperparameter_tune_off:&#10;        config_info['hyperparameter_tune_kwargs'] = False&#10;&#10;    if args.mail is not None:&#10;        config_info['mail'] = args.mail&#10;&#10;    prefix = args.prefix if args.prefix is not None else ''&#10;&#10;    if args.topn is not None:&#10;        config_info['topn'] = args.topn&#10;&#10;    if args.period_n is not None:&#10;        config_info['assist_period_n'] = config_info['period_n'] = args.period_n&#10;    if args.assist_period_n is not None:&#10;        config_info['assist_period_n'] = args.assist_period_n&#10;&#10;    if args.tuning_days is not None:&#10;        config_info['tuning_days'] = args.tuning_days&#10;&#10;    if args.quality is not None:&#10;        config_info['presets'] = args.quality&#10;&#10;    if args.cpu:&#10;        config_info['cpu'] = True&#10;&#10;    if args.feature_count is not None:&#10;        config_info['feature_count'] = args.feature_count&#10;        config_info['assist_feature_count'] = args.feature_count&#10;&#10;    if args.afc is not None:&#10;        config_info['assist_feature_count'] = args.afc&#10;&#10;    if args.alpha_type is not None:&#10;        config_info['alpha_type'] = args.alpha_type&#10;&#10;    if args.assist_alpha_type is not None:&#10;        config_info['assist_alpha_type'] = args.assist_alpha_type&#10;&#10;    if args.cls_type:&#10;        config_info['train_type'] = 'cls'&#10;&#10;    if args.assist_cls_type:&#10;        config_info['assist_train_type'] = 'cls'&#10;&#10;    if args.refine is not None:&#10;        gates = ast.literal_eval(args.refine)&#10;        df = pd.read_pickle(f'refined_{config_info[&quot;alpha_type&quot;]}_{config_info[&quot;market&quot;]}.pkl')&#10;        df = refine_stock_df(df, gates)&#10;        df.to_pickle(f'refined_{config_info[&quot;alpha_type&quot;]}_{config_info[&quot;market&quot;]}.pkl')&#10;&#10;    if args.evaluate:&#10;        evaluate_model(config_info['alpha_type'], config_info['period_n'],&#10;                       f'{start_test_year}0101', f'{end_test_year}1231', config_info['model_name'])&#10;&#10;    if args.generate_all:&#10;        date = datetime.date.today() if args.generate_all == 'today' \&#10;            else datetime.datetime.strptime(args.generate_all, '%Y%m%d').date()&#10;        fc = config_info['feature_count']&#10;        config_info['generate_all_features'] = True&#10;        config_info['feature_count'] = MAX_FEATURE_COUNT&#10;        prepare_data_for_all_year(end_date=date)&#10;        config_info['feature_count'] = fc&#10;        if config_info['alpha_158']:&#10;            logging.getLogger().info(&quot;开始处理alpha_158数据准备&quot;)&#10;            prepare_alpha158_data(datetime.date(2008, 1, 1), date)&#10;            logging.getLogger().info(&quot;正在与a300数据合并&quot;)&#10;            combined_with_a300()&#10;            logging.getLogger().info(&quot;Alpha_158数据准备完成&quot;)&#10;        else:&#10;            logging.getLogger().info(f&quot;正在加载精炼数据: refined_{config_info['alpha_type']}_{config_info['market']}.pkl&quot;)&#10;            df = pd.read_pickle(f'refined_{config_info[&quot;alpha_type&quot;]}_{config_info[&quot;market&quot;]}.pkl')&#10;            logging.getLogger().info(f&quot;正在精炼股票数据框，共{len(df)}行&quot;)&#10;            df = refine_stock_df(df, with_flag=False)&#10;            logging.getLogger().info(f&quot;正在保存精炼数据到pickle文件&quot;)&#10;            df.to_pickle(f'refined_{config_info[&quot;alpha_type&quot;]}_{config_info[&quot;market&quot;]}.pkl')&#10;&#10;        if os.path.exists('basic_data.pkl'):&#10;            logging.getLogger().info(&quot;发现basic_data.pkl文件，开始合并流程&quot;)&#10;            logging.getLogger().info(f&quot;正在加载refined_a458_{config_info['market']}.pkl&quot;)&#10;            df = pd.read_pickle(f'refined_a458_{config_info[&quot;market&quot;]}.pkl')&#10;            logging.getLogger().info(f&quot;正在加载basic_data.pkl&quot;)&#10;            basic_info = pd.read_pickle('basic_data.pkl')&#10;            &#10;            # 给basic_info的所有列名添加basic_前缀&#10;            basic_info.columns = [f'basic_{col}' for col in basic_info.columns]&#10;            logging.getLogger().info(f&quot;已为basic_info的列名添加basic_前缀，列名: {list(basic_info.columns)}&quot;)&#10;            &#10;            logging.getLogger().info(f&quot;正在合并数据框: df形状{df.shape}, basic_info形状{basic_info.shape}&quot;)&#10;            df = pd.merge(df, basic_info, how='left', left_index=True, right_index=True)&#10;            logging.getLogger().info(f&quot;合并完成，最终形状: {df.shape}&quot;)&#10;            df.to_pickle(f'refined_a458_{config_info[&quot;market&quot;]}.pkl')&#10;&#10;        # 合并市场状态数据&#10;        regime_file = f'{config_info[&quot;market&quot;]}_regimeID.csv'&#10;        if os.path.exists(regime_file):&#10;            logging.getLogger().info(f&quot;发现市场状态文件: {regime_file}，开始合并市场状态数据&quot;)&#10;            logging.getLogger().info(f&quot;正在加载refined_a458_{config_info['market']}.pkl用于状态合并&quot;)&#10;            df = pd.read_pickle(f'refined_a458_{config_info[&quot;market&quot;]}.pkl')&#10;            logging.getLogger().info(f&quot;正在从{regime_file}加载市场状态数据&quot;)&#10;            regime_data = pd.read_csv(regime_file, parse_dates=['date'])&#10;            logging.getLogger().info(f&quot;市场状态数据已加载，共{len(regime_data)}行，日期范围: {regime_data['date'].min()} 到 {regime_data['date'].max()}&quot;)&#10;            regime_data.set_index('date', inplace=True)&#10;&#10;            # 将df重置索引以便合并&#10;            logging.getLogger().info(&quot;正在重置df索引以进行合并操作&quot;)&#10;            df_reset = df.reset_index()&#10;            # 合并regime数据，基于date列&#10;            logging.getLogger().info(f&quot;正在合并市场状态数据: df形状{df_reset.shape}, 状态数据形状{regime_data.shape}&quot;)&#10;            df_merged = pd.merge(df_reset, regime_data, left_on='date', right_index=True, how='left')&#10;            logging.getLogger().info(f&quot;市场状态合并完成，合并后形状: {df_merged.shape}&quot;)&#10;            # 恢复原始索引&#10;            logging.getLogger().info(&quot;正在恢复原始多重索引&quot;)&#10;            df_merged.set_index(['date', 'sec_id'], inplace=True)&#10;            df_merged.to_pickle(f'refined_a458_{config_info[&quot;market&quot;]}.pkl')&#10;            logging.getLogger().info(&quot;市场状态数据合并完成并已保存&quot;)&#10;        else:&#10;            logging.getLogger().info(f&quot;未找到市场状态文件{regime_file}，跳过市场状态数据合并&quot;)&#10;&#10;&#10;    elif args.compute_ic_table:&#10;        start_year_ic = int(args.year)&#10;        end_year_ic = int(args.end_year)&#10;        for year in range(start_year_ic, end_year_ic + 1):&#10;            df = load_stock_data(config_info['alpha_type'], config_info['market'])&#10;            end = datetime.datetime(year, 12, 31)&#10;            start = end - datetime.timedelta(days=365 * args.train_year)&#10;            end = pd.to_datetime(end)&#10;            start = pd.to_datetime(start)&#10;&#10;            # Load existing IC file if it exists&#10;            ic_file_path = f'{config_info[&quot;alpha_type&quot;]}_{config_info[&quot;market&quot;]}_ic_{year}.csv'&#10;            if os.path.exists(ic_file_path):&#10;                existing_ic = pd.read_csv(ic_file_path, index_col=0)&#10;            else:&#10;                existing_ic = None&#10;            ic = compute_ic_table_for_data(df, start, end,&#10;                                           existing_ic.index.to_list() if existing_ic is not None else None)&#10;&#10;            # Merge with existing results if they exist&#10;            if existing_ic is not None:&#10;                ic = pd.concat([existing_ic, ic])&#10;&#10;            # Sort by feature sequence if available&#10;            try:&#10;                with open('feature_seq.txt', 'r') as file:&#10;                    index_order = [line.strip() for line in file]&#10;                    leftover_index = ic.index.difference(index_order)&#10;                    new_index = list(index_order) + list(leftover_index)&#10;                    ic = ic.reindex(new_index)&#10;            except FileNotFoundError:&#10;                pass&#10;&#10;            ic.to_csv(ic_file_path)&#10;&#10;    if args.run_current_best:&#10;        current_best_backtrade(start_year=start_test_year, end_year=end_test_year,&#10;                               save_path=fc_res_path, prefix=prefix)&#10;    if args.run_all_sub_model:&#10;        run_all_sub_model(f'{args.year}0101', f'{args.year}1231', args.model)&#10;    if args.prepare_model:&#10;        find_best_random_trained_model(start_year=start_test_year, end_year=end_test_year, prefix=prefix)&#10;&#10;    if args.get_returns:&#10;        if config_info['interval'] &gt;= 250:&#10;            get_returns_for_model_series_by_year(start_test_year, end_test_year, prefix, args.select_alpha)&#10;        else:&#10;            get_returns_for_model_series_by_season(prefix)&#10;&#10;    if args.run_select_alpha:&#10;        if os.path.exists('fc_res_prev.csv'):&#10;            feature_names = get_feature_names_from_fc_res('fc_res_prev.csv')&#10;            from_prev = True&#10;        else:&#10;            feature_names = get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'],&#10;                                              1000)&#10;            from_prev = False&#10;&#10;        if os.path.exists(fc_res_path):&#10;            exists_feature_names = get_feature_names_from_fc_res(fc_res_path)&#10;        else:&#10;            exists_feature_names = []&#10;&#10;        for fn in feature_names:&#10;            if fn in exists_feature_names:&#10;                continue&#10;            print('-------------------', fn)&#10;            config_info['added_alpha_for_stratergy'] = fn&#10;            current_best_backtrade(start_year=start_test_year, end_year=end_test_year, save_path=fc_res_path,&#10;                                   prefix=prefix)&#10;            config_info['fixed_model'] = None&#10;&#10;            if not from_prev:&#10;                config_info['added_alpha_for_stratergy'] = f'_{fn}'&#10;                current_best_backtrade(start_year=start_test_year, end_year=end_test_year, save_path=fc_res_path,&#10;                                       prefix=prefix)&#10;                config_info['fixed_model'] = None&#10;&#10;    if args.evaluate_alpha:&#10;        research_alpha(f'{args.year}0101', f'{args.year}1231', args.alpha, args.model)&#10;&#10;    if args.run_cur_year:&#10;        run_current_year_backtrade(prefix)&#10;&#10;    if args.rec is not None:&#10;        if os.path.exists('dibeili_set.pkl'):&#10;            config_info['interest'] = config_info['interest'] + pickle.load(open('dibeili_set.pkl', 'rb'))&#10;        recommend_for_today(with_gpt=(args.rec == 1), topn=10)&#10;    elif args.ret_of_day is not None:&#10;        ret = get_return_infos_of_range(args.ret_of_day, args.ret_of_day, config_info['model_name'],&#10;                                        topn=config_info['topn'],&#10;                                        assist_model_name=config_info['assist_model_name'])&#10;        print(ret)&#10;        print(ret.mean())&#10;    elif args.ret_of_range is not None:&#10;        days = args.ret_of_range.split('-')&#10;        ret = get_return_infos_of_range(days[0], days[1], config_info['model_name'], topn=config_info['topn'],&#10;                                        assist_model_name=config_info['assist_model_name'])&#10;        print_ret_range_info(ret)&#10;        if 'assist_return' in ret.columns:&#10;            ret = ret[ret['assist_return'] &gt; 0.6]&#10;            print('---------------return detail of assisted model --------------------')&#10;            print_ret_range_info(ret)&#10;    if args.train:&#10;        train_date = pd.to_datetime(str(args.train))&#10;        train_date = train_date - datetime.timedelta(days=1)&#10;        train(config_info['alpha_type'], config_info['period_n'], train_date, config_info['model_name'])&#10;&#10;    if args.find_best_comb:&#10;        ret = find_best_model_combination(start_test_year)&#10;        logging.info(ret)&#10;&#10;    if args.train_all_ensemble:&#10;        end_date = args.end_year if args.end_year is not None else datetime.date.today().year&#10;        end_date = datetime.date(end_date, 12, 31)&#10;        train_all_and_ensemble(&#10;            config_info['alpha_type'],&#10;            config_info['period_n'],&#10;            end_date,&#10;            config_info['model_name']&#10;        )&#10;&#10;    if args.draw_weights:&#10;        draw_weights_vs_returns(start_test_year, end_test_year)&#10;&#10;&#10;if __name__ == '__main__':&#10;    strategy_runner_main()" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/util.py">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/util.py" />
              <option name="originalContent" value="import datetime&#10;import logging&#10;import os&#10;import random&#10;import smtplib&#10;import warnings&#10;from email.header import Header&#10;from email.mime.application import MIMEApplication&#10;from email.mime.multipart import MIMEMultipart&#10;from email.mime.text import MIMEText&#10;&#10;import alphalens&#10;import backtrader as bt&#10;import matplotlib.pyplot as plt&#10;import mplfinance as mpf&#10;import numpy as np&#10;import openai&#10;import pandas as pd&#10;import qlib&#10;from alphalens import performance as perf&#10;from alphalens.utils import MaxLossExceededError&#10;from autogluon.core.metrics import make_scorer&#10;from autogluon.tabular import TabularPredictor&#10;from langchain.chat_models import AzureChatOpenAI&#10;from langchain.embeddings import OpenAIEmbeddings&#10;from numba import njit, prange&#10;from qlib.data import D&#10;from scipy.stats import zscore, pearsonr&#10;from sklearn.linear_model import LinearRegression&#10;from sklearn.metrics import confusion_matrix&#10;from sklearn.metrics import mean_squared_error&#10;from sklearn.metrics import r2_score&#10;import tqdm&#10;&#10;import configs&#10;from configs import config_info, convert_to_csi_code&#10;&#10;warnings.filterwarnings('ignore')&#10;&#10;global cache&#10;cache = {}&#10;qlib_inited = False&#10;MAX_FEATURE_COUNT = 1000&#10;&#10;MAX_TIME_SUB_MODEL=2000&#10;&#10;ALL_HYPERPARAMETERS = {&#10;    'GBM': [&#10;        {&#10;            'seed': 0,&#10;            'bagging_seed': 0,&#10;            'feature_fraction_seed': 0,&#10;            'data_random_seed': 0,&#10;            'feature_fraction': 0.9,&#10;            &quot;learning_rate&quot;: 0.03,&#10;            &quot;num_leaves&quot;: 128,&#10;            &quot;min_data_in_leaf&quot;: 5,&#10;            'ag_args': dict(model_type=&quot;GBM&quot;, name_suffix=&quot;Large&quot;, priority=0),&#10;        },&#10;        {&#10;            &quot;extra_trees&quot;: True,&#10;            'seed': 0,&#10;            'bagging_seed': 0,&#10;            'feature_fraction_seed': 0,&#10;            'drop_seed': 0,&#10;            'extra_seed': 0,&#10;            'objective_seed': 0,&#10;            'data_random_seed': 0,&#10;            'bagging_fraction': 1.0,&#10;            'feature_fraction': 1.0,&#10;            'bagging_freq': 0,&#10;            &quot;ag_args&quot;: {&quot;name_suffix&quot;: &quot;XT&quot;, &quot;priority&quot;: 0},&#10;            'early_stopping_round' : 100,&#10;            'force_col_wise': True,&#10;        },&#10;        {&#10;            'seed': 0,&#10;            'bagging_seed': 0,&#10;            'feature_fraction_seed': 0,&#10;            'data_random_seed': 0,&#10;        },&#10;    ],&#10;    'XGB': {&#10;        'seed': 0,&#10;    },&#10;    &quot;CAT&quot;: {&#10;        'random_seed': 0,&#10;    },&#10;    &quot;FASTAI&quot;: {&#10;        'seed': 0,&#10;    },&#10;    &quot;NN_TORCH&quot;: {&#10;    },&#10;    &quot;RF&quot;: {&#10;        &quot;criterion&quot;: &quot;squared_error&quot;,&#10;        &quot;random_state&quot;: 0,&#10;        &quot;ag_args&quot;: {&quot;name_suffix&quot;: &quot;MSE&quot;, &quot;problem_types&quot;: [&quot;regression&quot;, &quot;quantile&quot;]}&#10;    },&#10;    &quot;XT&quot;: {&#10;        &quot;criterion&quot;: &quot;squared_error&quot;,&#10;        &quot;random_state&quot;: 0,&#10;        &quot;ag_args&quot;: {&quot;name_suffix&quot;: &quot;MSE&quot;, &quot;problem_types&quot;: [&quot;regression&quot;, &quot;quantile&quot;]},&#10;    },&#10;    &quot;KNN&quot;: [{&quot;weights&quot;: &quot;uniform&quot;, &quot;ag_args&quot;: {&quot;name_suffix&quot;: &quot;Unif&quot;}}],&#10;}&#10;&#10;&#10;class AnnualizedSortinoRatio(bt.Analyzer):&#10;    params = (&#10;        ('riskfreerate', 0.0),  # 年化无风险收益率，例如 0.03 表示 3%&#10;        ('trading_days', 252),  # 一年中的交易日数量&#10;    )&#10;&#10;    def start(self):&#10;        self.returns = []  # 用于存储每日收益率&#10;        # 记录回测开始时的账户总值&#10;        self.prev_value = self.strategy.broker.getvalue()&#10;&#10;    def next(self):&#10;        current_value = self.strategy.broker.getvalue()&#10;        # 计算当期（每日）的收益率&#10;        # daily_ret 为当天收益率 = 当天账户价值 / 前一天账户价值 - 1&#10;        if self.prev_value != 0:&#10;            daily_ret = (current_value / self.prev_value) - 1.0&#10;            self.returns.append(daily_ret)&#10;        self.prev_value = current_value  # 更新前一日账户价值&#10;&#10;    def stop(self):&#10;        returns = np.array(self.returns)&#10;        # 将年化的无风险收益率转换为每日无风险收益率（简单除以交易日数量）&#10;        daily_rf = self.p.riskfreerate / self.p.trading_days&#10;        # 每日超额收益 = 每日实际收益 - 每日无风险收益率&#10;        excess_returns = returns - daily_rf&#10;&#10;        # 年化平均超额收益率 = 日均超额收益 * 交易日数&#10;        annualized_excess_return = np.mean(excess_returns) * self.p.trading_days&#10;&#10;        # 只选取亏损的那部分收益（超额收益为负的部分）&#10;        downside_excess = excess_returns[excess_returns &lt; 0]&#10;        if downside_excess.size == 0:&#10;            annualized_downside_std = 0.0&#10;        else:&#10;            # 年化下行波动率 = 日下行波动率 * sqrt(交易日数)&#10;            annualized_downside_std = np.std(downside_excess) * np.sqrt(self.p.trading_days)&#10;&#10;        if annualized_downside_std == 0:&#10;            sortino = float('inf')&#10;        else:&#10;            sortino = annualized_excess_return / annualized_downside_std&#10;&#10;        # 将计算结果存入 self.rets&#10;        self.rets = {'sortino': sortino}&#10;&#10;    def get_analysis(self):&#10;        return self.rets&#10;&#10;&#10;def sign_step(x):&#10;    return x if x &gt; 0 else 0&#10;&#10;&#10;def init_qlib():&#10;    global qlib_inited&#10;    if not qlib_inited:&#10;        configs.init_config()&#10;        qlib.init(provider_uri=config_info['qlib_url'])&#10;        qlib_inited = True&#10;&#10;    if os.path.exists(&quot;interest.txt&quot;):&#10;        with open(&quot;interest.txt&quot;, &quot;r&quot;) as f:&#10;            interest_str = f.read()&#10;        interest_list = interest_str.split(&quot;\n&quot;)&#10;        interest_list = [x for x in interest_list if x]&#10;        config_info['interest'] = interest_list&#10;&#10;&#10;def create_llm(model='deepseek-chat'):&#10;    # llm = ChatOpenAI(openai_api_base='https://api.deepseek.com/v1',&#10;    #                  openai_api_key=&quot;sk-0c9e855553a04ef2afcb675bea3d5dc9&quot;,&#10;    #                  model=model)&#10;    llm = AzureChatOpenAI(deployment_name=&quot;gpt-4o&quot;, openai_api_version=&quot;2024-05-01-preview&quot;,&#10;                          model_name='gpt-4o')&#10;    return llm&#10;&#10;&#10;def create_embedding():&#10;    embeddings = OpenAIEmbeddings(&#10;        deployment=&quot;text-embedding-ada-002&quot;,&#10;        model=&quot;text-embedding-ada-002&quot;,&#10;        chunk_size=1,&#10;    )&#10;    return embeddings&#10;&#10;&#10;def init_gpt():&#10;    openai.api_type = os.getenv('OPENAI_API_TYPE')&#10;    openai.api_base = os.getenv('OPENAI_API_BASE')&#10;    openai.api_key = os.getenv('OPENAI_API_KEY')&#10;    openai.api_version = os.getenv('OPENAI_API_VERSION')&#10;&#10;    if openai.api_key:&#10;        os.environ[&quot;OPENAI_API_KEY&quot;] = openai.api_key&#10;    if openai.api_type:&#10;        os.environ['OPENAI_API_TYPE'] = openai.api_type&#10;    if openai.api_base:&#10;        os.environ['OPENAI_API_BASE'] = openai.api_base&#10;    if openai.api_version:&#10;        os.environ['OPENAI_API_VERSION'] = openai.api_version&#10;&#10;    serpapi_api_key = os.getenv('SERPAPI_API_KEY')&#10;    if serpapi_api_key:&#10;        os.environ[&quot;SERPAPI_API_KEY&quot;] = serpapi_api_key&#10;&#10;&#10;def predict_proba(model, test_data, sub_model_name=None):&#10;    if config_info['train_type'] == 'reg':&#10;        predictions = model.predict(test_data, model=sub_model_name)&#10;        &#10;        if 'return' in test_data.columns:&#10;            y_true = test_data['return']&#10;            # 过滤掉NaN值&#10;            valid_mask = ~(pd.isna(y_true) | pd.isna(predictions))&#10;            if valid_mask.sum() &gt; 0:&#10;                y_true_valid = y_true[valid_mask]&#10;                predictions_valid = predictions[valid_mask]&#10;                r2 = r2_score(y_true_valid, predictions_valid)&#10;                logging.getLogger().info(f&quot;R² Score: {r2:.4f}&quot;)&#10;                &#10;                ic_corr, ic_p_value = pearsonr(predictions_valid, y_true_valid)&#10;                logging.getLogger().info(f&quot;IC Score: {ic_corr:.4f}&quot;)&#10;        return predictions&#10;    else:&#10;        return model.predict_proba(test_data, model=sub_model_name)&#10;&#10;&#10;def clear_cache():&#10;    cache.clear()&#10;&#10;&#10;def load_stock_data(alpha_type, market, prefix=''):&#10;    file_name = f'refined_{prefix}{alpha_type}_{market}.pkl'&#10;    if cache.get(file_name) is None:&#10;        df = pd.read_pickle(file_name)&#10;        df = df.loc[:, ~df.columns.duplicated()]&#10;        cache[file_name] = df&#10;&#10;    ret = cache.get(file_name)&#10;    if len(config_info['secid_filter_train']) &gt; 0:&#10;        ret = ret[ret.index.get_level_values(1).isin(config_info['secid_filter_train'])]&#10;    return ret&#10;&#10;&#10;def get_flag_by_gates(changes, gates):&#10;    flags = changes.apply(lambda row: (row &gt;= row.quantile(gates)).astype(int), axis=1)&#10;    return flags&#10;&#10;&#10;def refine_stock_df(df, gates=[(1, 0.9), (5, 0.9), (10, 0.9), (20, 0.9)], with_flag=True):&#10;    if config_info['index_enhance']:&#10;        index_path = f'./{config_info[&quot;market&quot;]}.csv'&#10;        stock_index = pd.read_csv(index_path, index_col=0)&#10;        stock_index.index = pd.to_datetime(stock_index.index)&#10;        stock_index = stock_index[['close']].rename(columns={'close': 'index_close'}).reset_index()&#10;        df_reset = df.reset_index()&#10;        df = pd.merge(df_reset, stock_index, on='date', how='left')&#10;        df.set_index(['date', 'sec_id'], inplace=True)&#10;&#10;    df.reset_index(inplace=True)&#10;    if 'return' in df.columns:&#10;        returns = df.pivot(index='date', columns='sec_id', values='return')&#10;    else:&#10;        returns = df.pivot(index='date', columns='sec_id', values='return_1D')&#10;    close = df.pivot(index='date', columns='sec_id', values='close')&#10;&#10;    if 'index_close' in df.columns:&#10;        index_close = df.pivot(index='date', columns='sec_id', values='index_close')&#10;    else:&#10;        index_close = None&#10;&#10;    for i, j in gates:&#10;        if i != 1:&#10;            changes = close.pct_change(i)&#10;            if index_close is not None:&#10;                index_changes = index_close.pct_change(i)&#10;        else:&#10;            changes = returns.copy()&#10;            if index_close is not None:&#10;                index_changes = index_close.pct_change(1)&#10;&#10;        if f'return_{i}D' in df.columns:&#10;            df.drop(columns=[f'return_{i}D'], inplace=True)&#10;        if f'flag_{i}D' in df.columns:&#10;            df.drop(columns=[f'flag_{i}D'], inplace=True)&#10;&#10;        changes = changes.shift(-i)&#10;        if index_close is not None:&#10;            index_changes = index_changes.shift(-i)&#10;            changes = changes - index_changes&#10;            daily_std = changes.std(axis=1, skipna=True)&#10;            mean_std = daily_std.mean()&#10;            changes = changes.div(daily_std, axis=0) * mean_std&#10;            clip_range = config_info['max_return'][i] * 2&#10;            changes = changes.clip(-clip_range, clip_range)&#10;            # 打印changes的统计信息&#10;            print(f'return_{i}D changes统计信息:')&#10;            print(f'  形状: {changes.shape}')&#10;            print(f'  均值: {changes.mean().mean():.6f}')&#10;            print(f'  标准差: {changes.std().mean():.6f}')&#10;            print(f'  最小值: {changes.min().min():.6f}')&#10;            print(f'  最大值: {changes.max().max():.6f}')&#10;            print(f'  daily_std均值: {mean_std:.6f}')&#10;            print(f'  daily_std标准差: {daily_std.std():.6f}')&#10;&#10;        if with_flag:&#10;            print(f'flag_{i}D')&#10;            flags = get_flag_by_gates(changes, j)&#10;            flags = flags.reset_index().melt(id_vars='date', var_name='sec_id', value_name='return')&#10;            print(flags['return'].value_counts() / len(flags))&#10;            flags.rename(columns={'return': f'flag_{i}D'}, inplace=True)&#10;&#10;        changes = changes.reset_index().melt(id_vars='date', var_name='sec_id', value_name='return')&#10;        changes.rename(columns={'return': f'return_{i}D'}, inplace=True)&#10;        df = df.merge(changes, on=['date', 'sec_id'], how='left')&#10;&#10;        if with_flag:&#10;            df = df.merge(flags, on=['date', 'sec_id'], how='right')&#10;&#10;    if 'return' in df.columns:&#10;        df.dropna(subset=['return'], inplace=True)&#10;    else:&#10;        df.dropna(subset=['return_1D'], inplace=True)&#10;        df['return'] = df['return_1D']&#10;&#10;    if index_close is not None:&#10;        df.drop(columns=['index_close'], inplace=True)&#10;    df.set_index(['date', 'sec_id'], inplace=True)&#10;    return df&#10;&#10;&#10;def refine_stock_data(alpha_type, prefix=''):&#10;    df = pd.read_pickle(f'{prefix}{alpha_type}_{config_info[&quot;market&quot;]}.pkl')&#10;    df = refine_stock_df(df)&#10;    df.to_pickle(f'refined_{prefix}{alpha_type}_{config_info[&quot;market&quot;]}.pkl')&#10;&#10;&#10;def get_feature_names(alpha_type, market, period_n, feature_count=-1):&#10;    if period_n &gt; 10:&#10;        period_n = 10&#10;    year = config_info.get('ic_year')&#10;    ic_file = f'{alpha_type}_{market}_ic_{year}.csv'&#10;    if not os.path.exists(ic_file):&#10;        ic_file = f'{alpha_type}_{market}_ic.csv'&#10;    if not os.path.exists(ic_file):&#10;        return []&#10;    ic_table = pd.read_csv(ic_file, index_col=0)&#10;    rank_1d = abs(ic_table.loc[:, f'{period_n}D']).rank(ascending=False)&#10;    feature_abandon = f'feature_abandon_{year}' if os.path.exists(f'feature_abandon_{year}') else &quot;feature_abandon&quot;&#10;    if not config_info['decorrelated_top_features'] and config_info['remove_from_fid'] &gt; 0 and os.path.exists(f&quot;feature_importance.csv&quot;):&#10;        df = pd.read_csv(f&quot;feature_importance.csv&quot;, index_col=0)&#10;        df = df.sort_values(by='p99_high', ascending=True)&#10;        df = df.iloc[config_info['remove_from_fid']:]&#10;        selected_features = df.index.tolist()&#10;        if config_info['use_basic_factors']:&#10;&#10;        return selected_features&#10;    elif os.path.exists(feature_abandon):&#10;        with open(feature_abandon, &quot;r&quot;) as f:&#10;            abandoned = [line.strip() for line in f if line.strip()]&#10;            rank_1d = rank_1d[~rank_1d.index.isin(abandoned)]&#10;    if feature_count &lt;= 0:&#10;        feature_count = config_info['feature_count']&#10;    factor_names = rank_1d[rank_1d &lt;= feature_count].index.tolist()&#10;    if config_info['index_feature_count'] &gt; 0:&#10;        factor_names.extend([f'index_feature{i}' for i in range(1, config_info['index_feature_count'] + 1)])&#10;    return factor_names&#10;&#10;&#10;def reshape_daily_stock_data(stock_code, stock_df, save_path):&#10;    stock_df.rename(columns={'开盘': 'open', '收盘': 'close', '最高': 'high', '最低': 'low', '成交量': 'volume',&#10;                             '成交额': 'amount', '换手率': 'rate', '日期': 'date'}, inplace=True)&#10;    stock_df.drop(columns=['振幅', '涨跌幅', '涨跌额'], inplace=True)&#10;    stock_df.to_csv(f'./{save_path}/{stock_code}.csv')&#10;    stock_df['date'] = pd.to_datetime(stock_df['date'])&#10;    stock_df.set_index('date', inplace=True)&#10;    return stock_df&#10;&#10;&#10;def compute_alphalen_factor(returns, factor_name, stock_data):&#10;    feature = stock_data.loc[:, factor_name]&#10;&#10;    try:&#10;        factors = alphalens.utils.get_clean_factor_and_forward_returns(feature, returns,&#10;                                                                       bins=None,&#10;                                                                       periods=(1, 5, 10), quantiles=5,&#10;                                                                       max_loss=0.35)&#10;    except MaxLossExceededError as e:&#10;        print(e)&#10;        print('fall back to binn mode')&#10;        min_f, max_f = min(feature), max(feature)&#10;        try:&#10;            factors = alphalens.utils.get_clean_factor_and_forward_returns(feature, returns,&#10;                                                                           bins=np.linspace(min_f, max_f, 5),&#10;                                                                           periods=(1, 5, 10), quantiles=None,&#10;                                                                           max_loss=0.52)&#10;        except MaxLossExceededError as e:&#10;            factors = alphalens.utils.get_clean_factor_and_forward_returns(feature, returns,&#10;                                                                           bins=3,&#10;                                                                           periods=(1, 5, 10), quantiles=None,&#10;                                                                           max_loss=0.52)&#10;    return factors&#10;&#10;&#10;def get_instrument_list(full_mode=False):&#10;    market = convert_to_csi_code(config_info['market'])&#10;    start = None&#10;    end = None&#10;    if config_info['generate_ins_date'] is not None:&#10;        start = config_info['generate_ins_date'][0]&#10;        end = config_info['generate_ins_date'][1]&#10;    instruments = []&#10;    while len(instruments) == 0:&#10;        if full_mode:&#10;            instruments = D.instruments(market=market)&#10;        else:&#10;            instruments = [item.lstrip('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ') for item in&#10;                           D.list_instruments(D.instruments(market=market), start_time=start, end_time=end)]&#10;        # 将end往后延一年&#10;        if end is not None:&#10;            end = pd.to_datetime(end)&#10;            end = end + pd.DateOffset(years=1)&#10;    return instruments&#10;&#10;&#10;def compute_ic_table_for_data(df, start=None, end=None, existing_ic=None):&#10;    if start is not None and end is not None:&#10;        df = df.loc[(start &lt; df.index.get_level_values(0)) &amp; (df.index.get_level_values(0) &lt; end), :]&#10;    returns = df.loc[:, ['close']].copy()&#10;    returns = returns.pivot_table(index='date', columns='sec_id', values='close')&#10;    returns.rename(columns={'close': 'returns'}, inplace=True)&#10;    ret = pd.DataFrame()&#10;    for col in df.columns:&#10;        if col in ['return', 'open', 'high', 'low', 'close', 'volume', 'vwap', 'index_close', 'amount', 'adjclose',&#10;                   'factor',&#10;                   'benchmark_open', 'benchmark_close']:&#10;            continue&#10;        if col.startswith('flag') or col.startswith('return'):&#10;            continue&#10;        if existing_ic and col in existing_ic:&#10;            continue&#10;        print('compute ic for %s' % col)&#10;        factors = compute_alphalen_factor(returns, col, df)&#10;        ic = perf.mean_information_coefficient(factors)&#10;        ic = abs(ic)&#10;        ret = pd.concat([ret, pd.DataFrame(ic.T, columns=[col])], axis=1)&#10;&#10;    return ret.T&#10;&#10;&#10;def prepare_data_for_model(stock_data, factors, period, start_date, end_date, keep_close=False):&#10;    end_date = pd.to_datetime(end_date)&#10;    if config_info['train_type'] == 'cls':&#10;        label_tag = f'flag_{period}D'&#10;        drop_label = f'return_{period}D'&#10;    else:&#10;        label_tag = f'return_{period}D'&#10;        drop_label = f'flag_{period}D'&#10;&#10;    extra_labels = [label_tag, drop_label, 'return']&#10;    if keep_close:&#10;        extra_labels.append('close')&#10;    extra_labels = [col for col in extra_labels if col in stock_data.columns]&#10;&#10;    labels = factors + extra_labels&#10;    if 'regime_id' in stock_data.columns and 'regime_id' not in labels:&#10;        labels.append('regime_id')&#10;&#10;    ret_data = stock_data.loc[&#10;        (stock_data.index.get_level_values(0) &gt;= start_date) &amp;&#10;        (stock_data.index.get_level_values(0) &lt;= end_date),&#10;        labels&#10;    ].copy()&#10;&#10;    if not config_info['auto_feature_select']:&#10;        if config_info['train_type'] == 'reg':&#10;            def weights_caculator(row, end):&#10;                base_weight = row.get(f'return_{period}D', 0) / config_info['max_return'][period]&#10;                weights = min(config_info['max_weight'], abs(base_weight))&#10;                if config_info['weights421']:&#10;                    days_diff = (end - row.name[0]).days&#10;                    max_level_days = max(config_info['more_weights_days'], 180)&#10;                    sec_level_days = max_level_days * 2&#10;                    if days_diff &lt; max_level_days:&#10;                        return 40 * (weights ** config_info['curve_2'])&#10;                    elif days_diff &lt; sec_level_days:&#10;                        return 30 * (weights ** config_info['curve_2'])&#10;                return 10 * (weights ** config_info['curve'])&#10;&#10;            ret_data['weight'] = ret_data.apply(weights_caculator, args=(end_date,), axis=1)&#10;        elif config_info['train_type'] == 'cls':&#10;            if f'flag_{period}D' in ret_data.columns:&#10;                weight = ret_data[f'flag_{period}D'].value_counts() / len(ret_data)&#10;                weight = (1 - weight) / min(1 - weight)&#10;                ret_data['weight'] = ret_data[f'flag_{period}D'].apply(lambda x: weight[x])&#10;            else:&#10;                ret_data['weight'] = 1&#10;&#10;    for col in ['return', drop_label]:&#10;        if col in ret_data.columns:&#10;            ret_data.drop(columns=[col], inplace=True)&#10;    if label_tag in ret_data.columns:&#10;        ret_data.rename(columns={label_tag: 'return'}, inplace=True)&#10;&#10;    if config_info['train_with_add_features']:&#10;        add_features = prepare_add_features(start_date, end_date, False)&#10;        extra_feat = f'return_{config_info[&quot;period_n&quot;]}D'&#10;        if extra_feat in add_features.columns:&#10;            add_features.drop(columns=[extra_feat], inplace=True)&#10;        ret_data = pd.merge(ret_data, add_features, left_index=True, right_index=True, how='left')&#10;&#10;    ret_data.dropna(subset=ret_data.columns.difference(['return']), inplace=True)&#10;    ret_data = ret_data.loc[~ret_data.isin([np.inf, -np.inf]).any(axis=1)]&#10;&#10;    return ret_data&#10;&#10;&#10;def load_model(model_name):&#10;    if not os.path.exists('models/' + model_name):&#10;        return None&#10;    try:&#10;        ret = TabularPredictor.load('models/' + model_name, require_version_match=False, verbosity=0)&#10;        return ret if ret.is_fit else None&#10;    except:&#10;        return None&#10;&#10;&#10;def stock_metric(y_true, y_pred):&#10;    cm = confusion_matrix(y_true, y_pred, labels=[1, 0])&#10;    total = 1 if cm[:, 0].sum() == 0 else cm[:, 0].sum()&#10;    precision_1 = cm[0, 0] / total&#10;    false_positive_ratio_3 = cm[1, 0] / total&#10;    result = precision_1 - false_positive_ratio_3&#10;    return result&#10;&#10;&#10;def prepare_add_features(start_date, end_date, with_main_features=True):&#10;    label = f'return_{config_info[&quot;period_n&quot;]}D'&#10;    stock_data = load_stock_data(config_info['alpha_type'], config_info['market'])&#10;    df = stock_data.loc[&#10;        (stock_data.index.get_level_values(0) &gt;= start_date) &amp; (stock_data.index.get_level_values(0) &lt;= end_date), [&#10;            label]]&#10;&#10;    add_df = pd.read_csv(f'{config_info[&quot;market&quot;]}_additional_features.csv', parse_dates=['date'],&#10;                         dtype={'stock_code': str})&#10;    add_df = add_df.loc[(add_df['date'] &gt;= start_date) &amp; (add_df['date'] &lt;= end_date)]&#10;    add_df.rename(columns={'stock_code': 'sec_id'}, inplace=True)&#10;    add_df['sec_id'] = add_df['sec_id'].apply(lambda x: x.zfill(6))&#10;    add_df.set_index(['date', 'sec_id'], inplace=True)&#10;&#10;    df = pd.merge(df, add_df, left_index=True, right_index=True, how='left')&#10;&#10;    if with_main_features:&#10;        period = config_info['period_n']&#10;        factors = get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'])&#10;        train_data = prepare_data_for_model(stock_data, factors, period, start_date, end_date)&#10;        train_data = remove_lastdays_from_data(train_data)&#10;        train_data.drop(columns=['return'], inplace=True)&#10;        df = pd.merge(df, train_data, left_index=True, right_index=True, how='left')&#10;    return df&#10;&#10;&#10;def remove_lastdays_from_data(train_data):&#10;    train_data = train_data.sort_index(level=0, ascending=False)&#10;    last_n_days = train_data.index.get_level_values(0).unique()[:config_info['period_n']]&#10;    train_data = train_data[~train_data.index.get_level_values(0).isin(last_n_days)]&#10;    train_data.dropna(inplace=True)&#10;    return train_data&#10;&#10;&#10;def compute_batch_loss(pos_batch, neg_scores):&#10;    &quot;&quot;&quot;&#10;    对一个正样本批次与所有负样本计算 pairwise 排序损失，&#10;    返回该批次损失总和和元素个数。&#10;    &quot;&quot;&quot;&#10;    differences_batch = pos_batch[:, None] - neg_scores[None, :]&#10;    batch_loss = np.log1p(np.exp(-differences_batch))&#10;    return np.sum(batch_loss), batch_loss.size&#10;&#10;&#10;@njit(parallel=True, fastmath=True)&#10;def pairwise_logistic_loss_parallel(pos_scores, neg_scores):&#10;    total_loss = 0.0&#10;    # 并行化外层循环，每次处理一个正样本对应所有负样本对&#10;    for i in prange(pos_scores.shape[0]):&#10;        local_loss = 0.0&#10;        # 内层循环不做并行处理，累加当前正样本与所有负样本的损失&#10;        for j in range(neg_scores.shape[0]):&#10;            diff = pos_scores[i] - neg_scores[j]&#10;            local_loss += np.log1p(np.exp(-diff))&#10;        total_loss += local_loss  # 这里使用加法累加各线程局部结果&#10;    count = pos_scores.shape[0] * neg_scores.shape[0]&#10;    return total_loss, count&#10;&#10;&#10;def mixed_loss_metric(y_true, y_pred, pos_weight=0.9, neg_weight=0.1, lambda_rank=1.0):&#10;    eps = 1e-8&#10;    y_pred = np.clip(y_pred, eps, 1 - eps)&#10;    # 加权二元交叉熵损失&#10;    bce_loss = - (pos_weight * y_true * np.log(y_pred) +&#10;                  neg_weight * (1 - y_true) * np.log(1 - y_pred))&#10;    bce_loss_mean = np.mean(bce_loss)&#10;&#10;    # 将预测概率转换为 logits&#10;    logits = np.log(y_pred / (1 - y_pred))&#10;&#10;    pos_idx = np.where(y_true == 1)[0]&#10;    neg_idx = np.where(y_true == 0)[0]&#10;&#10;    if len(pos_idx) == 0 or len(neg_idx) == 0:&#10;        ranking_loss = 0.0&#10;    else:&#10;        pos_scores = logits[pos_idx]&#10;        neg_scores = logits[neg_idx]&#10;        total_loss, count = pairwise_logistic_loss_parallel(pos_scores, neg_scores)&#10;        ranking_loss = total_loss / count&#10;&#10;    total_loss_final = bce_loss_mean + lambda_rank * ranking_loss&#10;    return total_loss_final&#10;&#10;&#10;class MixedLossMetric(object):&#10;    def __init__(self, pos_weight=0.9, neg_weight=0.1, lambda_rank=1.0):&#10;        self.pos_weight = pos_weight&#10;        self.neg_weight = neg_weight&#10;        self.lambda_rank = lambda_rank&#10;        self.greater_is_better = False  # 损失越低越好&#10;        self.greater_is_better_internal = self.greater_is_better&#10;        self.needs_pred = False  # 表示不需要&quot;预测标签&quot;&#10;        self.needs_proba = True  # 表示需要概率&#10;        self.needs_quantile = False&#10;        self.name = &quot;mixed_loss_metric&quot;&#10;        self._optimum = 1&#10;        self.needs_class = True&#10;&#10;    def __call__(self, y_true, y_pred, weight=None):&#10;        if y_pred.ndim &gt; 1 and y_pred.shape[1] &gt; 1:&#10;            y_pred = y_pred[:, 1]&#10;        return mixed_loss_metric(y_true, y_pred, self.pos_weight, self.neg_weight, self.lambda_rank)&#10;&#10;&#10;def top_mean_squarer(y_true, y_pred, *, sample_weight=None, multioutput=&quot;uniform_average&quot;, squared=False):&#10;    sorted_idx = np.argsort(-y_pred)&#10;    cutoff = int(np.ceil(len(y_pred) * 0.1))&#10;    top_idx = sorted_idx[:cutoff]&#10;    return mean_squared_error(y_true[top_idx], y_pred[top_idx], sample_weight=None, multioutput=multioutput,&#10;                              squared=squared)&#10;&#10;&#10;def train_model(stock_data, factors, period, start_date, end_date, save_path, selected_hyper=True):&#10;    if config_info['decorrelated_top_features']:&#10;        factors = get_decorrelated_top_features(stock_data, factors, f'return_{period}D', len(factors) - config_info['remove_from_fid'])&#10;    if 'regime_id' in stock_data.columns and 'regime_id' not in factors:&#10;        factors.append('regime_id')&#10;    train_data = prepare_data_for_model(stock_data, factors, period, start_date, end_date)&#10;    train_data = remove_lastdays_from_data(train_data)&#10;    last_day = max(train_data.index.get_level_values(0).unique())&#10;    metric = None&#10;    if config_info['train_type'] == 'reg':&#10;        metric = make_scorer(&quot;mean_squared_error&quot;, top_mean_squarer, optimum=0, greater_is_better=False)&#10;    # elif  config_info['train_type'] == 'cls':&#10;    #    metric = make_scorer(name='stock_metric', score_func=stock_metric)&#10;    #    metric = MixedLossMetric(pos_weight=0.9, neg_weight=0.1, lambda_rank=1.0)&#10;&#10;    if config_info['auto_feature_select'] or config_info['train_type'] == 'rank':&#10;        weight = None&#10;    else:&#10;        weight = 'weight'&#10;    presets = config_info['presets']&#10;    tuning_days = config_info['tuning_days']&#10;    good_quality = (presets not in ['medium_quality'])&#10;&#10;    if tuning_days &gt; 0:&#10;        tuned_date = train_data.index.get_level_values(0).max() - pd.DateOffset(days=tuning_days)&#10;        tuned_data = train_data.loc[train_data.index.get_level_values(0) &gt;= tuned_date, :]&#10;&#10;        unique_sec_ids = tuned_data.index.get_level_values(1).unique()&#10;        selected_sec_ids = np.random.RandomState(0).choice(&#10;            unique_sec_ids, size=len(unique_sec_ids)//2, replace=False)&#10;&#10;        with open('tuning_sec_ids.txt', 'w') as f:&#10;            for sec_id in selected_sec_ids:&#10;                f.write(f&quot;{sec_id}\n&quot;)&#10;        tuned_data_sample = tuned_data.loc[tuned_data.index.get_level_values(1).isin(selected_sec_ids)]&#10;        train_data = pd.concat([&#10;            train_data.loc[train_data.index.get_level_values(0) &lt;= tuned_date, :],&#10;            tuned_data.loc[~tuned_data.index.get_level_values(1).isin(selected_sec_ids), :]&#10;        ])&#10;        tuned_data = tuned_data_sample&#10;        holdout_frac = None&#10;    else:&#10;        tuned_data = None&#10;        holdout_frac = 0.1&#10;&#10;    use_bag_holdout = True&#10;    global_seed = config_info['train_seed']&#10;    kwargs = {}&#10;    groups_name = None&#10;    if good_quality:&#10;        folds = config_info['num_bag_folds']&#10;        kwargs = {&#10;            'num_stack_levels': config_info['num_stack_levels'],&#10;            'num_bag_folds': folds,&#10;        }&#10;        if folds &gt; 0:&#10;            groups_name = 'group'&#10;            train_indices = train_data.index.get_level_values(1).unique()&#10;            np.random.seed(global_seed)&#10;            train_indices = np.array(train_indices)&#10;            np.random.shuffle(train_indices)&#10;            groups = np.array_split(train_indices, folds)&#10;            train_data['group'] = 0&#10;            for fold_idx, group in enumerate(groups):&#10;                mask = train_data.index.get_level_values(1).isin(group)&#10;                train_data.loc[mask, 'group'] = fold_idx&#10;    if isinstance(selected_hyper, dict):&#10;        hyperparameters = selected_hyper&#10;    elif selected_hyper:&#10;        hyperparameters = get_hyperparameters(global_seed)&#10;    else:&#10;        hyperparameters = 'zeroshot' if config_info['zeroshot'] else ALL_HYPERPARAMETERS&#10;&#10;    logging.getLogger().info('%s stocks in the training' % len(train_data.index.get_level_values(1).unique().tolist()))&#10;    logging.getLogger().info(f'start at {start_date}, end at {end_date}， last date is {last_day}')&#10;&#10;    logging.getLogger().info(f'regimeID in  training: {&quot;regime_id&quot; in stock_data.columns}')&#10;&#10;    pred = TabularPredictor(label='return', path=f'models/{save_path}', eval_metric=metric, sample_weight=weight,&#10;                            weight_evaluation=weight is not None, groups=groups_name).fit(&#10;        train_data,&#10;        tuned_data,&#10;        verbosity=config_info['ag_verbosity'],&#10;        time_limit=config_info['train_time_limit'],&#10;        presets=presets,&#10;        keep_only_best=False,&#10;        use_bag_holdout=use_bag_holdout,&#10;        hyperparameters=hyperparameters,&#10;        ds_args={'enable_ray_logging': True},&#10;        num_gpus=0 if config_info['cpu'] else 1,&#10;        holdout_frac=holdout_frac,&#10;        feature_prune_kwargs={} if config_info['auto_feature_select'] else None,&#10;        hyperparameter_tune_kwargs='auto' if config_info['hyperparameter_tune_kwargs'] else None,&#10;        **kwargs&#10;    )&#10;    if config_info['num_stack_levels'] == 0 and config_info['enable_second_ensemble']:&#10;        all_models = pred.model_names()&#10;        l1_models = [m for m in all_models if 'WeightedEnsemble' not in m]&#10;        pred.fit_weighted_ensemble(&#10;            base_models=list(l1_models))&#10;    fid_path = f'feature_importance.csv'&#10;    if tuned_data is not None and not os.path.exists(fid_path):&#10;        logging.getLogger().info(f'tuning data lens {tuned_data.shape[0]}')&#10;        fid = pred.feature_importance(tuned_data)&#10;        fid['imp_div_std'] = fid['importance'] / fid['stddev']&#10;        fid['neg_log_p'] = -np.log10(fid['p_value'])&#10;        fid['z_p99'] = zscore(fid['p99_high'])&#10;        fid['z_imp_div_std'] = zscore(fid['imp_div_std'])&#10;        fid['z_neg_log_p'] = zscore(fid['neg_log_p'])&#10;        fid['composite_score'] = fid['z_p99'] + fid['z_imp_div_std'] + fid['z_neg_log_p']&#10;        fid.to_csv(fid_path)&#10;    return pred&#10;&#10;&#10;def get_hyperparameters(global_seed):&#10;    hyperparameters = {}&#10;    if config_info['zeroshot']:&#10;        for model_name in config_info.get('models_to_use', ['GBM', 'CAT', 'FASTAI']):&#10;            if model_name in configs.hyperparameter_portfolio_small:&#10;                hyperparameters[model_name] = configs.hyperparameter_portfolio_small[model_name]&#10;    else:&#10;        for model_name in config_info.get('models_to_use', ['GBM', 'CAT', 'FASTAI']):&#10;            if model_name in ALL_HYPERPARAMETERS:&#10;                if model_name == 'GBM':&#10;                    if isinstance(ALL_HYPERPARAMETERS[model_name], list):&#10;                        if len(config_info['gbm_to_use']) &gt; 0:&#10;                            hyperparameters[model_name] = [ALL_HYPERPARAMETERS[model_name][int(i)] for i in&#10;                                                         config_info['gbm_to_use']]&#10;                        else:&#10;                            hyperparameters[model_name] = ALL_HYPERPARAMETERS[model_name]&#10;                    else:&#10;                        hyperparameters[model_name] = ALL_HYPERPARAMETERS[model_name]&#10;                else:&#10;                    hyperparameters[model_name] = ALL_HYPERPARAMETERS[model_name]&#10;&#10;    for model_name, model_params in hyperparameters.items():&#10;        if isinstance(model_params, list):&#10;            for param_set in model_params:&#10;                if 'ag_args_fit' in param_set:&#10;                    param_set['ag_args_fit']['max_time_limit'] = config_info['max_time_sub_model']&#10;                else:&#10;                    param_set['ag_args_fit'] = dict(max_time_limit=config_info['max_time_sub_model'])&#10;                for seed_key in param_set:&#10;                    if seed_key.endswith('seed'):&#10;                        param_set[seed_key] = global_seed&#10;        else:&#10;            if 'ag_args_fit' in model_params:&#10;                model_params['ag_args_fit']['max_time_limit'] = config_info['max_time_sub_model']&#10;            else :&#10;                model_params['ag_args_fit'] = dict(max_time_limit=config_info['max_time_sub_model'])&#10;            for seed_key in model_params.keys():&#10;                if seed_key.endswith('seed'):&#10;                    model_params[seed_key] = global_seed&#10;&#10;    if config_info['cpu']:&#10;        if 'GBM' in hyperparameters and isinstance(hyperparameters['GBM'], list):&#10;            for param_set in hyperparameters['GBM']:&#10;                if 'device_type' in param_set:&#10;                    param_set['device_type'] = 'cpu'&#10;&#10;        if 'XGB' in hyperparameters:&#10;            if 'tree_method' in hyperparameters['XGB']:&#10;                hyperparameters['XGB']['tree_method'] = 'hist'&#10;            if 'predictor' in hyperparameters['XGB']:&#10;                hyperparameters['XGB']['predictor'] = 'cpu_predictor'&#10;    else:&#10;        if 'GBM' in hyperparameters and isinstance(hyperparameters['GBM'], list):&#10;            for param_set in hyperparameters['GBM']:&#10;                if 'device_type' in param_set:&#10;                    param_set['device_type'] = 'gpu'&#10;    return hyperparameters&#10;&#10;&#10;def prepare_bt_data(stock_data, cerebro_bt, start_date, end_date):&#10;    def to_qlib_code(code):&#10;        if code.startswith('6'):&#10;            return f&quot;SH{code}&quot;&#10;        elif code.startswith(('0', '1', '2', '3')):&#10;            return f&quot;SZ{code}&quot;&#10;        elif code.startswith(('87', '83', '4')):&#10;            return f&quot;BJ{code}&quot;&#10;        else:&#10;            return code&#10;&#10;    index_data = pd.read_csv(f'{config_info[&quot;market&quot;]}.csv', index_col=0, parse_dates=True)&#10;    index_data = index_data.loc[(index_data.index &gt;= start_date) &amp; (index_data.index &lt;= end_date), :]&#10;    index_data.rename(columns={'close': 'Close', 'open': 'Open', 'high': 'High', 'low': 'Low', 'volume': 'Volume'},&#10;                      inplace=True)&#10;    data = bt.feeds.PandasData(dataname=index_data)&#10;    cerebro_bt.adddata(data, name=config_info[&quot;market&quot;])&#10;&#10;    fields = [&quot;$open&quot;, &quot;$high&quot;, &quot;$low&quot;, &quot;$close&quot;, &quot;$volume&quot;]&#10;&#10;    instruments = [to_qlib_code(cd) for cd in stock_data]&#10;&#10;    data_df = D.features(&#10;        instruments=instruments,&#10;        fields=fields,&#10;        start_time=start_date,&#10;        end_time=end_date&#10;    )&#10;&#10;    all_codes = data_df.index.get_level_values('instrument').unique()&#10;&#10;    for q_code in all_codes:&#10;        df_single = data_df.xs(q_code, level='instrument')&#10;        df_single = df_single.rename(columns={&#10;            '$open': 'Open',&#10;            '$high': 'High',&#10;            '$low': 'Low',&#10;            '$close': 'Close',&#10;            '$volume': 'Volume'&#10;        })&#10;&#10;        if not isinstance(df_single.index, pd.DatetimeIndex):&#10;            df_single.index = pd.to_datetime(df_single.index)&#10;&#10;        original_code = q_code.lstrip('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ')&#10;&#10;        trading_days = D.calendar(start_time=start_date, end_time=end_date)&#10;        df_single = df_single.reindex(trading_days, method='ffill')&#10;        df_single.fillna(method='ffill', inplace=True)&#10;        df_single.fillna(method='bfill', inplace=True)&#10;        data = bt.feeds.PandasData(dataname=df_single)&#10;        cerebro_bt.adddata(data, name=original_code)&#10;&#10;&#10;def prepare_bt_pred_data(pred_data, df, topn=10):&#10;    if config_info['train_type'] == 'cls':&#10;        pred_pos_data = pd.DataFrame(pred_data, index=pred_data.index, columns=[1])&#10;        pred_pos_data.rename(columns={1: 'return'}, inplace=True)&#10;        pred_neg_data = pd.DataFrame(pred_data, index=pred_data.index, columns=[0])&#10;        pred_neg_data.rename(columns={0: 'return'}, inplace=True)&#10;        pred_data = pred_pos_data - pred_neg_data&#10;    else:&#10;        pred_data = pd.DataFrame(pred_data, index=pred_data.index, columns=['return'])&#10;    pred_data['date'] = pred_data.index.get_level_values(0)&#10;    pred_data['sec_id'] = pred_data.index.get_level_values(1)&#10;    pred_data.reset_index(drop=True, inplace=True)&#10;    secid_filter = config_info['secid_filter']&#10;    if len(secid_filter) &gt; 0:&#10;        pred_data = pred_data.loc[pred_data['sec_id'].isin(secid_filter), :]&#10;    pred_data['rank'] = pred_data.groupby('date')['return'].rank(ascending=False, method='min')&#10;    if topn &gt; 0:&#10;        pred_data = pred_data.loc[pred_data['rank'] &lt;= topn].copy()&#10;    if df is not None:&#10;        pred_data = add_additional_features(pred_data, df)&#10;    pred_data.sort_values(['date', 'rank'], inplace=True)&#10;    return pred_data&#10;&#10;&#10;def add_additional_features(backtrader_pred, df):&#10;    ret5 = df['vwap'].groupby('sec_id').pct_change(5)&#10;    ret5 = pd.DataFrame(ret5)&#10;    ret5.rename(columns={'vwap': 'vwap5'}, inplace=True)&#10;    ret = pd.merge(backtrader_pred, ret5, left_on=['date', 'sec_id'], right_on=['date', 'sec_id'],&#10;                   how='left')&#10;    feature_names = config_info['added_alpha_for_stratergy']&#10;    if feature_names is not None:&#10;        feature_list = [feature.strip() for feature in feature_names.split(',')]&#10;        feature_list = [feature[1:] if feature.startswith('_') else feature for feature in feature_list]&#10;&#10;        if config_info['train_with_add_features']:&#10;            add_features = prepare_add_features(min(backtrader_pred['date']), max(backtrader_pred['date']), False)&#10;            existing_columns = [col for col in feature_list if col in add_features.columns]&#10;            if len(existing_columns) &gt; 0:&#10;                add_features = add_features.loc[:, existing_columns]&#10;            add_features.reset_index(inplace=True)&#10;            ret = pd.merge(ret, add_features, left_on=['date', 'sec_id'], right_on=['date', 'sec_id'], how='left')&#10;&#10;        existing_columns = [col for col in feature_list if col in df.columns]&#10;        ret = pd.merge(ret, df[existing_columns], left_on=['date', 'sec_id'], right_on=['date', 'sec_id'],&#10;                       how='left')&#10;&#10;    return ret&#10;&#10;&#10;def init_logger():&#10;    logger = logging.getLogger()&#10;    logger.setLevel(logging.INFO)&#10;&#10;    ch = logging.StreamHandler()&#10;    ch.setLevel(logging.INFO)&#10;    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')&#10;    ch.setFormatter(formatter)&#10;    logger.addHandler(ch)&#10;&#10;    if config_info['trade_log']:&#10;        fh = logging.FileHandler('logfile.log', mode='a')&#10;        fh.setLevel(logging.INFO)&#10;        fh.setFormatter(formatter)&#10;        logger.addHandler(fh)&#10;&#10;    pd.set_option('display.max_columns', None)&#10;    pd.set_option('display.max_rows', None)&#10;    pd.set_option('display.width', None)&#10;    pd.set_option('display.max_colwidth', None)&#10;&#10;    return logger&#10;&#10;&#10;def send_email(title, msg, addr):&#10;    try:&#10;        smtp = smtplib.SMTP()&#10;        smtp.connect('smtp.126.com')&#10;        from_adr = '<EMAIL>'&#10;        smtp.login(from_adr, 'QNspf6Q8HQKy5VBj')&#10;        mime_msg = MIMEText(msg, 'plain', 'utf-8')&#10;        mime_msg['Subject'] = Header(title, 'utf-8')&#10;        mime_msg['From'] = from_adr&#10;        mime_msg['To'] = addr&#10;        smtp.sendmail(from_adr, addr.split(','), mime_msg.as_string())&#10;        smtp.quit()&#10;    except:&#10;        pass&#10;&#10;&#10;def send_email_attachment(title, addr, msg, *files):&#10;    try:&#10;        smtp = smtplib.SMTP()&#10;        smtp.connect('smtp.126.com')&#10;        from_adr = '<EMAIL>'&#10;        smtp.login(from_adr, 'QNspf6Q8HQKy5VBj')&#10;        mime_msg = MIMEMultipart()&#10;        mime_msg['Subject'] = Header(title, 'utf-8')&#10;        mime_msg['From'] = from_adr&#10;        mime_msg['To'] = addr&#10;        mime_msg.attach(MIMEText(msg, 'plain', 'utf-8'))&#10;        for file_path in files:&#10;            with open(file_path, 'rb') as f:&#10;                attachment_content = MIMEApplication(f.read(), _subtype='txt')&#10;                attachment_content.add_header('Content-Disposition', 'attachment', filename=file_path.split(&quot;/&quot;)[-1])&#10;                mime_msg.attach(attachment_content)&#10;        smtp.sendmail(from_adr, addr.split(','), mime_msg.as_string())&#10;        smtp.quit()&#10;    except:&#10;        pass&#10;&#10;&#10;def notify_by_email(title, msg=None, also_to_jammie=False, force_send=False):&#10;    if not config_info['notify_by_mail'] and not force_send:&#10;        return&#10;    if msg is None:&#10;        msg = title&#10;    send_email(title, msg, config_info['mail'])&#10;    if also_to_jammie:&#10;        send_email(title, msg, '<EMAIL>')&#10;&#10;&#10;def notify_by_email_with_attachment(title, *file_path):&#10;    send_email_attachment(title, config_info['mail'], '', *file_path)&#10;&#10;&#10;def get_last_time():&#10;    market = pd.read_csv(f'{config_info[&quot;market&quot;]}.csv', index_col=0)&#10;    now = pd.to_datetime(market.index[-1])&#10;    return now&#10;&#10;&#10;def prepare_data_for_cerebro(df, end_date, test_start_date, tuning_sec_only=False):&#10;    cerebro = bt.Cerebro()&#10;    bt_data = df.loc[(df.index.get_level_values(0) &gt;= test_start_date) &amp; (&#10;            df.index.get_level_values(0) &lt;= end_date), :].index.get_level_values(1).unique().tolist()&#10;    if tuning_sec_only:&#10;        with open('tuning_sec_ids.txt', 'r') as f:&#10;            tuning_sec_ids = f.read().splitlines()&#10;            bt_data = list(set(bt_data) &amp; set(tuning_sec_ids))&#10;    prepare_bt_data(bt_data, cerebro, test_start_date, end_date)&#10;    return cerebro&#10;&#10;&#10;def prepare_cerebro(cerebro):&#10;    cerebro.broker.set_coo(True)&#10;    cerebro.broker.setcash(10000000.0)&#10;    cerebro.broker.setcommission(commission=0.001)&#10;    cerebro.addanalyzer(bt.analyzers.TimeReturn)&#10;    cerebro.addanalyzer(bt.analyzers.SharpeRatio, timeframe=bt.TimeFrame.Days, annualize=True, riskfreerate=0.0)&#10;    cerebro.addanalyzer(AnnualizedSortinoRatio)&#10;    cerebro.addanalyzer(bt.analyzers.DrawDown)&#10;    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer)&#10;    cerebro.addanalyzer(bt.analyzers.AnnualReturn)&#10;    cerebro.addobserver(bt.observers.Value)&#10;    cerebro.addobserver(bt.observers.DrawDown)&#10;&#10;&#10;def draw_values(stats, save_path):&#10;    values = stats[0].observers.value.lines.value.get(size=stats[0].data.datetime.buflen())&#10;    dates = [bt.num2date(x) for x in stats[0].data.datetime.array]&#10;    plt.figure(figsize=(10, 6))&#10;    plt.plot(dates, values)&#10;    plt.title('Value over Time')&#10;    plt.xlabel('Date')&#10;    plt.ylabel('Value')&#10;    plt.savefig(save_path)&#10;&#10;&#10;def dump_last_broker(cerebro, stat):&#10;    broker = cerebro.broker&#10;    positions = broker.positions&#10;    ret = '\n-------operations-------\n'&#10;    sells = []&#10;    buys = []&#10;    holdings = []&#10;    file_name = pd.to_datetime(stat.data.datetime.date(0)).strftime('%Y-%m-%d')&#10;    file_name = f'{file_name}_holding.txt'&#10;    for order in stat.last_order:&#10;        ret += f&quot;{order[0]} {order[1]}\n&quot;&#10;        if order[0] == 'sel':&#10;            sells.append(order[1])&#10;        else:&#10;            buys.append(order[1])&#10;    ret += '\n-------holding------\n'&#10;    for data, position in positions.items():&#10;        if not position.size &gt; 0:&#10;            continue&#10;        if data._name in sells:&#10;            continue&#10;&#10;        last_day_change = (data.close[0] - data.close[-1]) / data.close[-1]&#10;        holding_period_change = (data.close[0] - position.price) / position.price&#10;        holdings.append(data._name)&#10;        ret += f'{data._name}, 最后一日涨幅: {format(last_day_change * 100, &quot;.2f&quot;)}%, 持有收益: {format(holding_period_change * 100, &quot;.2f&quot;)}%\n'&#10;&#10;    if config_info['trade_log']:&#10;        with open(file_name, 'w') as f:&#10;            f.write('Buys:\n')&#10;            for buy in buys:&#10;                f.write(f'{buy}\n')&#10;            f.write('\nHoldings:\n')&#10;            for holding in holdings:&#10;                f.write(f'{holding}\n')&#10;    return ret&#10;&#10;&#10;def dump_stats_cerebro(cerebro, stats, mail_title=None):&#10;    values = stats[0].observers.value.lines.value.get(size=stats[0].data.datetime.buflen())&#10;    dates = [bt.num2date(x) for x in stats[0].data.datetime.array]&#10;    values = pd.Series(values, index=dates)&#10;    change = values.pct_change()[-1]&#10;    msg = mail_title + '\r\n\r\n'&#10;    msg += str(stats[0].analyzers.annualreturn.get_analysis()) + '\r\n'&#10;    msg += str(stats[0].analyzers.sharperatio.get_analysis()) + '\r\n'&#10;    msg += str(stats[0].analyzers.annualizedsortinoratio.get_analysis()) + '\r\n'&#10;    msg += str(stats[0].analyzers.drawdown.get_analysis()) + '\r\n'&#10;&#10;    # Get trade analysis&#10;    trade_analysis = stats[0].analyzers.tradeanalyzer.get_analysis()&#10;    if 'len' in trade_analysis:&#10;        avg_holding = str(trade_analysis['len']['average'])&#10;        msg += f'avg Holding: {avg_holding}\r\n'&#10;&#10;    # Calculate winning ratio&#10;    total_completed = trade_analysis.get('total', {}).get('closed', 0)&#10;    total_won = trade_analysis.get('won', {}).get('total', 0)&#10;    if total_completed &gt; 0:&#10;        win_ratio = (total_won / total_completed) * 100&#10;        msg += f'Win Ratio: {win_ratio:.2f}%\r\n'&#10;&#10;    msg += f'last day change: {change}'&#10;&#10;    if config_info['dump_change']:&#10;        msg += dump_last_broker(cerebro, stats[0])&#10;    if (mail_title is not None) and (config_info['notify_by_mail'] is True):&#10;        value_png_path = 'value.png'&#10;        draw_values(stats, value_png_path)&#10;        send_email_attachment(mail_title, config_info['mail'], msg, value_png_path)&#10;&#10;    logger = logging.getLogger()&#10;    logger.info(msg)&#10;&#10;&#10;class TopNStratergy(bt.Strategy):&#10;    params = (&#10;        ('topn', 10),&#10;        ('period', 10),&#10;        ('printlog', False),&#10;        ('pred_data', None),&#10;        ('max_down', 0.1),&#10;        ('min_hold', 1000),&#10;        ('min_return', -1),&#10;        ('trade_data', None),&#10;        ('min_mean_return', -1),&#10;        ('train_year', config_info['train_year']),&#10;        ('rank_model', None)&#10;    )&#10;&#10;    def log(self, txt, dt=None):&#10;        dt = dt or self.data.datetime.date(0)&#10;        if self.params.printlog:&#10;            logging.getLogger().info('%s, %s' % (dt.isoformat(), txt))&#10;&#10;    def notify_order(self, order):&#10;        if order.status in [order.Submitted, order.Accepted]:&#10;            return&#10;        sec_code = order.data._name&#10;        if order.status in [order.Completed]:&#10;            if order.isbuy():&#10;                exec_price = order.executed.price&#10;                size = order.executed.size&#10;                exec_date = self.data.datetime.date(0)  # 当前bar对应的日期&#10;                self.buy_pre[sec_code] = [exec_price, exec_date, size]&#10;                self.log('BUY EXECUTED %s, Price: %.4f, Cost: %.2f, Comm %.2f' %&#10;                         (sec_code,&#10;                          order.executed.price,&#10;                          order.executed.value,&#10;                          order.executed.comm))&#10;            elif order.issell():&#10;                if sec_code in self.buy_pre:&#10;                    del self.buy_pre[sec_code]&#10;                self.log('SELL EXECUTED %s, Price: %.4f, Cost: %.2f, Comm %.2f' %&#10;                         (sec_code,&#10;                          order.executed.price,&#10;                          order.executed.value,&#10;                          order.executed.comm))&#10;        elif order.status == order.Margin:&#10;            attempted_cost = order.created.price * order.created.size&#10;            current_cash = self.broker.get_cash()&#10;            shortfall = attempted_cost - current_cash&#10;&#10;            self.log(f&quot;Margin error for {sec_code}, short of {shortfall:.2f} cash&quot;)&#10;        elif order.status in [order.Canceled, order.Rejected]:&#10;            self.log('Order Canceled/Rejected %s:%s ' % (sec_code, order.isbuy()))&#10;&#10;    def notify_trade(self, trade):&#10;        if not trade.isclosed:&#10;            return&#10;        self.log('OPERATION PROFIT, GROSS %.2f, NET %.2f' % (trade.pnl, trade.pnlcomm))&#10;&#10;    def __init__(self):&#10;        self.skip_days = config_info['skip_days']&#10;        self.is_last_day = False&#10;        self.last_day = None&#10;        self.last_order = []&#10;        self.current_bar = 0&#10;        self.vix = False&#10;        self.topn = self.p.topn&#10;        self.period = self.p.period&#10;        self.pred_data = self.p.pred_data&#10;        self.buy_pre = {}&#10;        self.max_down = self.p.max_down&#10;        self.min_hold = self.p.min_hold&#10;        self.min_return = self.p.min_return&#10;        self.trade_data = self.p.trade_data&#10;        self.min_mean_return = self.p.min_mean_return&#10;        self.train_year = self.p.train_year&#10;        self.rank_model = self.p.rank_model&#10;        if self.pred_data is not None:&#10;            self.farrest_day = self.pred_data['date'].min()&#10;        self.cur_step_pred_data = None&#10;        if config_info['wencai_scores_path'] is not None:&#10;            self.wencai_data = pd.read_csv(config_info['wencai_scores_path'], index_col=[0, 1], parse_dates=True,&#10;                                           dtype={1: str})&#10;            if self.pred_data is not None:&#10;                wencai_columns = 'predict'&#10;                updated = pd.merge(self.pred_data, self.wencai_data[wencai_columns], left_on=['date', 'sec_id'],&#10;                                   right_index=True)&#10;                if not updated.empty:&#10;                    self.pred_data = updated&#10;                    temp = []&#10;                    select_range = config_info['select_range']&#10;                    for date, group in self.pred_data.groupby('date'):&#10;                        group = group.copy()&#10;                        top_rows = group[group['rank'] &lt;= select_range].copy()&#10;                        if not top_rows.empty:&#10;                            top_rows = top_rows.sort_values(by=wencai_columns, ascending=False)&#10;                            top_rows['rank'] = range(1, len(top_rows) + 1)&#10;                            group.update(top_rows)&#10;                        temp.append(group)&#10;                    self.pred_data = pd.concat(temp)&#10;        else:&#10;            self.wencai_data = None&#10;        self.index_data = pd.read_csv(f'{config_info[&quot;market&quot;]}.csv', index_col=0, parse_dates=True)&#10;        if os.path.exists('trade_date.txt') and config_info['consider_trade_date']:&#10;            with open('trade_date.txt', 'r') as f:&#10;                self.trade_date = f.readlines()&#10;                self.trade_date = [pd.to_datetime(date) for date in self.trade_date]&#10;        else:&#10;            self.trade_date = None&#10;&#10;    def get_predict_data(self, predict_date):&#10;        return self.pred_data.loc[(self.pred_data['date'] == predict_date), :]&#10;&#10;    def get_low_level(self, sec_code):&#10;        if self.cur_step_pred_data.empty:&#10;            return 0&#10;        returns = self.cur_step_pred_data.loc[self.cur_step_pred_data['sec_id'] == sec_code, 'rank_sel']&#10;        if returns.empty:&#10;            return 1&#10;        if returns.values[0] &lt;= self.topn * config_info['topn_dropout_k']:&#10;            return 0&#10;        return 1 if returns.values[0] &lt;= config_info['bottom_rank'] else 2&#10;&#10;    def check_sells(self, dt, predict_date):&#10;        buy_pre_items = self.buy_pre.items()&#10;        added_cash = 0&#10;&#10;        buy_pre_items = sorted(&#10;            buy_pre_items,&#10;            key=lambda x: (&#10;                self.cur_step_pred_data.loc[&#10;                    (self.cur_step_pred_data['sec_id'] == x[0]),&#10;                    'rank_sel'&#10;                ].values[0]&#10;                if not self.cur_step_pred_data.loc[&#10;                    (self.cur_step_pred_data['sec_id'] == x[0])&#10;                ].empty&#10;                else float('inf')&#10;            ),&#10;            reverse=True&#10;        )&#10;        sells = 0&#10;        for sec_code, info in buy_pre_items:&#10;            price = info[0]&#10;            buy_date = info[1]&#10;            size = info[2]&#10;&#10;            cur_close = self.getdatabyname(sec_code).close[0]&#10;            diff = (price - cur_close) / price&#10;            last_close = self.getdatabyname(sec_code).close[-1]&#10;            day_max_down = config_info['day_max_down']&#10;            low_level = self.get_low_level(sec_code)&#10;            if ((config_info['hold_till_end'] &gt; 0) and (&#10;                    predict_date - pd.Timestamp(buy_date)).days &gt;= config_info['hold_till_end'] * 7 / 5) or (&#10;                    (config_info['hold_till_end'] == 0) and (&#10;                    (diff &gt; self.max_down)&#10;                    or low_level &gt;= 1&#10;                    or ((last_close - cur_close) / last_close &gt; day_max_down)&#10;            )):&#10;                price_sell = self.getdatabyname(sec_code).close[0]&#10;                self.close(data=self.getdatabyname(sec_code))&#10;                added_cash += (size * price_sell)&#10;                if self.is_last_day:&#10;                    self.last_order.append(('sel', sec_code))&#10;                sells += 1&#10;                if sells &gt;= config_info['max_sell'] and low_level &lt; 2:&#10;                    break&#10;        return self.topn - (len(self.buy_pre) - sells), added_cash&#10;&#10;    def close_all(self):&#10;        for sec_code, info in self.buy_pre.items():&#10;            self.close(data=self.getdatabyname(sec_code))&#10;            if self.is_last_day:&#10;                self.last_order.append(('sell', sec_code))&#10;&#10;    def get_mean_returns(self, dt, predict_date):&#10;        mean_returns = self.pred_data.loc[pd.to_datetime(self.pred_data['date']) == predict_date, 'return'].mean()&#10;        return mean_returns&#10;&#10;    def get_selected_sec(self, dt, i, predict_date):&#10;        if self.cur_step_pred_data.empty or len(self.cur_step_pred_data) &lt;= i:&#10;            return None, None, True&#10;        selected = self.cur_step_pred_data.iloc[i]&#10;        sec_code = selected['sec_id']&#10;        returns = selected['return']&#10;        if not config_info['buy_high_price']:&#10;            if sec_code in self.getdatanames():&#10;                if self.getdatabyname(sec_code).close[-1] / self.getdatabyname(sec_code).close[-2] &gt;= 1.095:&#10;                    if pd.to_datetime(self.data.datetime.date(-1)) &lt; predict_date and \&#10;                            pd.to_datetime(self.data.datetime.date(-2)) &lt; predict_date:&#10;                        self.log(f'{sec_code} is too high')&#10;                        return sec_code, returns, True&#10;        return sec_code, returns, False&#10;&#10;    def next(self):&#10;        if self.skip_days &gt; 0:&#10;            self.skip_days -= 1&#10;            return&#10;        if self.last_day is None:&#10;            self.last_day = pd.to_datetime(self.data.datetime.date(-1))&#10;            first_run = True&#10;        else:&#10;            first_run = False&#10;        predict_date = dt = pd.to_datetime(self.data.datetime.date(0))&#10;        if predict_date == self.last_day:&#10;            self.is_last_day = True&#10;        self.current_bar += 1&#10;        self.prepare_cur_step_predicted_data(predict_date)&#10;        if self.trade_date is not None:&#10;            if predict_date not in self.trade_date:&#10;                self.close_all()&#10;                self.vix = False&#10;                return&#10;        if config_info['consider_vix']:&#10;            if self.vix:&#10;                if self.can_trade():&#10;                    self.vix = False&#10;                else:&#10;                    return&#10;            if not first_run:&#10;                self.vix = self.evaluate_and_clear_positions(predict_date)&#10;                if self.vix:&#10;                    return&#10;&#10;        for i in range(0, min(config_info['select_range'], len(self.cur_step_pred_data))):&#10;            newline = pd.DataFrame([self.cur_step_pred_data.iloc[i]['sec_id'], dt]).T&#10;            newline.columns = self.trade_data[0].columns&#10;            self.trade_data[0] = pd.concat([self.trade_data[0], newline])&#10;&#10;        empty_count, added_cash = self.check_sells(dt, predict_date)&#10;        if empty_count &lt;= 0:&#10;            return&#10;        self.update_cur_step_data_for_empty_slots(max(config_info['select_range'], empty_count * 2))&#10;        mean_returns = self.get_mean_returns(dt, predict_date)&#10;        if mean_returns &lt; self.min_mean_return:&#10;            return&#10;&#10;        avail_cash = self.broker.get_cash() + added_cash&#10;&#10;        each_cash = self.broker.get_value() / self.topn&#10;        for i in range(0, len(self.cur_step_pred_data)):&#10;            if avail_cash &lt; each_cash * 0.8:&#10;                break&#10;            sec_code, returns, should_skip = self.get_selected_sec(dt, i, predict_date)&#10;            if sec_code is None:&#10;                break&#10;            if returns &lt; self.min_return:&#10;                break&#10;            if should_skip:&#10;                continue&#10;            if self.buy_pre.get(sec_code) is not None:&#10;                continue&#10;            if sec_code not in self.getdatanames():&#10;                continue&#10;            price = self.getdatabyname(sec_code).close[0]&#10;            buy_cash = min(avail_cash, each_cash) * 0.98&#10;            avail_cash -= buy_cash&#10;            size = int(buy_cash / price)&#10;            self.buy(data=self.getdatabyname(sec_code), size=size)&#10;            if self.is_last_day:&#10;                self.last_order.append(('buy', sec_code))&#10;            empty_count -= 1&#10;            if empty_count &lt;= 0:&#10;                break&#10;&#10;    def prepare_cur_step_predicted_data(self, predict_date):&#10;        now_time = pd.to_datetime(predict_date)&#10;        start_time = now_time.replace(day=1)&#10;        instruments = D.list_instruments(D.instruments(market=convert_to_csi_code(config_info['market'])),&#10;                                         start_time=start_time, end_time=now_time)&#10;        instruments = [itr.lstrip('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ') for itr in instruments]&#10;        cur_step_pred_data = self.pred_data.loc[(self.pred_data['date'] == predict_date)]&#10;        cur_step_pred_data = cur_step_pred_data.loc[cur_step_pred_data['sec_id'].isin(instruments) |&#10;                                                    cur_step_pred_data['sec_id'].isin(self.buy_pre.keys())]&#10;        cur_step_pred_data['rank_sel'] = cur_step_pred_data['rank'].rank(ascending=True)&#10;        self.cur_step_pred_data = cur_step_pred_data.loc[&#10;            (cur_step_pred_data['rank_sel'] &lt;= self.topn * config_info['topn_dropout_k']) |&#10;            (cur_step_pred_data['sec_id'].isin(self.buy_pre.keys()))]&#10;        self.cur_step_pred_data = self.cur_step_pred_data.sort_values(by='rank_sel')&#10;        if config_info['random_backtest']:&#10;            n = config_info['select_range']&#10;            if n &lt; len(self.cur_step_pred_data):&#10;                subset = self.cur_step_pred_data.head(n).sample(frac=1, random_state=random.randint(0, 100000))&#10;                remaining = self.cur_step_pred_data.iloc[n:]&#10;                self.cur_step_pred_data = pd.concat([subset, remaining])&#10;            else:&#10;                self.cur_step_pred_data = self.cur_step_pred_data.sample(frac=1, random_state=random.randint(0, 100000))&#10;&#10;    def get_data_value(self, sec_id, attr, idx):&#10;        if sec_id in self.getdatanames():&#10;            return getattr(self.getdatabyname(sec_id), attr)[idx]&#10;        else:&#10;            return np.nan&#10;&#10;    def can_trade(self):&#10;        # 计算当前日期的价格变化，并仅在满足条件时允许交易&#10;        open_prices = self.cur_step_pred_data.apply(&#10;            lambda row: self.get_data_value(row['sec_id'], 'open', 0), axis=1)&#10;        close_prices = self.cur_step_pred_data.apply(&#10;            lambda row: self.get_data_value(row['sec_id'], 'close', 0), axis=1)&#10;        current_day_change = ((close_prices - open_prices) / open_prices).mean()&#10;&#10;        if np.isnan(current_day_change):&#10;            return False&#10;&#10;        if current_day_change &gt; config_info['vix_up_level']:&#10;            close_neg1 = self.cur_step_pred_data.apply(&#10;                lambda row: self.get_data_value(row['sec_id'], 'close', -1), axis=1)&#10;            current_day_change = ((close_prices - close_neg1) / close_neg1).mean()&#10;            if current_day_change &gt; config_info['vix_up_level']:&#10;                self.log(&quot;当日涨幅为%.4f，解除恐慌状态&quot; % current_day_change)&#10;                return True&#10;        elif len(self.cur_step_pred_data) &gt;= 2:&#10;            close_0 = self.cur_step_pred_data.apply(&#10;                lambda row: self.get_data_value(row['sec_id'], 'close', 0), axis=1)&#10;            close_neg1 = self.cur_step_pred_data.apply(&#10;                lambda row: self.get_data_value(row['sec_id'], 'close', -1), axis=1)&#10;            close_neg2 = self.cur_step_pred_data.apply(&#10;                lambda row: self.get_data_value(row['sec_id'], 'close', -2), axis=1)&#10;&#10;            first_day_return = ((close_0 - close_neg1) / close_neg1).mean()&#10;            second_day_return = ((close_neg1 - close_neg2) / close_neg2).mean()&#10;&#10;            if np.isnan(first_day_return) or np.isnan(second_day_return):&#10;                return False&#10;&#10;            if first_day_return &gt; 0 and second_day_return &gt; 0:&#10;                self.log(&quot;两日上涨，解除恐慌状态&quot;)&#10;                return True&#10;        return False&#10;&#10;    def evaluate_and_clear_positions(self, cur_date):&#10;        # 计算所有当前步骤预测数据的平均开盘-收盘比率&#10;        open_price = self.index_data.loc[cur_date, 'open']&#10;        close_price = self.index_data.loc[cur_date, 'close']&#10;        avg_open_close_ratio = ((open_price - close_price) / open_price).mean()&#10;&#10;        if np.isnan(avg_open_close_ratio):&#10;            return False&#10;&#10;        # 如果比率超过阈值，则清仓&#10;        if avg_open_close_ratio &gt; config_info['vix_down_level']:&#10;            self.close_all()&#10;            self.log(&quot;当日跌幅超过%.4f，进入恐慌状态&quot; % avg_open_close_ratio)&#10;            return True&#10;&#10;        prev_day = pd.to_datetime(self.data.datetime.date(-1))&#10;        prev_close = self.index_data.loc[prev_day, 'close']&#10;        avg_close_change_ratio = ((prev_close - close_price) / prev_close).mean()&#10;&#10;        if np.isnan(avg_close_change_ratio):&#10;            return False&#10;&#10;        # 如果变化比率超过阈值，则清仓&#10;        if avg_close_change_ratio &gt; config_info['vix_down_level']:&#10;            self.close_all()&#10;            self.log(&quot;当日相对上日跌幅超过%.4f，进入恐慌状态&quot; % avg_close_change_ratio)&#10;            return True&#10;        return False&#10;&#10;    def update_cur_step_data_for_empty_slots(self, empty_count):&#10;        added_feature = config_info.get('added_alpha_for_stratergy')&#10;        if added_feature:&#10;            feature_list = [feature.strip() for feature in added_feature.split(',')]&#10;            if len(feature_list) == 1:&#10;                ascending = added_feature.startswith('_')&#10;                if ascending:&#10;                    added_feature = added_feature[1:]&#10;                rerange = min(config_info['topn'], empty_count)&#10;                top2n = self.cur_step_pred_data.head(rerange)&#10;                top2n = top2n.sort_values(by=added_feature, ascending=ascending)&#10;                self.cur_step_pred_data.iloc[:rerange] = top2n&#10;            else:&#10;                coefficients = []&#10;                actual_features = []&#10;&#10;                for feature in feature_list:&#10;                    if feature.startswith('_'):&#10;                        coefficients.append(-1)&#10;                        actual_features.append(feature[1:])&#10;                    else:&#10;                        coefficients.append(1)&#10;                        actual_features.append(feature)&#10;&#10;                top2n = self.cur_step_pred_data.head(empty_count).copy()&#10;                rank_df = top2n[actual_features].rank(method='first', ascending=False)&#10;                sort_sum = rank_df.multiply(coefficients).sum(axis=1)&#10;                top2n['sort_sum'] = sort_sum&#10;                top2n_sorted = top2n.sort_values(by='sort_sum', ascending=True)&#10;                self.cur_step_pred_data.iloc[:empty_count] = top2n_sorted.drop(columns='sort_sum')&#10;&#10;        if self.rank_model is not None:&#10;            current_date = pd.to_datetime(self.datas[0].datetime.date(0))&#10;            rerange = min(config_info['topn'], empty_count)&#10;            top2n_stocks = self.cur_step_pred_data.head(rerange)['sec_id'].to_list()&#10;            rank_predictions = predict_ranks(self.rank_model, top2n_stocks, current_date)&#10;            rank_pred_data = prepare_bt_pred_data(rank_predictions, None, 0)&#10;            rank_pred_data['trade_rank'] = rank_pred_data['rank']&#10;            if not rank_predictions.empty:&#10;                self.cur_step_pred_data = pd.merge(&#10;                    self.cur_step_pred_data,&#10;                    rank_pred_data[['sec_id', 'trade_rank']],&#10;                    on='sec_id',&#10;                    how='left'&#10;                )&#10;                &#10;                topn_part = self.cur_step_pred_data.head(rerange).copy()&#10;                if not topn_part.empty:&#10;                    topn_part = topn_part.sort_values('trade_rank', ascending=True)&#10;                    self.cur_step_pred_data.iloc[:len(topn_part)] = topn_part&#10;&#10;&#10;def plot_stock_charts(stock_codes, output_dir, days=90):&#10;    os.makedirs(output_dir, exist_ok=True)&#10;    code_names = pd.read_csv(os.path.expanduser('~/stock_comments.csv'), index_col=0, dtype={'sec_id': str})&#10;    for code in stock_codes:&#10;        today = datetime.date.today()&#10;        df = D.features([f&quot;SH{code}&quot; if code.startswith('6') else f&quot;SZ{code}&quot;],&#10;                        fields=[&quot;$close&quot;, &quot;$open&quot;, &quot;$high&quot;, &quot;$low&quot;, &quot;$volume&quot;],&#10;                        start_time=today - datetime.timedelta(days=days), end_time=today)&#10;        code_name = code_names.loc[code, '名称']&#10;        if df.empty:&#10;            print(f&quot;No data available for stock {code}&quot;)&#10;            continue&#10;&#10;        df.reset_index(level=0, inplace=True)&#10;        df = df[df.index &gt;= df.index[-1] - pd.DateOffset(days=days)]&#10;&#10;        if df.empty:&#10;            print(f&quot;No data available for the last month for stock {code}-{code_name}&quot;)&#10;            continue&#10;&#10;        df.columns = ['symbol', 'close', 'open', 'high', 'low', 'volume']&#10;        plt.rcParams['font.sans-serif'] = [&#10;            'Noto Sans CJK JP']&#10;        plt.rcParams['axes.unicode_minus'] = False  # Ensure minus signs are displayed correctly&#10;&#10;        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 6), gridspec_kw={'height_ratios': [3, 1]})&#10;        mpf.plot(df, type='candle', mav=(5, 10, 20), volume=ax2, ax=ax1, show_nontrading=True)&#10;&#10;        ax1.set_title(f&quot;{code}-{code_name}&quot;)&#10;&#10;        plt.savefig(os.path.join(output_dir, f&quot;{code}.png&quot;))&#10;        plt.close()&#10;&#10;&#10;def get_intervals_from_config():&#10;    csi_code = configs.convert_to_csi_code(config_info['market'])&#10;    index_file = f'{config_info[&quot;qlib_url&quot;]}/instruments/{csi_code}.txt'&#10;    intervals = []&#10;    with open(index_file, 'r') as f:&#10;        for line in f:&#10;            line = line.strip()&#10;            if not line:&#10;                continue&#10;            parts = line.split()&#10;            if len(parts) &lt; 3:&#10;                continue&#10;            start_str, end_str = parts[1], parts[2]&#10;            start_date = datetime.datetime.strptime(start_str, '%Y-%m-%d').date()&#10;            end_date = datetime.datetime.strptime(end_str, '%Y-%m-%d').date()&#10;            intervals.append((start_date, end_date))&#10;    intervals = list(set(intervals))&#10;    intervals.sort(key=lambda x: x[0])&#10;    return intervals&#10;&#10;def prepare_rank_training_data(trade_data_path, stock_data, start_date, end_date):&#10;    if not os.path.exists(trade_data_path):&#10;        return None&#10;    trade_data = pd.read_csv(trade_data_path, parse_dates=['date'], index_col=0)&#10;    trade_data['sec_id'] = trade_data['sec_id'].apply(lambda x: str(x).zfill(6))&#10;    trade_data.reset_index(drop=True, inplace=True)&#10;    trade_data.set_index(['date', 'sec_id'], inplace=True)&#10;    trade_data = trade_data[(trade_data.index.get_level_values(0) &gt;= start_date) &amp; (trade_data.index.get_level_values(0) &lt;= end_date)]&#10;    ret = pd.merge(stock_data, trade_data, left_index=True, right_index=True, how='inner')&#10;    # ret['return_10D'] = ret.groupby(level=0)['return'].rank(ascending=True, pct=True)&#10;    return ret&#10;&#10;def _get_ranked_features_from_ic_files():&#10;    ic_table = pd.read_csv(f'a458_{config_info[&quot;market&quot;]}_ic.csv', index_col=0)&#10;    if os.path.exists(f'basic_{config_info[&quot;market&quot;]}_ic.csv'):&#10;        ic_table = pd.concat([ic_table, pd.read_csv(f'basic_{config_info[&quot;market&quot;]}_ic.csv', index_col=0)])&#10;    rank_1d = abs(ic_table.loc[:, f'{config_info[&quot;period_n&quot;]}D']).rank(ascending=False)&#10;    return rank_1d.index.to_list()&#10;&#10;&#10;def get_decorrelated_top_features(train_data: pd.DataFrame, feature_names: list, target_col: str = 'return_10D', top_k: int = 20, prefix=''):&#10;    decorrelated_features_file = f'{prefix}decorrelated_features.txt'&#10;    if os.path.exists(decorrelated_features_file):&#10;        with open(decorrelated_features_file, 'r') as f:&#10;            features = [line.strip() for line in f if line.strip()]&#10;        return features[:top_k]&#10;&#10;    valid_feature_names = [f for f in feature_names if f in train_data.columns]&#10;    X = train_data[valid_feature_names].copy()&#10;    y = train_data[target_col]&#10;&#10;    selected_features = []&#10;&#10;    tqdm_bar = tqdm.tqdm(range(min(top_k, len(valid_feature_names))), desc=&quot;Selecting Features&quot;)&#10;&#10;    for _ in tqdm_bar:&#10;        corrs = X.corrwith(y).abs()&#10;        if corrs.dropna().empty:&#10;            break&#10;&#10;        best_feature = corrs.idxmax()&#10;        selected_features.append(best_feature)&#10;        tqdm_bar.set_description(f&quot;Selected: {best_feature}&quot;)&#10;        f = X[best_feature]&#10;&#10;        for col in X.columns:&#10;            if col == best_feature:&#10;                continue&#10;&#10;            tqdm_bar.set_description(f&quot;Selected: {best_feature}， processing: {col}&quot;)&#10;&#10;            f_col = f&#10;            t_col = X[col]&#10;            valid_idx = f_col.notna() &amp; t_col.notna()&#10;            valid_idx &amp;= (~f_col.isin([np.inf, -np.inf])) &amp; (~t_col.isin([np.inf, -np.inf]))&#10;            valid_idx &amp;= f_col.abs() &lt; 1e10&#10;            valid_idx &amp;= t_col.abs() &lt; 1e10&#10;&#10;            if valid_idx.sum() &lt; 2:&#10;                continue&#10;&#10;            reg = LinearRegression()&#10;            f_valid = f_col[valid_idx].values.reshape(-1, 1)&#10;            col_valid = t_col[valid_idx].values&#10;            reg.fit(f_valid, col_valid)&#10;&#10;            pred = np.full_like(f_col, fill_value=np.nan, dtype=np.float64)&#10;            pred[valid_idx] = reg.predict(f_valid)&#10;&#10;            X[col] = X[col] - pred&#10;&#10;        X.drop(columns=[best_feature], inplace=True)&#10;&#10;        if X.empty:&#10;            break&#10;&#10;    with open(decorrelated_features_file, 'w') as f:&#10;        for feature in selected_features:&#10;            f.write(f&quot;{feature}\n&quot;)&#10;    return selected_features&#10;&#10;&#10;def train_rank_model(trade_data_path, stock_data, start_date, end_date, save_path):&#10;    train_data = prepare_rank_training_data(trade_data_path, stock_data, start_date, end_date)&#10;    if train_data is None or len(train_data) &lt; 1000:&#10;        return None&#10;    rfid = config_info['remove_from_fid']&#10;    config_info['remove_from_fid'] = 300&#10;    td = config_info['tuning_days']&#10;    config_info['tuning_days'] = 0&#10;    presets = config_info['presets']&#10;    config_info['presets'] = 'medium_quality'&#10;    tt = config_info['train_type']&#10;    config_info['train_type'] = 'rank'&#10;    dtf = config_info['decorrelated_top_features']&#10;    config_info['decorrelated_top_features'] = False&#10;    zh = config_info['zeroshot']&#10;    config_info['zeroshot'] = False&#10;&#10;    feature_names = _get_ranked_features_from_ic_files()&#10;    feature_names = get_decorrelated_top_features(train_data, feature_names, top_k=20, prefix='rank_')&#10;&#10;    ret = train_model(train_data, feature_names, config_info['period_n'], start_date, end_date, save_path,&#10;                      selected_hyper={'LR':{}})&#10;&#10;    config_info['remove_from_fid'] = rfid&#10;    config_info['tuning_days'] = td&#10;    config_info['presets'] = presets&#10;    config_info['train_type'] = tt&#10;    config_info['decorrelated_top_features'] = dtf&#10;    config_info['zeroshot'] = zh&#10;    return ret&#10;&#10;&#10;def predict_ranks(model, stocks, date):&#10;    df = load_stock_data(config_info['alpha_type'], config_info['market'])&#10;    feature_names = _get_ranked_features_from_ic_files()&#10;    pred_data = prepare_data_for_model(df, feature_names, config_info['period_n'], date, date)&#10;    pred_data = pred_data.loc[pred_data.index.get_level_values(1).isin(stocks)]&#10;    return predict_proba(model, pred_data)" />
              <option name="updatedContent" value="import datetime&#10;import logging&#10;import os&#10;import random&#10;import smtplib&#10;import warnings&#10;from email.header import Header&#10;from email.mime.application import MIMEApplication&#10;from email.mime.multipart import MIMEMultipart&#10;from email.mime.text import MIMEText&#10;&#10;import alphalens&#10;import backtrader as bt&#10;import matplotlib.pyplot as plt&#10;import mplfinance as mpf&#10;import numpy as np&#10;import openai&#10;import pandas as pd&#10;import qlib&#10;from alphalens import performance as perf&#10;from alphalens.utils import MaxLossExceededError&#10;from autogluon.core.metrics import make_scorer&#10;from autogluon.tabular import TabularPredictor&#10;from langchain.chat_models import AzureChatOpenAI&#10;from langchain.embeddings import OpenAIEmbeddings&#10;from numba import njit, prange&#10;from qlib.data import D&#10;from scipy.stats import zscore, pearsonr&#10;from sklearn.linear_model import LinearRegression&#10;from sklearn.metrics import confusion_matrix&#10;from sklearn.metrics import mean_squared_error&#10;from sklearn.metrics import r2_score&#10;import tqdm&#10;&#10;import configs&#10;from configs import config_info, convert_to_csi_code&#10;&#10;warnings.filterwarnings('ignore')&#10;&#10;global cache&#10;cache = {}&#10;qlib_inited = False&#10;MAX_FEATURE_COUNT = 1000&#10;&#10;MAX_TIME_SUB_MODEL=2000&#10;&#10;ALL_HYPERPARAMETERS = {&#10;    'GBM': [&#10;        {&#10;            'seed': 0,&#10;            'bagging_seed': 0,&#10;            'feature_fraction_seed': 0,&#10;            'data_random_seed': 0,&#10;            'feature_fraction': 0.9,&#10;            &quot;learning_rate&quot;: 0.03,&#10;            &quot;num_leaves&quot;: 128,&#10;            &quot;min_data_in_leaf&quot;: 5,&#10;            'ag_args': dict(model_type=&quot;GBM&quot;, name_suffix=&quot;Large&quot;, priority=0),&#10;        },&#10;        {&#10;            &quot;extra_trees&quot;: True,&#10;            'seed': 0,&#10;            'bagging_seed': 0,&#10;            'feature_fraction_seed': 0,&#10;            'drop_seed': 0,&#10;            'extra_seed': 0,&#10;            'objective_seed': 0,&#10;            'data_random_seed': 0,&#10;            'bagging_fraction': 1.0,&#10;            'feature_fraction': 1.0,&#10;            'bagging_freq': 0,&#10;            &quot;ag_args&quot;: {&quot;name_suffix&quot;: &quot;XT&quot;, &quot;priority&quot;: 0},&#10;            'early_stopping_round' : 100,&#10;            'force_col_wise': True,&#10;        },&#10;        {&#10;            'seed': 0,&#10;            'bagging_seed': 0,&#10;            'feature_fraction_seed': 0,&#10;            'data_random_seed': 0,&#10;        },&#10;    ],&#10;    'XGB': {&#10;        'seed': 0,&#10;    },&#10;    &quot;CAT&quot;: {&#10;        'random_seed': 0,&#10;    },&#10;    &quot;FASTAI&quot;: {&#10;        'seed': 0,&#10;    },&#10;    &quot;NN_TORCH&quot;: {&#10;    },&#10;    &quot;RF&quot;: {&#10;        &quot;criterion&quot;: &quot;squared_error&quot;,&#10;        &quot;random_state&quot;: 0,&#10;        &quot;ag_args&quot;: {&quot;name_suffix&quot;: &quot;MSE&quot;, &quot;problem_types&quot;: [&quot;regression&quot;, &quot;quantile&quot;]}&#10;    },&#10;    &quot;XT&quot;: {&#10;        &quot;criterion&quot;: &quot;squared_error&quot;,&#10;        &quot;random_state&quot;: 0,&#10;        &quot;ag_args&quot;: {&quot;name_suffix&quot;: &quot;MSE&quot;, &quot;problem_types&quot;: [&quot;regression&quot;, &quot;quantile&quot;]},&#10;    },&#10;    &quot;KNN&quot;: [{&quot;weights&quot;: &quot;uniform&quot;, &quot;ag_args&quot;: {&quot;name_suffix&quot;: &quot;Unif&quot;}}],&#10;}&#10;&#10;&#10;class AnnualizedSortinoRatio(bt.Analyzer):&#10;    params = (&#10;        ('riskfreerate', 0.0),  # 年化无风险收益率，例如 0.03 表示 3%&#10;        ('trading_days', 252),  # 一年中的交易日数量&#10;    )&#10;&#10;    def start(self):&#10;        self.returns = []  # 用于存储每日收益率&#10;        # 记录回测开始时的账户总值&#10;        self.prev_value = self.strategy.broker.getvalue()&#10;&#10;    def next(self):&#10;        current_value = self.strategy.broker.getvalue()&#10;        # 计算当期（每日）的收益率&#10;        # daily_ret 为当天收益率 = 当天账户价值 / 前一天账户价值 - 1&#10;        if self.prev_value != 0:&#10;            daily_ret = (current_value / self.prev_value) - 1.0&#10;            self.returns.append(daily_ret)&#10;        self.prev_value = current_value  # 更新前一日账户价值&#10;&#10;    def stop(self):&#10;        returns = np.array(self.returns)&#10;        # 将年化的无风险收益率转换为每日无风险收益率（简单除以交易日数量）&#10;        daily_rf = self.p.riskfreerate / self.p.trading_days&#10;        # 每日超额收益 = 每日实际收益 - 每日无风险收益率&#10;        excess_returns = returns - daily_rf&#10;&#10;        # 年化平均超额收益率 = 日均超额收益 * 交易日数&#10;        annualized_excess_return = np.mean(excess_returns) * self.p.trading_days&#10;&#10;        # 只选取亏损的那部分收益（超额收益为负的部分）&#10;        downside_excess = excess_returns[excess_returns &lt; 0]&#10;        if downside_excess.size == 0:&#10;            annualized_downside_std = 0.0&#10;        else:&#10;            # 年化下行波动率 = 日下行波动率 * sqrt(交易日数)&#10;            annualized_downside_std = np.std(downside_excess) * np.sqrt(self.p.trading_days)&#10;&#10;        if annualized_downside_std == 0:&#10;            sortino = float('inf')&#10;        else:&#10;            sortino = annualized_excess_return / annualized_downside_std&#10;&#10;        # 将计算结果存入 self.rets&#10;        self.rets = {'sortino': sortino}&#10;&#10;    def get_analysis(self):&#10;        return self.rets&#10;&#10;&#10;def sign_step(x):&#10;    return x if x &gt; 0 else 0&#10;&#10;&#10;def init_qlib():&#10;    global qlib_inited&#10;    if not qlib_inited:&#10;        configs.init_config()&#10;        qlib.init(provider_uri=config_info['qlib_url'])&#10;        qlib_inited = True&#10;&#10;    if os.path.exists(&quot;interest.txt&quot;):&#10;        with open(&quot;interest.txt&quot;, &quot;r&quot;) as f:&#10;            interest_str = f.read()&#10;        interest_list = interest_str.split(&quot;\n&quot;)&#10;        interest_list = [x for x in interest_list if x]&#10;        config_info['interest'] = interest_list&#10;&#10;&#10;def create_llm(model='deepseek-chat'):&#10;    # llm = ChatOpenAI(openai_api_base='https://api.deepseek.com/v1',&#10;    #                  openai_api_key=&quot;sk-0c9e855553a04ef2afcb675bea3d5dc9&quot;,&#10;    #                  model=model)&#10;    llm = AzureChatOpenAI(deployment_name=&quot;gpt-4o&quot;, openai_api_version=&quot;2024-05-01-preview&quot;,&#10;                          model_name='gpt-4o')&#10;    return llm&#10;&#10;&#10;def create_embedding():&#10;    embeddings = OpenAIEmbeddings(&#10;        deployment=&quot;text-embedding-ada-002&quot;,&#10;        model=&quot;text-embedding-ada-002&quot;,&#10;        chunk_size=1,&#10;    )&#10;    return embeddings&#10;&#10;&#10;def init_gpt():&#10;    openai.api_type = os.getenv('OPENAI_API_TYPE')&#10;    openai.api_base = os.getenv('OPENAI_API_BASE')&#10;    openai.api_key = os.getenv('OPENAI_API_KEY')&#10;    openai.api_version = os.getenv('OPENAI_API_VERSION')&#10;&#10;    if openai.api_key:&#10;        os.environ[&quot;OPENAI_API_KEY&quot;] = openai.api_key&#10;    if openai.api_type:&#10;        os.environ['OPENAI_API_TYPE'] = openai.api_type&#10;    if openai.api_base:&#10;        os.environ['OPENAI_API_BASE'] = openai.api_base&#10;    if openai.api_version:&#10;        os.environ['OPENAI_API_VERSION'] = openai.api_version&#10;&#10;    serpapi_api_key = os.getenv('SERPAPI_API_KEY')&#10;    if serpapi_api_key:&#10;        os.environ[&quot;SERPAPI_API_KEY&quot;] = serpapi_api_key&#10;&#10;&#10;def predict_proba(model, test_data, sub_model_name=None):&#10;    if config_info['train_type'] == 'reg':&#10;        predictions = model.predict(test_data, model=sub_model_name)&#10;        &#10;        if 'return' in test_data.columns:&#10;            y_true = test_data['return']&#10;            # 过滤掉NaN值&#10;            valid_mask = ~(pd.isna(y_true) | pd.isna(predictions))&#10;            if valid_mask.sum() &gt; 0:&#10;                y_true_valid = y_true[valid_mask]&#10;                predictions_valid = predictions[valid_mask]&#10;                r2 = r2_score(y_true_valid, predictions_valid)&#10;                logging.getLogger().info(f&quot;R² Score: {r2:.4f}&quot;)&#10;                &#10;                ic_corr, ic_p_value = pearsonr(predictions_valid, y_true_valid)&#10;                logging.getLogger().info(f&quot;IC Score: {ic_corr:.4f}&quot;)&#10;        return predictions&#10;    else:&#10;        return model.predict_proba(test_data, model=sub_model_name)&#10;&#10;&#10;def clear_cache():&#10;    cache.clear()&#10;&#10;&#10;def load_stock_data(alpha_type, market, prefix=''):&#10;    file_name = f'refined_{prefix}{alpha_type}_{market}.pkl'&#10;    if cache.get(file_name) is None:&#10;        df = pd.read_pickle(file_name)&#10;        df = df.loc[:, ~df.columns.duplicated()]&#10;        cache[file_name] = df&#10;&#10;    ret = cache.get(file_name)&#10;    if len(config_info['secid_filter_train']) &gt; 0:&#10;        ret = ret[ret.index.get_level_values(1).isin(config_info['secid_filter_train'])]&#10;    return ret&#10;&#10;&#10;def get_flag_by_gates(changes, gates):&#10;    flags = changes.apply(lambda row: (row &gt;= row.quantile(gates)).astype(int), axis=1)&#10;    return flags&#10;&#10;&#10;def refine_stock_df(df, gates=[(1, 0.9), (5, 0.9), (10, 0.9), (20, 0.9)], with_flag=True):&#10;    if config_info['index_enhance']:&#10;        index_path = f'./{config_info[&quot;market&quot;]}.csv'&#10;        stock_index = pd.read_csv(index_path, index_col=0)&#10;        stock_index.index = pd.to_datetime(stock_index.index)&#10;        stock_index = stock_index[['close']].rename(columns={'close': 'index_close'}).reset_index()&#10;        df_reset = df.reset_index()&#10;        df = pd.merge(df_reset, stock_index, on='date', how='left')&#10;        df.set_index(['date', 'sec_id'], inplace=True)&#10;&#10;    df.reset_index(inplace=True)&#10;    if 'return' in df.columns:&#10;        returns = df.pivot(index='date', columns='sec_id', values='return')&#10;    else:&#10;        returns = df.pivot(index='date', columns='sec_id', values='return_1D')&#10;    close = df.pivot(index='date', columns='sec_id', values='close')&#10;&#10;    if 'index_close' in df.columns:&#10;        index_close = df.pivot(index='date', columns='sec_id', values='index_close')&#10;    else:&#10;        index_close = None&#10;&#10;    for i, j in gates:&#10;        if i != 1:&#10;            changes = close.pct_change(i)&#10;            if index_close is not None:&#10;                index_changes = index_close.pct_change(i)&#10;        else:&#10;            changes = returns.copy()&#10;            if index_close is not None:&#10;                index_changes = index_close.pct_change(1)&#10;&#10;        if f'return_{i}D' in df.columns:&#10;            df.drop(columns=[f'return_{i}D'], inplace=True)&#10;        if f'flag_{i}D' in df.columns:&#10;            df.drop(columns=[f'flag_{i}D'], inplace=True)&#10;&#10;        changes = changes.shift(-i)&#10;        if index_close is not None:&#10;            index_changes = index_changes.shift(-i)&#10;            changes = changes - index_changes&#10;            daily_std = changes.std(axis=1, skipna=True)&#10;            mean_std = daily_std.mean()&#10;            changes = changes.div(daily_std, axis=0) * mean_std&#10;            clip_range = config_info['max_return'][i] * 2&#10;            changes = changes.clip(-clip_range, clip_range)&#10;            # 打印changes的统计信息&#10;            print(f'return_{i}D changes统计信息:')&#10;            print(f'  形状: {changes.shape}')&#10;            print(f'  均值: {changes.mean().mean():.6f}')&#10;            print(f'  标准差: {changes.std().mean():.6f}')&#10;            print(f'  最小值: {changes.min().min():.6f}')&#10;            print(f'  最大值: {changes.max().max():.6f}')&#10;            print(f'  daily_std均值: {mean_std:.6f}')&#10;            print(f'  daily_std标准差: {daily_std.std():.6f}')&#10;&#10;        if with_flag:&#10;            print(f'flag_{i}D')&#10;            flags = get_flag_by_gates(changes, j)&#10;            flags = flags.reset_index().melt(id_vars='date', var_name='sec_id', value_name='return')&#10;            print(flags['return'].value_counts() / len(flags))&#10;            flags.rename(columns={'return': f'flag_{i}D'}, inplace=True)&#10;&#10;        changes = changes.reset_index().melt(id_vars='date', var_name='sec_id', value_name='return')&#10;        changes.rename(columns={'return': f'return_{i}D'}, inplace=True)&#10;        df = df.merge(changes, on=['date', 'sec_id'], how='left')&#10;&#10;        if with_flag:&#10;            df = df.merge(flags, on=['date', 'sec_id'], how='right')&#10;&#10;    if 'return' in df.columns:&#10;        df.dropna(subset=['return'], inplace=True)&#10;    else:&#10;        df.dropna(subset=['return_1D'], inplace=True)&#10;        df['return'] = df['return_1D']&#10;&#10;    if index_close is not None:&#10;        df.drop(columns=['index_close'], inplace=True)&#10;    df.set_index(['date', 'sec_id'], inplace=True)&#10;    return df&#10;&#10;&#10;def refine_stock_data(alpha_type, prefix=''):&#10;    df = pd.read_pickle(f'{prefix}{alpha_type}_{config_info[&quot;market&quot;]}.pkl')&#10;    df = refine_stock_df(df)&#10;    df.to_pickle(f'refined_{prefix}{alpha_type}_{config_info[&quot;market&quot;]}.pkl')&#10;&#10;&#10;def get_feature_names(alpha_type, market, period_n, feature_count=-1):&#10;    if period_n &gt; 10:&#10;        period_n = 10&#10;    year = config_info.get('ic_year')&#10;    ic_file = f'{alpha_type}_{market}_ic_{year}.csv'&#10;    if not os.path.exists(ic_file):&#10;        ic_file = f'{alpha_type}_{market}_ic.csv'&#10;    if not os.path.exists(ic_file):&#10;        return []&#10;    ic_table = pd.read_csv(ic_file, index_col=0)&#10;    rank_1d = abs(ic_table.loc[:, f'{period_n}D']).rank(ascending=False)&#10;    feature_abandon = f'feature_abandon_{year}' if os.path.exists(f'feature_abandon_{year}') else &quot;feature_abandon&quot;&#10;    if not config_info['decorrelated_top_features'] and config_info['remove_from_fid'] &gt; 0 and os.path.exists(f&quot;feature_importance.csv&quot;):&#10;        df = pd.read_csv(f&quot;feature_importance.csv&quot;, index_col=0)&#10;        df = df.sort_values(by='p99_high', ascending=True)&#10;        df = df.iloc[config_info['remove_from_fid']:]&#10;        selected_features = df.index.tolist()&#10;        if config_info['use_basic_factors']:&#10;            # 获取以basic_开头的列名并加入selected_features，注意去重&#10;            basic_features = [col for col in df.index if col.startswith('basic_')]&#10;            selected_features.extend(basic_features)&#10;            # 去重，保持顺序&#10;            selected_features = list(dict.fromkeys(selected_features))&#10;        return selected_features&#10;    elif os.path.exists(feature_abandon):&#10;        with open(feature_abandon, &quot;r&quot;) as f:&#10;            abandoned = [line.strip() for line in f if line.strip()]&#10;            rank_1d = rank_1d[~rank_1d.index.isin(abandoned)]&#10;    if feature_count &lt;= 0:&#10;        feature_count = config_info['feature_count']&#10;    factor_names = rank_1d[rank_1d &lt;= feature_count].index.tolist()&#10;    if config_info['index_feature_count'] &gt; 0:&#10;        factor_names.extend([f'index_feature{i}' for i in range(1, config_info['index_feature_count'] + 1)])&#10;    return factor_names&#10;&#10;&#10;def reshape_daily_stock_data(stock_code, stock_df, save_path):&#10;    stock_df.rename(columns={'开盘': 'open', '收盘': 'close', '最高': 'high', '最低': 'low', '成交量': 'volume',&#10;                             '成交额': 'amount', '换手率': 'rate', '日期': 'date'}, inplace=True)&#10;    stock_df.drop(columns=['振幅', '涨跌幅', '涨跌额'], inplace=True)&#10;    stock_df.to_csv(f'./{save_path}/{stock_code}.csv')&#10;    stock_df['date'] = pd.to_datetime(stock_df['date'])&#10;    stock_df.set_index('date', inplace=True)&#10;    return stock_df&#10;&#10;&#10;def compute_alphalen_factor(returns, factor_name, stock_data):&#10;    feature = stock_data.loc[:, factor_name]&#10;&#10;    try:&#10;        factors = alphalens.utils.get_clean_factor_and_forward_returns(feature, returns,&#10;                                                                       bins=None,&#10;                                                                       periods=(1, 5, 10), quantiles=5,&#10;                                                                       max_loss=0.35)&#10;    except MaxLossExceededError as e:&#10;        print(e)&#10;        print('fall back to binn mode')&#10;        min_f, max_f = min(feature), max(feature)&#10;        try:&#10;            factors = alphalens.utils.get_clean_factor_and_forward_returns(feature, returns,&#10;                                                                           bins=np.linspace(min_f, max_f, 5),&#10;                                                                           periods=(1, 5, 10), quantiles=None,&#10;                                                                           max_loss=0.52)&#10;        except MaxLossExceededError as e:&#10;            factors = alphalens.utils.get_clean_factor_and_forward_returns(feature, returns,&#10;                                                                           bins=3,&#10;                                                                           periods=(1, 5, 10), quantiles=None,&#10;                                                                           max_loss=0.52)&#10;    return factors&#10;&#10;&#10;def get_instrument_list(full_mode=False):&#10;    market = convert_to_csi_code(config_info['market'])&#10;    start = None&#10;    end = None&#10;    if config_info['generate_ins_date'] is not None:&#10;        start = config_info['generate_ins_date'][0]&#10;        end = config_info['generate_ins_date'][1]&#10;    instruments = []&#10;    while len(instruments) == 0:&#10;        if full_mode:&#10;            instruments = D.instruments(market=market)&#10;        else:&#10;            instruments = [item.lstrip('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ') for item in&#10;                           D.list_instruments(D.instruments(market=market), start_time=start, end_time=end)]&#10;        # 将end往后延一年&#10;        if end is not None:&#10;            end = pd.to_datetime(end)&#10;            end = end + pd.DateOffset(years=1)&#10;    return instruments&#10;&#10;&#10;def compute_ic_table_for_data(df, start=None, end=None, existing_ic=None):&#10;    if start is not None and end is not None:&#10;        df = df.loc[(start &lt; df.index.get_level_values(0)) &amp; (df.index.get_level_values(0) &lt; end), :]&#10;    returns = df.loc[:, ['close']].copy()&#10;    returns = returns.pivot_table(index='date', columns='sec_id', values='close')&#10;    returns.rename(columns={'close': 'returns'}, inplace=True)&#10;    ret = pd.DataFrame()&#10;    for col in df.columns:&#10;        if col in ['return', 'open', 'high', 'low', 'close', 'volume', 'vwap', 'index_close', 'amount', 'adjclose',&#10;                   'factor',&#10;                   'benchmark_open', 'benchmark_close']:&#10;            continue&#10;        if col.startswith('flag') or col.startswith('return'):&#10;            continue&#10;        if existing_ic and col in existing_ic:&#10;            continue&#10;        print('compute ic for %s' % col)&#10;        factors = compute_alphalen_factor(returns, col, df)&#10;        ic = perf.mean_information_coefficient(factors)&#10;        ic = abs(ic)&#10;        ret = pd.concat([ret, pd.DataFrame(ic.T, columns=[col])], axis=1)&#10;&#10;    return ret.T&#10;&#10;&#10;def prepare_data_for_model(stock_data, factors, period, start_date, end_date, keep_close=False):&#10;    end_date = pd.to_datetime(end_date)&#10;    if config_info['train_type'] == 'cls':&#10;        label_tag = f'flag_{period}D'&#10;        drop_label = f'return_{period}D'&#10;    else:&#10;        label_tag = f'return_{period}D'&#10;        drop_label = f'flag_{period}D'&#10;&#10;    extra_labels = [label_tag, drop_label, 'return']&#10;    if keep_close:&#10;        extra_labels.append('close')&#10;    extra_labels = [col for col in extra_labels if col in stock_data.columns]&#10;&#10;    labels = factors + extra_labels&#10;    if 'regime_id' in stock_data.columns and 'regime_id' not in labels:&#10;        labels.append('regime_id')&#10;&#10;    ret_data = stock_data.loc[&#10;        (stock_data.index.get_level_values(0) &gt;= start_date) &amp;&#10;        (stock_data.index.get_level_values(0) &lt;= end_date),&#10;        labels&#10;    ].copy()&#10;&#10;    if not config_info['auto_feature_select']:&#10;        if config_info['train_type'] == 'reg':&#10;            def weights_caculator(row, end):&#10;                base_weight = row.get(f'return_{period}D', 0) / config_info['max_return'][period]&#10;                weights = min(config_info['max_weight'], abs(base_weight))&#10;                if config_info['weights421']:&#10;                    days_diff = (end - row.name[0]).days&#10;                    max_level_days = max(config_info['more_weights_days'], 180)&#10;                    sec_level_days = max_level_days * 2&#10;                    if days_diff &lt; max_level_days:&#10;                        return 40 * (weights ** config_info['curve_2'])&#10;                    elif days_diff &lt; sec_level_days:&#10;                        return 30 * (weights ** config_info['curve_2'])&#10;                return 10 * (weights ** config_info['curve'])&#10;&#10;            ret_data['weight'] = ret_data.apply(weights_caculator, args=(end_date,), axis=1)&#10;        elif config_info['train_type'] == 'cls':&#10;            if f'flag_{period}D' in ret_data.columns:&#10;                weight = ret_data[f'flag_{period}D'].value_counts() / len(ret_data)&#10;                weight = (1 - weight) / min(1 - weight)&#10;                ret_data['weight'] = ret_data[f'flag_{period}D'].apply(lambda x: weight[x])&#10;            else:&#10;                ret_data['weight'] = 1&#10;&#10;    for col in ['return', drop_label]:&#10;        if col in ret_data.columns:&#10;            ret_data.drop(columns=[col], inplace=True)&#10;    if label_tag in ret_data.columns:&#10;        ret_data.rename(columns={label_tag: 'return'}, inplace=True)&#10;&#10;    if config_info['train_with_add_features']:&#10;        add_features = prepare_add_features(start_date, end_date, False)&#10;        extra_feat = f'return_{config_info[&quot;period_n&quot;]}D'&#10;        if extra_feat in add_features.columns:&#10;            add_features.drop(columns=[extra_feat], inplace=True)&#10;        ret_data = pd.merge(ret_data, add_features, left_index=True, right_index=True, how='left')&#10;&#10;    ret_data.dropna(subset=ret_data.columns.difference(['return']), inplace=True)&#10;    ret_data = ret_data.loc[~ret_data.isin([np.inf, -np.inf]).any(axis=1)]&#10;&#10;    return ret_data&#10;&#10;&#10;def load_model(model_name):&#10;    if not os.path.exists('models/' + model_name):&#10;        return None&#10;    try:&#10;        ret = TabularPredictor.load('models/' + model_name, require_version_match=False, verbosity=0)&#10;        return ret if ret.is_fit else None&#10;    except:&#10;        return None&#10;&#10;&#10;def stock_metric(y_true, y_pred):&#10;    cm = confusion_matrix(y_true, y_pred, labels=[1, 0])&#10;    total = 1 if cm[:, 0].sum() == 0 else cm[:, 0].sum()&#10;    precision_1 = cm[0, 0] / total&#10;    false_positive_ratio_3 = cm[1, 0] / total&#10;    result = precision_1 - false_positive_ratio_3&#10;    return result&#10;&#10;&#10;def prepare_add_features(start_date, end_date, with_main_features=True):&#10;    label = f'return_{config_info[&quot;period_n&quot;]}D'&#10;    stock_data = load_stock_data(config_info['alpha_type'], config_info['market'])&#10;    df = stock_data.loc[&#10;        (stock_data.index.get_level_values(0) &gt;= start_date) &amp; (stock_data.index.get_level_values(0) &lt;= end_date), [&#10;            label]]&#10;&#10;    add_df = pd.read_csv(f'{config_info[&quot;market&quot;]}_additional_features.csv', parse_dates=['date'],&#10;                         dtype={'stock_code': str})&#10;    add_df = add_df.loc[(add_df['date'] &gt;= start_date) &amp; (add_df['date'] &lt;= end_date)]&#10;    add_df.rename(columns={'stock_code': 'sec_id'}, inplace=True)&#10;    add_df['sec_id'] = add_df['sec_id'].apply(lambda x: x.zfill(6))&#10;    add_df.set_index(['date', 'sec_id'], inplace=True)&#10;&#10;    df = pd.merge(df, add_df, left_index=True, right_index=True, how='left')&#10;&#10;    if with_main_features:&#10;        period = config_info['period_n']&#10;        factors = get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'])&#10;        train_data = prepare_data_for_model(stock_data, factors, period, start_date, end_date)&#10;        train_data = remove_lastdays_from_data(train_data)&#10;        train_data.drop(columns=['return'], inplace=True)&#10;        df = pd.merge(df, train_data, left_index=True, right_index=True, how='left')&#10;    return df&#10;&#10;&#10;def remove_lastdays_from_data(train_data):&#10;    train_data = train_data.sort_index(level=0, ascending=False)&#10;    last_n_days = train_data.index.get_level_values(0).unique()[:config_info['period_n']]&#10;    train_data = train_data[~train_data.index.get_level_values(0).isin(last_n_days)]&#10;    train_data.dropna(inplace=True)&#10;    return train_data&#10;&#10;&#10;def compute_batch_loss(pos_batch, neg_scores):&#10;    &quot;&quot;&quot;&#10;    对一个正样本批次与所有负样本计算 pairwise 排序损失，&#10;    返回该批次损失总和和元素个数。&#10;    &quot;&quot;&quot;&#10;    differences_batch = pos_batch[:, None] - neg_scores[None, :]&#10;    batch_loss = np.log1p(np.exp(-differences_batch))&#10;    return np.sum(batch_loss), batch_loss.size&#10;&#10;&#10;@njit(parallel=True, fastmath=True)&#10;def pairwise_logistic_loss_parallel(pos_scores, neg_scores):&#10;    total_loss = 0.0&#10;    # 并行化外层循环，每次处理一个正样本对应所有负样本对&#10;    for i in prange(pos_scores.shape[0]):&#10;        local_loss = 0.0&#10;        # 内层循环不做并行处理，累加当前正样本与所有负样本的损失&#10;        for j in range(neg_scores.shape[0]):&#10;            diff = pos_scores[i] - neg_scores[j]&#10;            local_loss += np.log1p(np.exp(-diff))&#10;        total_loss += local_loss  # 这里使用加法累加各线程局部结果&#10;    count = pos_scores.shape[0] * neg_scores.shape[0]&#10;    return total_loss, count&#10;&#10;&#10;def mixed_loss_metric(y_true, y_pred, pos_weight=0.9, neg_weight=0.1, lambda_rank=1.0):&#10;    eps = 1e-8&#10;    y_pred = np.clip(y_pred, eps, 1 - eps)&#10;    # 加权二元交叉熵损失&#10;    bce_loss = - (pos_weight * y_true * np.log(y_pred) +&#10;                  neg_weight * (1 - y_true) * np.log(1 - y_pred))&#10;    bce_loss_mean = np.mean(bce_loss)&#10;&#10;    # 将预测概率转换为 logits&#10;    logits = np.log(y_pred / (1 - y_pred))&#10;&#10;    pos_idx = np.where(y_true == 1)[0]&#10;    neg_idx = np.where(y_true == 0)[0]&#10;&#10;    if len(pos_idx) == 0 or len(neg_idx) == 0:&#10;        ranking_loss = 0.0&#10;    else:&#10;        pos_scores = logits[pos_idx]&#10;        neg_scores = logits[neg_idx]&#10;        total_loss, count = pairwise_logistic_loss_parallel(pos_scores, neg_scores)&#10;        ranking_loss = total_loss / count&#10;&#10;    total_loss_final = bce_loss_mean + lambda_rank * ranking_loss&#10;    return total_loss_final&#10;&#10;&#10;class MixedLossMetric(object):&#10;    def __init__(self, pos_weight=0.9, neg_weight=0.1, lambda_rank=1.0):&#10;        self.pos_weight = pos_weight&#10;        self.neg_weight = neg_weight&#10;        self.lambda_rank = lambda_rank&#10;        self.greater_is_better = False  # 损失越低越好&#10;        self.greater_is_better_internal = self.greater_is_better&#10;        self.needs_pred = False  # 表示不需要&quot;预测标签&quot;&#10;        self.needs_proba = True  # 表示需要概率&#10;        self.needs_quantile = False&#10;        self.name = &quot;mixed_loss_metric&quot;&#10;        self._optimum = 1&#10;        self.needs_class = True&#10;&#10;    def __call__(self, y_true, y_pred, weight=None):&#10;        if y_pred.ndim &gt; 1 and y_pred.shape[1] &gt; 1:&#10;            y_pred = y_pred[:, 1]&#10;        return mixed_loss_metric(y_true, y_pred, self.pos_weight, self.neg_weight, self.lambda_rank)&#10;&#10;&#10;def top_mean_squarer(y_true, y_pred, *, sample_weight=None, multioutput=&quot;uniform_average&quot;, squared=False):&#10;    sorted_idx = np.argsort(-y_pred)&#10;    cutoff = int(np.ceil(len(y_pred) * 0.1))&#10;    top_idx = sorted_idx[:cutoff]&#10;    return mean_squared_error(y_true[top_idx], y_pred[top_idx], sample_weight=None, multioutput=multioutput,&#10;                              squared=squared)&#10;&#10;&#10;def train_model(stock_data, factors, period, start_date, end_date, save_path, selected_hyper=True):&#10;    if config_info['decorrelated_top_features']:&#10;        factors = get_decorrelated_top_features(stock_data, factors, f'return_{period}D', len(factors) - config_info['remove_from_fid'])&#10;    if 'regime_id' in stock_data.columns and 'regime_id' not in factors:&#10;        factors.append('regime_id')&#10;    train_data = prepare_data_for_model(stock_data, factors, period, start_date, end_date)&#10;    train_data = remove_lastdays_from_data(train_data)&#10;    last_day = max(train_data.index.get_level_values(0).unique())&#10;    metric = None&#10;    if config_info['train_type'] == 'reg':&#10;        metric = make_scorer(&quot;mean_squared_error&quot;, top_mean_squarer, optimum=0, greater_is_better=False)&#10;    # elif  config_info['train_type'] == 'cls':&#10;    #    metric = make_scorer(name='stock_metric', score_func=stock_metric)&#10;    #    metric = MixedLossMetric(pos_weight=0.9, neg_weight=0.1, lambda_rank=1.0)&#10;&#10;    if config_info['auto_feature_select'] or config_info['train_type'] == 'rank':&#10;        weight = None&#10;    else:&#10;        weight = 'weight'&#10;    presets = config_info['presets']&#10;    tuning_days = config_info['tuning_days']&#10;    good_quality = (presets not in ['medium_quality'])&#10;&#10;    if tuning_days &gt; 0:&#10;        tuned_date = train_data.index.get_level_values(0).max() - pd.DateOffset(days=tuning_days)&#10;        tuned_data = train_data.loc[train_data.index.get_level_values(0) &gt;= tuned_date, :]&#10;&#10;        unique_sec_ids = tuned_data.index.get_level_values(1).unique()&#10;        selected_sec_ids = np.random.RandomState(0).choice(&#10;            unique_sec_ids, size=len(unique_sec_ids)//2, replace=False)&#10;&#10;        with open('tuning_sec_ids.txt', 'w') as f:&#10;            for sec_id in selected_sec_ids:&#10;                f.write(f&quot;{sec_id}\n&quot;)&#10;        tuned_data_sample = tuned_data.loc[tuned_data.index.get_level_values(1).isin(selected_sec_ids)]&#10;        train_data = pd.concat([&#10;            train_data.loc[train_data.index.get_level_values(0) &lt;= tuned_date, :],&#10;            tuned_data.loc[~tuned_data.index.get_level_values(1).isin(selected_sec_ids), :]&#10;        ])&#10;        tuned_data = tuned_data_sample&#10;        holdout_frac = None&#10;    else:&#10;        tuned_data = None&#10;        holdout_frac = 0.1&#10;&#10;    use_bag_holdout = True&#10;    global_seed = config_info['train_seed']&#10;    kwargs = {}&#10;    groups_name = None&#10;    if good_quality:&#10;        folds = config_info['num_bag_folds']&#10;        kwargs = {&#10;            'num_stack_levels': config_info['num_stack_levels'],&#10;            'num_bag_folds': folds,&#10;        }&#10;        if folds &gt; 0:&#10;            groups_name = 'group'&#10;            train_indices = train_data.index.get_level_values(1).unique()&#10;            np.random.seed(global_seed)&#10;            train_indices = np.array(train_indices)&#10;            np.random.shuffle(train_indices)&#10;            groups = np.array_split(train_indices, folds)&#10;            train_data['group'] = 0&#10;            for fold_idx, group in enumerate(groups):&#10;                mask = train_data.index.get_level_values(1).isin(group)&#10;                train_data.loc[mask, 'group'] = fold_idx&#10;    if isinstance(selected_hyper, dict):&#10;        hyperparameters = selected_hyper&#10;    elif selected_hyper:&#10;        hyperparameters = get_hyperparameters(global_seed)&#10;    else:&#10;        hyperparameters = 'zeroshot' if config_info['zeroshot'] else ALL_HYPERPARAMETERS&#10;&#10;    logging.getLogger().info('%s stocks in the training' % len(train_data.index.get_level_values(1).unique().tolist()))&#10;    logging.getLogger().info(f'start at {start_date}, end at {end_date}， last date is {last_day}')&#10;&#10;    logging.getLogger().info(f'regimeID in  training: {&quot;regime_id&quot; in stock_data.columns}')&#10;&#10;    pred = TabularPredictor(label='return', path=f'models/{save_path}', eval_metric=metric, sample_weight=weight,&#10;                            weight_evaluation=weight is not None, groups=groups_name).fit(&#10;        train_data,&#10;        tuned_data,&#10;        verbosity=config_info['ag_verbosity'],&#10;        time_limit=config_info['train_time_limit'],&#10;        presets=presets,&#10;        keep_only_best=False,&#10;        use_bag_holdout=use_bag_holdout,&#10;        hyperparameters=hyperparameters,&#10;        ds_args={'enable_ray_logging': True},&#10;        num_gpus=0 if config_info['cpu'] else 1,&#10;        holdout_frac=holdout_frac,&#10;        feature_prune_kwargs={} if config_info['auto_feature_select'] else None,&#10;        hyperparameter_tune_kwargs='auto' if config_info['hyperparameter_tune_kwargs'] else None,&#10;        **kwargs&#10;    )&#10;    if config_info['num_stack_levels'] == 0 and config_info['enable_second_ensemble']:&#10;        all_models = pred.model_names()&#10;        l1_models = [m for m in all_models if 'WeightedEnsemble' not in m]&#10;        pred.fit_weighted_ensemble(&#10;            base_models=list(l1_models))&#10;    fid_path = f'feature_importance.csv'&#10;    if tuned_data is not None and not os.path.exists(fid_path):&#10;        logging.getLogger().info(f'tuning data lens {tuned_data.shape[0]}')&#10;        fid = pred.feature_importance(tuned_data)&#10;        fid['imp_div_std'] = fid['importance'] / fid['stddev']&#10;        fid['neg_log_p'] = -np.log10(fid['p_value'])&#10;        fid['z_p99'] = zscore(fid['p99_high'])&#10;        fid['z_imp_div_std'] = zscore(fid['imp_div_std'])&#10;        fid['z_neg_log_p'] = zscore(fid['neg_log_p'])&#10;        fid['composite_score'] = fid['z_p99'] + fid['z_imp_div_std'] + fid['z_neg_log_p']&#10;        fid.to_csv(fid_path)&#10;    return pred&#10;&#10;&#10;def get_hyperparameters(global_seed):&#10;    hyperparameters = {}&#10;    if config_info['zeroshot']:&#10;        for model_name in config_info.get('models_to_use', ['GBM', 'CAT', 'FASTAI']):&#10;            if model_name in configs.hyperparameter_portfolio_small:&#10;                hyperparameters[model_name] = configs.hyperparameter_portfolio_small[model_name]&#10;    else:&#10;        for model_name in config_info.get('models_to_use', ['GBM', 'CAT', 'FASTAI']):&#10;            if model_name in ALL_HYPERPARAMETERS:&#10;                if model_name == 'GBM':&#10;                    if isinstance(ALL_HYPERPARAMETERS[model_name], list):&#10;                        if len(config_info['gbm_to_use']) &gt; 0:&#10;                            hyperparameters[model_name] = [ALL_HYPERPARAMETERS[model_name][int(i)] for i in&#10;                                                         config_info['gbm_to_use']]&#10;                        else:&#10;                            hyperparameters[model_name] = ALL_HYPERPARAMETERS[model_name]&#10;                    else:&#10;                        hyperparameters[model_name] = ALL_HYPERPARAMETERS[model_name]&#10;                else:&#10;                    hyperparameters[model_name] = ALL_HYPERPARAMETERS[model_name]&#10;&#10;    for model_name, model_params in hyperparameters.items():&#10;        if isinstance(model_params, list):&#10;            for param_set in model_params:&#10;                if 'ag_args_fit' in param_set:&#10;                    param_set['ag_args_fit']['max_time_limit'] = config_info['max_time_sub_model']&#10;                else:&#10;                    param_set['ag_args_fit'] = dict(max_time_limit=config_info['max_time_sub_model'])&#10;                for seed_key in param_set:&#10;                    if seed_key.endswith('seed'):&#10;                        param_set[seed_key] = global_seed&#10;        else:&#10;            if 'ag_args_fit' in model_params:&#10;                model_params['ag_args_fit']['max_time_limit'] = config_info['max_time_sub_model']&#10;            else :&#10;                model_params['ag_args_fit'] = dict(max_time_limit=config_info['max_time_sub_model'])&#10;            for seed_key in model_params.keys():&#10;                if seed_key.endswith('seed'):&#10;                    model_params[seed_key] = global_seed&#10;&#10;    if config_info['cpu']:&#10;        if 'GBM' in hyperparameters and isinstance(hyperparameters['GBM'], list):&#10;            for param_set in hyperparameters['GBM']:&#10;                if 'device_type' in param_set:&#10;                    param_set['device_type'] = 'cpu'&#10;&#10;        if 'XGB' in hyperparameters:&#10;            if 'tree_method' in hyperparameters['XGB']:&#10;                hyperparameters['XGB']['tree_method'] = 'hist'&#10;            if 'predictor' in hyperparameters['XGB']:&#10;                hyperparameters['XGB']['predictor'] = 'cpu_predictor'&#10;    else:&#10;        if 'GBM' in hyperparameters and isinstance(hyperparameters['GBM'], list):&#10;            for param_set in hyperparameters['GBM']:&#10;                if 'device_type' in param_set:&#10;                    param_set['device_type'] = 'gpu'&#10;    return hyperparameters&#10;&#10;&#10;def prepare_bt_data(stock_data, cerebro_bt, start_date, end_date):&#10;    def to_qlib_code(code):&#10;        if code.startswith('6'):&#10;            return f&quot;SH{code}&quot;&#10;        elif code.startswith(('0', '1', '2', '3')):&#10;            return f&quot;SZ{code}&quot;&#10;        elif code.startswith(('87', '83', '4')):&#10;            return f&quot;BJ{code}&quot;&#10;        else:&#10;            return code&#10;&#10;    index_data = pd.read_csv(f'{config_info[&quot;market&quot;]}.csv', index_col=0, parse_dates=True)&#10;    index_data = index_data.loc[(index_data.index &gt;= start_date) &amp; (index_data.index &lt;= end_date), :]&#10;    index_data.rename(columns={'close': 'Close', 'open': 'Open', 'high': 'High', 'low': 'Low', 'volume': 'Volume'},&#10;                      inplace=True)&#10;    data = bt.feeds.PandasData(dataname=index_data)&#10;    cerebro_bt.adddata(data, name=config_info[&quot;market&quot;])&#10;&#10;    fields = [&quot;$open&quot;, &quot;$high&quot;, &quot;$low&quot;, &quot;$close&quot;, &quot;$volume&quot;]&#10;&#10;    instruments = [to_qlib_code(cd) for cd in stock_data]&#10;&#10;    data_df = D.features(&#10;        instruments=instruments,&#10;        fields=fields,&#10;        start_time=start_date,&#10;        end_time=end_date&#10;    )&#10;&#10;    all_codes = data_df.index.get_level_values('instrument').unique()&#10;&#10;    for q_code in all_codes:&#10;        df_single = data_df.xs(q_code, level='instrument')&#10;        df_single = df_single.rename(columns={&#10;            '$open': 'Open',&#10;            '$high': 'High',&#10;            '$low': 'Low',&#10;            '$close': 'Close',&#10;            '$volume': 'Volume'&#10;        })&#10;&#10;        if not isinstance(df_single.index, pd.DatetimeIndex):&#10;            df_single.index = pd.to_datetime(df_single.index)&#10;&#10;        original_code = q_code.lstrip('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ')&#10;&#10;        trading_days = D.calendar(start_time=start_date, end_time=end_date)&#10;        df_single = df_single.reindex(trading_days, method='ffill')&#10;        df_single.fillna(method='ffill', inplace=True)&#10;        df_single.fillna(method='bfill', inplace=True)&#10;        data = bt.feeds.PandasData(dataname=df_single)&#10;        cerebro_bt.adddata(data, name=original_code)&#10;&#10;&#10;def prepare_bt_pred_data(pred_data, df, topn=10):&#10;    if config_info['train_type'] == 'cls':&#10;        pred_pos_data = pd.DataFrame(pred_data, index=pred_data.index, columns=[1])&#10;        pred_pos_data.rename(columns={1: 'return'}, inplace=True)&#10;        pred_neg_data = pd.DataFrame(pred_data, index=pred_data.index, columns=[0])&#10;        pred_neg_data.rename(columns={0: 'return'}, inplace=True)&#10;        pred_data = pred_pos_data - pred_neg_data&#10;    else:&#10;        pred_data = pd.DataFrame(pred_data, index=pred_data.index, columns=['return'])&#10;    pred_data['date'] = pred_data.index.get_level_values(0)&#10;    pred_data['sec_id'] = pred_data.index.get_level_values(1)&#10;    pred_data.reset_index(drop=True, inplace=True)&#10;    secid_filter = config_info['secid_filter']&#10;    if len(secid_filter) &gt; 0:&#10;        pred_data = pred_data.loc[pred_data['sec_id'].isin(secid_filter), :]&#10;    pred_data['rank'] = pred_data.groupby('date')['return'].rank(ascending=False, method='min')&#10;    if topn &gt; 0:&#10;        pred_data = pred_data.loc[pred_data['rank'] &lt;= topn].copy()&#10;    if df is not None:&#10;        pred_data = add_additional_features(pred_data, df)&#10;    pred_data.sort_values(['date', 'rank'], inplace=True)&#10;    return pred_data&#10;&#10;&#10;def add_additional_features(backtrader_pred, df):&#10;    ret5 = df['vwap'].groupby('sec_id').pct_change(5)&#10;    ret5 = pd.DataFrame(ret5)&#10;    ret5.rename(columns={'vwap': 'vwap5'}, inplace=True)&#10;    ret = pd.merge(backtrader_pred, ret5, left_on=['date', 'sec_id'], right_on=['date', 'sec_id'],&#10;                   how='left')&#10;    feature_names = config_info['added_alpha_for_stratergy']&#10;    if feature_names is not None:&#10;        feature_list = [feature.strip() for feature in feature_names.split(',')]&#10;        feature_list = [feature[1:] if feature.startswith('_') else feature for feature in feature_list]&#10;&#10;        if config_info['train_with_add_features']:&#10;            add_features = prepare_add_features(min(backtrader_pred['date']), max(backtrader_pred['date']), False)&#10;            existing_columns = [col for col in feature_list if col in add_features.columns]&#10;            if len(existing_columns) &gt; 0:&#10;                add_features = add_features.loc[:, existing_columns]&#10;            add_features.reset_index(inplace=True)&#10;            ret = pd.merge(ret, add_features, left_on=['date', 'sec_id'], right_on=['date', 'sec_id'], how='left')&#10;&#10;        existing_columns = [col for col in feature_list if col in df.columns]&#10;        ret = pd.merge(ret, df[existing_columns], left_on=['date', 'sec_id'], right_on=['date', 'sec_id'],&#10;                       how='left')&#10;&#10;    return ret&#10;&#10;&#10;def init_logger():&#10;    logger = logging.getLogger()&#10;    logger.setLevel(logging.INFO)&#10;&#10;    ch = logging.StreamHandler()&#10;    ch.setLevel(logging.INFO)&#10;    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')&#10;    ch.setFormatter(formatter)&#10;    logger.addHandler(ch)&#10;&#10;    if config_info['trade_log']:&#10;        fh = logging.FileHandler('logfile.log', mode='a')&#10;        fh.setLevel(logging.INFO)&#10;        fh.setFormatter(formatter)&#10;        logger.addHandler(fh)&#10;&#10;    pd.set_option('display.max_columns', None)&#10;    pd.set_option('display.max_rows', None)&#10;    pd.set_option('display.width', None)&#10;    pd.set_option('display.max_colwidth', None)&#10;&#10;    return logger&#10;&#10;&#10;def send_email(title, msg, addr):&#10;    try:&#10;        smtp = smtplib.SMTP()&#10;        smtp.connect('smtp.126.com')&#10;        from_adr = '<EMAIL>'&#10;        smtp.login(from_adr, 'QNspf6Q8HQKy5VBj')&#10;        mime_msg = MIMEText(msg, 'plain', 'utf-8')&#10;        mime_msg['Subject'] = Header(title, 'utf-8')&#10;        mime_msg['From'] = from_adr&#10;        mime_msg['To'] = addr&#10;        smtp.sendmail(from_adr, addr.split(','), mime_msg.as_string())&#10;        smtp.quit()&#10;    except:&#10;        pass&#10;&#10;&#10;def send_email_attachment(title, addr, msg, *files):&#10;    try:&#10;        smtp = smtplib.SMTP()&#10;        smtp.connect('smtp.126.com')&#10;        from_adr = '<EMAIL>'&#10;        smtp.login(from_adr, 'QNspf6Q8HQKy5VBj')&#10;        mime_msg = MIMEMultipart()&#10;        mime_msg['Subject'] = Header(title, 'utf-8')&#10;        mime_msg['From'] = from_adr&#10;        mime_msg['To'] = addr&#10;        mime_msg.attach(MIMEText(msg, 'plain', 'utf-8'))&#10;        for file_path in files:&#10;            with open(file_path, 'rb') as f:&#10;                attachment_content = MIMEApplication(f.read(), _subtype='txt')&#10;                attachment_content.add_header('Content-Disposition', 'attachment', filename=file_path.split(&quot;/&quot;)[-1])&#10;                mime_msg.attach(attachment_content)&#10;        smtp.sendmail(from_adr, addr.split(','), mime_msg.as_string())&#10;        smtp.quit()&#10;    except:&#10;        pass&#10;&#10;&#10;def notify_by_email(title, msg=None, also_to_jammie=False, force_send=False):&#10;    if not config_info['notify_by_mail'] and not force_send:&#10;        return&#10;    if msg is None:&#10;        msg = title&#10;    send_email(title, msg, config_info['mail'])&#10;    if also_to_jammie:&#10;        send_email(title, msg, '<EMAIL>')&#10;&#10;&#10;def notify_by_email_with_attachment(title, *file_path):&#10;    send_email_attachment(title, config_info['mail'], '', *file_path)&#10;&#10;&#10;def get_last_time():&#10;    market = pd.read_csv(f'{config_info[&quot;market&quot;]}.csv', index_col=0)&#10;    now = pd.to_datetime(market.index[-1])&#10;    return now&#10;&#10;&#10;def prepare_data_for_cerebro(df, end_date, test_start_date, tuning_sec_only=False):&#10;    cerebro = bt.Cerebro()&#10;    bt_data = df.loc[(df.index.get_level_values(0) &gt;= test_start_date) &amp; (&#10;            df.index.get_level_values(0) &lt;= end_date), :].index.get_level_values(1).unique().tolist()&#10;    if tuning_sec_only:&#10;        with open('tuning_sec_ids.txt', 'r') as f:&#10;            tuning_sec_ids = f.read().splitlines()&#10;            bt_data = list(set(bt_data) &amp; set(tuning_sec_ids))&#10;    prepare_bt_data(bt_data, cerebro, test_start_date, end_date)&#10;    return cerebro&#10;&#10;&#10;def prepare_cerebro(cerebro):&#10;    cerebro.broker.set_coo(True)&#10;    cerebro.broker.setcash(10000000.0)&#10;    cerebro.broker.setcommission(commission=0.001)&#10;    cerebro.addanalyzer(bt.analyzers.TimeReturn)&#10;    cerebro.addanalyzer(bt.analyzers.SharpeRatio, timeframe=bt.TimeFrame.Days, annualize=True, riskfreerate=0.0)&#10;    cerebro.addanalyzer(AnnualizedSortinoRatio)&#10;    cerebro.addanalyzer(bt.analyzers.DrawDown)&#10;    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer)&#10;    cerebro.addanalyzer(bt.analyzers.AnnualReturn)&#10;    cerebro.addobserver(bt.observers.Value)&#10;    cerebro.addobserver(bt.observers.DrawDown)&#10;&#10;&#10;def draw_values(stats, save_path):&#10;    values = stats[0].observers.value.lines.value.get(size=stats[0].data.datetime.buflen())&#10;    dates = [bt.num2date(x) for x in stats[0].data.datetime.array]&#10;    plt.figure(figsize=(10, 6))&#10;    plt.plot(dates, values)&#10;    plt.title('Value over Time')&#10;    plt.xlabel('Date')&#10;    plt.ylabel('Value')&#10;    plt.savefig(save_path)&#10;&#10;&#10;def dump_last_broker(cerebro, stat):&#10;    broker = cerebro.broker&#10;    positions = broker.positions&#10;    ret = '\n-------operations-------\n'&#10;    sells = []&#10;    buys = []&#10;    holdings = []&#10;    file_name = pd.to_datetime(stat.data.datetime.date(0)).strftime('%Y-%m-%d')&#10;    file_name = f'{file_name}_holding.txt'&#10;    for order in stat.last_order:&#10;        ret += f&quot;{order[0]} {order[1]}\n&quot;&#10;        if order[0] == 'sel':&#10;            sells.append(order[1])&#10;        else:&#10;            buys.append(order[1])&#10;    ret += '\n-------holding------\n'&#10;    for data, position in positions.items():&#10;        if not position.size &gt; 0:&#10;            continue&#10;        if data._name in sells:&#10;            continue&#10;&#10;        last_day_change = (data.close[0] - data.close[-1]) / data.close[-1]&#10;        holding_period_change = (data.close[0] - position.price) / position.price&#10;        holdings.append(data._name)&#10;        ret += f'{data._name}, 最后一日涨幅: {format(last_day_change * 100, &quot;.2f&quot;)}%, 持有收益: {format(holding_period_change * 100, &quot;.2f&quot;)}%\n'&#10;&#10;    if config_info['trade_log']:&#10;        with open(file_name, 'w') as f:&#10;            f.write('Buys:\n')&#10;            for buy in buys:&#10;                f.write(f'{buy}\n')&#10;            f.write('\nHoldings:\n')&#10;            for holding in holdings:&#10;                f.write(f'{holding}\n')&#10;    return ret&#10;&#10;&#10;def dump_stats_cerebro(cerebro, stats, mail_title=None):&#10;    values = stats[0].observers.value.lines.value.get(size=stats[0].data.datetime.buflen())&#10;    dates = [bt.num2date(x) for x in stats[0].data.datetime.array]&#10;    values = pd.Series(values, index=dates)&#10;    change = values.pct_change()[-1]&#10;    msg = mail_title + '\r\n\r\n'&#10;    msg += str(stats[0].analyzers.annualreturn.get_analysis()) + '\r\n'&#10;    msg += str(stats[0].analyzers.sharperatio.get_analysis()) + '\r\n'&#10;    msg += str(stats[0].analyzers.annualizedsortinoratio.get_analysis()) + '\r\n'&#10;    msg += str(stats[0].analyzers.drawdown.get_analysis()) + '\r\n'&#10;&#10;    # Get trade analysis&#10;    trade_analysis = stats[0].analyzers.tradeanalyzer.get_analysis()&#10;    if 'len' in trade_analysis:&#10;        avg_holding = str(trade_analysis['len']['average'])&#10;        msg += f'avg Holding: {avg_holding}\r\n'&#10;&#10;    # Calculate winning ratio&#10;    total_completed = trade_analysis.get('total', {}).get('closed', 0)&#10;    total_won = trade_analysis.get('won', {}).get('total', 0)&#10;    if total_completed &gt; 0:&#10;        win_ratio = (total_won / total_completed) * 100&#10;        msg += f'Win Ratio: {win_ratio:.2f}%\r\n'&#10;&#10;    msg += f'last day change: {change}'&#10;&#10;    if config_info['dump_change']:&#10;        msg += dump_last_broker(cerebro, stats[0])&#10;    if (mail_title is not None) and (config_info['notify_by_mail'] is True):&#10;        value_png_path = 'value.png'&#10;        draw_values(stats, value_png_path)&#10;        send_email_attachment(mail_title, config_info['mail'], msg, value_png_path)&#10;&#10;    logger = logging.getLogger()&#10;    logger.info(msg)&#10;&#10;&#10;class TopNStratergy(bt.Strategy):&#10;    params = (&#10;        ('topn', 10),&#10;        ('period', 10),&#10;        ('printlog', False),&#10;        ('pred_data', None),&#10;        ('max_down', 0.1),&#10;        ('min_hold', 1000),&#10;        ('min_return', -1),&#10;        ('trade_data', None),&#10;        ('min_mean_return', -1),&#10;        ('train_year', config_info['train_year']),&#10;        ('rank_model', None)&#10;    )&#10;&#10;    def log(self, txt, dt=None):&#10;        dt = dt or self.data.datetime.date(0)&#10;        if self.params.printlog:&#10;            logging.getLogger().info('%s, %s' % (dt.isoformat(), txt))&#10;&#10;    def notify_order(self, order):&#10;        if order.status in [order.Submitted, order.Accepted]:&#10;            return&#10;        sec_code = order.data._name&#10;        if order.status in [order.Completed]:&#10;            if order.isbuy():&#10;                exec_price = order.executed.price&#10;                size = order.executed.size&#10;                exec_date = self.data.datetime.date(0)  # 当前bar对应的日期&#10;                self.buy_pre[sec_code] = [exec_price, exec_date, size]&#10;                self.log('BUY EXECUTED %s, Price: %.4f, Cost: %.2f, Comm %.2f' %&#10;                         (sec_code,&#10;                          order.executed.price,&#10;                          order.executed.value,&#10;                          order.executed.comm))&#10;            elif order.issell():&#10;                if sec_code in self.buy_pre:&#10;                    del self.buy_pre[sec_code]&#10;                self.log('SELL EXECUTED %s, Price: %.4f, Cost: %.2f, Comm %.2f' %&#10;                         (sec_code,&#10;                          order.executed.price,&#10;                          order.executed.value,&#10;                          order.executed.comm))&#10;        elif order.status == order.Margin:&#10;            attempted_cost = order.created.price * order.created.size&#10;            current_cash = self.broker.get_cash()&#10;            shortfall = attempted_cost - current_cash&#10;&#10;            self.log(f&quot;Margin error for {sec_code}, short of {shortfall:.2f} cash&quot;)&#10;        elif order.status in [order.Canceled, order.Rejected]:&#10;            self.log('Order Canceled/Rejected %s:%s ' % (sec_code, order.isbuy()))&#10;&#10;    def notify_trade(self, trade):&#10;        if not trade.isclosed:&#10;            return&#10;        self.log('OPERATION PROFIT, GROSS %.2f, NET %.2f' % (trade.pnl, trade.pnlcomm))&#10;&#10;    def __init__(self):&#10;        self.skip_days = config_info['skip_days']&#10;        self.is_last_day = False&#10;        self.last_day = None&#10;        self.last_order = []&#10;        self.current_bar = 0&#10;        self.vix = False&#10;        self.topn = self.p.topn&#10;        self.period = self.p.period&#10;        self.pred_data = self.p.pred_data&#10;        self.buy_pre = {}&#10;        self.max_down = self.p.max_down&#10;        self.min_hold = self.p.min_hold&#10;        self.min_return = self.p.min_return&#10;        self.trade_data = self.p.trade_data&#10;        self.min_mean_return = self.p.min_mean_return&#10;        self.train_year = self.p.train_year&#10;        self.rank_model = self.p.rank_model&#10;        if self.pred_data is not None:&#10;            self.farrest_day = self.pred_data['date'].min()&#10;        self.cur_step_pred_data = None&#10;        if config_info['wencai_scores_path'] is not None:&#10;            self.wencai_data = pd.read_csv(config_info['wencai_scores_path'], index_col=[0, 1], parse_dates=True,&#10;                                           dtype={1: str})&#10;            if self.pred_data is not None:&#10;                wencai_columns = 'predict'&#10;                updated = pd.merge(self.pred_data, self.wencai_data[wencai_columns], left_on=['date', 'sec_id'],&#10;                                   right_index=True)&#10;                if not updated.empty:&#10;                    self.pred_data = updated&#10;                    temp = []&#10;                    select_range = config_info['select_range']&#10;                    for date, group in self.pred_data.groupby('date'):&#10;                        group = group.copy()&#10;                        top_rows = group[group['rank'] &lt;= select_range].copy()&#10;                        if not top_rows.empty:&#10;                            top_rows = top_rows.sort_values(by=wencai_columns, ascending=False)&#10;                            top_rows['rank'] = range(1, len(top_rows) + 1)&#10;                            group.update(top_rows)&#10;                        temp.append(group)&#10;                    self.pred_data = pd.concat(temp)&#10;        else:&#10;            self.wencai_data = None&#10;        self.index_data = pd.read_csv(f'{config_info[&quot;market&quot;]}.csv', index_col=0, parse_dates=True)&#10;        if os.path.exists('trade_date.txt') and config_info['consider_trade_date']:&#10;            with open('trade_date.txt', 'r') as f:&#10;                self.trade_date = f.readlines()&#10;                self.trade_date = [pd.to_datetime(date) for date in self.trade_date]&#10;        else:&#10;            self.trade_date = None&#10;&#10;    def get_predict_data(self, predict_date):&#10;        return self.pred_data.loc[(self.pred_data['date'] == predict_date), :]&#10;&#10;    def get_low_level(self, sec_code):&#10;        if self.cur_step_pred_data.empty:&#10;            return 0&#10;        returns = self.cur_step_pred_data.loc[self.cur_step_pred_data['sec_id'] == sec_code, 'rank_sel']&#10;        if returns.empty:&#10;            return 1&#10;        if returns.values[0] &lt;= self.topn * config_info['topn_dropout_k']:&#10;            return 0&#10;        return 1 if returns.values[0] &lt;= config_info['bottom_rank'] else 2&#10;&#10;    def check_sells(self, dt, predict_date):&#10;        buy_pre_items = self.buy_pre.items()&#10;        added_cash = 0&#10;&#10;        buy_pre_items = sorted(&#10;            buy_pre_items,&#10;            key=lambda x: (&#10;                self.cur_step_pred_data.loc[&#10;                    (self.cur_step_pred_data['sec_id'] == x[0]),&#10;                    'rank_sel'&#10;                ].values[0]&#10;                if not self.cur_step_pred_data.loc[&#10;                    (self.cur_step_pred_data['sec_id'] == x[0])&#10;                ].empty&#10;                else float('inf')&#10;            ),&#10;            reverse=True&#10;        )&#10;        sells = 0&#10;        for sec_code, info in buy_pre_items:&#10;            price = info[0]&#10;            buy_date = info[1]&#10;            size = info[2]&#10;&#10;            cur_close = self.getdatabyname(sec_code).close[0]&#10;            diff = (price - cur_close) / price&#10;            last_close = self.getdatabyname(sec_code).close[-1]&#10;            day_max_down = config_info['day_max_down']&#10;            low_level = self.get_low_level(sec_code)&#10;            if ((config_info['hold_till_end'] &gt; 0) and (&#10;                    predict_date - pd.Timestamp(buy_date)).days &gt;= config_info['hold_till_end'] * 7 / 5) or (&#10;                    (config_info['hold_till_end'] == 0) and (&#10;                    (diff &gt; self.max_down)&#10;                    or low_level &gt;= 1&#10;                    or ((last_close - cur_close) / last_close &gt; day_max_down)&#10;            )):&#10;                price_sell = self.getdatabyname(sec_code).close[0]&#10;                self.close(data=self.getdatabyname(sec_code))&#10;                added_cash += (size * price_sell)&#10;                if self.is_last_day:&#10;                    self.last_order.append(('sel', sec_code))&#10;                sells += 1&#10;                if sells &gt;= config_info['max_sell'] and low_level &lt; 2:&#10;                    break&#10;        return self.topn - (len(self.buy_pre) - sells), added_cash&#10;&#10;    def close_all(self):&#10;        for sec_code, info in self.buy_pre.items():&#10;            self.close(data=self.getdatabyname(sec_code))&#10;            if self.is_last_day:&#10;                self.last_order.append(('sell', sec_code))&#10;&#10;    def get_mean_returns(self, dt, predict_date):&#10;        mean_returns = self.pred_data.loc[pd.to_datetime(self.pred_data['date']) == predict_date, 'return'].mean()&#10;        return mean_returns&#10;&#10;    def get_selected_sec(self, dt, i, predict_date):&#10;        if self.cur_step_pred_data.empty or len(self.cur_step_pred_data) &lt;= i:&#10;            return None, None, True&#10;        selected = self.cur_step_pred_data.iloc[i]&#10;        sec_code = selected['sec_id']&#10;        returns = selected['return']&#10;        if not config_info['buy_high_price']:&#10;            if sec_code in self.getdatanames():&#10;                if self.getdatabyname(sec_code).close[-1] / self.getdatabyname(sec_code).close[-2] &gt;= 1.095:&#10;                    if pd.to_datetime(self.data.datetime.date(-1)) &lt; predict_date and \&#10;                            pd.to_datetime(self.data.datetime.date(-2)) &lt; predict_date:&#10;                        self.log(f'{sec_code} is too high')&#10;                        return sec_code, returns, True&#10;        return sec_code, returns, False&#10;&#10;    def next(self):&#10;        if self.skip_days &gt; 0:&#10;            self.skip_days -= 1&#10;            return&#10;        if self.last_day is None:&#10;            self.last_day = pd.to_datetime(self.data.datetime.date(-1))&#10;            first_run = True&#10;        else:&#10;            first_run = False&#10;        predict_date = dt = pd.to_datetime(self.data.datetime.date(0))&#10;        if predict_date == self.last_day:&#10;            self.is_last_day = True&#10;        self.current_bar += 1&#10;        self.prepare_cur_step_predicted_data(predict_date)&#10;        if self.trade_date is not None:&#10;            if predict_date not in self.trade_date:&#10;                self.close_all()&#10;                self.vix = False&#10;                return&#10;        if config_info['consider_vix']:&#10;            if self.vix:&#10;                if self.can_trade():&#10;                    self.vix = False&#10;                else:&#10;                    return&#10;            if not first_run:&#10;                self.vix = self.evaluate_and_clear_positions(predict_date)&#10;                if self.vix:&#10;                    return&#10;&#10;        for i in range(0, min(config_info['select_range'], len(self.cur_step_pred_data))):&#10;            newline = pd.DataFrame([self.cur_step_pred_data.iloc[i]['sec_id'], dt]).T&#10;            newline.columns = self.trade_data[0].columns&#10;            self.trade_data[0] = pd.concat([self.trade_data[0], newline])&#10;&#10;        empty_count, added_cash = self.check_sells(dt, predict_date)&#10;        if empty_count &lt;= 0:&#10;            return&#10;        self.update_cur_step_data_for_empty_slots(max(config_info['select_range'], empty_count * 2))&#10;        mean_returns = self.get_mean_returns(dt, predict_date)&#10;        if mean_returns &lt; self.min_mean_return:&#10;            return&#10;&#10;        avail_cash = self.broker.get_cash() + added_cash&#10;&#10;        each_cash = self.broker.get_value() / self.topn&#10;        for i in range(0, len(self.cur_step_pred_data)):&#10;            if avail_cash &lt; each_cash * 0.8:&#10;                break&#10;            sec_code, returns, should_skip = self.get_selected_sec(dt, i, predict_date)&#10;            if sec_code is None:&#10;                break&#10;            if returns &lt; self.min_return:&#10;                break&#10;            if should_skip:&#10;                continue&#10;            if self.buy_pre.get(sec_code) is not None:&#10;                continue&#10;            if sec_code not in self.getdatanames():&#10;                continue&#10;            price = self.getdatabyname(sec_code).close[0]&#10;            buy_cash = min(avail_cash, each_cash) * 0.98&#10;            avail_cash -= buy_cash&#10;            size = int(buy_cash / price)&#10;            self.buy(data=self.getdatabyname(sec_code), size=size)&#10;            if self.is_last_day:&#10;                self.last_order.append(('buy', sec_code))&#10;            empty_count -= 1&#10;            if empty_count &lt;= 0:&#10;                break&#10;&#10;    def prepare_cur_step_predicted_data(self, predict_date):&#10;        now_time = pd.to_datetime(predict_date)&#10;        start_time = now_time.replace(day=1)&#10;        instruments = D.list_instruments(D.instruments(market=convert_to_csi_code(config_info['market'])),&#10;                                         start_time=start_time, end_time=now_time)&#10;        instruments = [itr.lstrip('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ') for itr in instruments]&#10;        cur_step_pred_data = self.pred_data.loc[(self.pred_data['date'] == predict_date)]&#10;        cur_step_pred_data = cur_step_pred_data.loc[cur_step_pred_data['sec_id'].isin(instruments) |&#10;                                                    cur_step_pred_data['sec_id'].isin(self.buy_pre.keys())]&#10;        cur_step_pred_data['rank_sel'] = cur_step_pred_data['rank'].rank(ascending=True)&#10;        self.cur_step_pred_data = cur_step_pred_data.loc[&#10;            (cur_step_pred_data['rank_sel'] &lt;= self.topn * config_info['topn_dropout_k']) |&#10;            (cur_step_pred_data['sec_id'].isin(self.buy_pre.keys()))]&#10;        self.cur_step_pred_data = self.cur_step_pred_data.sort_values(by='rank_sel')&#10;        if config_info['random_backtest']:&#10;            n = config_info['select_range']&#10;            if n &lt; len(self.cur_step_pred_data):&#10;                subset = self.cur_step_pred_data.head(n).sample(frac=1, random_state=random.randint(0, 100000))&#10;                remaining = self.cur_step_pred_data.iloc[n:]&#10;                self.cur_step_pred_data = pd.concat([subset, remaining])&#10;            else:&#10;                self.cur_step_pred_data = self.cur_step_pred_data.sample(frac=1, random_state=random.randint(0, 100000))&#10;&#10;    def get_data_value(self, sec_id, attr, idx):&#10;        if sec_id in self.getdatanames():&#10;            return getattr(self.getdatabyname(sec_id), attr)[idx]&#10;        else:&#10;            return np.nan&#10;&#10;    def can_trade(self):&#10;        # 计算当前日期的价格变化，并仅在满足条件时允许交易&#10;        open_prices = self.cur_step_pred_data.apply(&#10;            lambda row: self.get_data_value(row['sec_id'], 'open', 0), axis=1)&#10;        close_prices = self.cur_step_pred_data.apply(&#10;            lambda row: self.get_data_value(row['sec_id'], 'close', 0), axis=1)&#10;        current_day_change = ((close_prices - open_prices) / open_prices).mean()&#10;&#10;        if np.isnan(current_day_change):&#10;            return False&#10;&#10;        if current_day_change &gt; config_info['vix_up_level']:&#10;            close_neg1 = self.cur_step_pred_data.apply(&#10;                lambda row: self.get_data_value(row['sec_id'], 'close', -1), axis=1)&#10;            current_day_change = ((close_prices - close_neg1) / close_neg1).mean()&#10;            if current_day_change &gt; config_info['vix_up_level']:&#10;                self.log(&quot;当日涨幅为%.4f，解除恐慌状态&quot; % current_day_change)&#10;                return True&#10;        elif len(self.cur_step_pred_data) &gt;= 2:&#10;            close_0 = self.cur_step_pred_data.apply(&#10;                lambda row: self.get_data_value(row['sec_id'], 'close', 0), axis=1)&#10;            close_neg1 = self.cur_step_pred_data.apply(&#10;                lambda row: self.get_data_value(row['sec_id'], 'close', -1), axis=1)&#10;            close_neg2 = self.cur_step_pred_data.apply(&#10;                lambda row: self.get_data_value(row['sec_id'], 'close', -2), axis=1)&#10;&#10;            first_day_return = ((close_0 - close_neg1) / close_neg1).mean()&#10;            second_day_return = ((close_neg1 - close_neg2) / close_neg2).mean()&#10;&#10;            if np.isnan(first_day_return) or np.isnan(second_day_return):&#10;                return False&#10;&#10;            if first_day_return &gt; 0 and second_day_return &gt; 0:&#10;                self.log(&quot;两日上涨，解除恐慌状态&quot;)&#10;                return True&#10;        return False&#10;&#10;    def evaluate_and_clear_positions(self, cur_date):&#10;        # 计算所有当前步骤预测数据的平均开盘-收盘比率&#10;        open_price = self.index_data.loc[cur_date, 'open']&#10;        close_price = self.index_data.loc[cur_date, 'close']&#10;        avg_open_close_ratio = ((open_price - close_price) / open_price).mean()&#10;&#10;        if np.isnan(avg_open_close_ratio):&#10;            return False&#10;&#10;        # 如果比率超过阈值，则清仓&#10;        if avg_open_close_ratio &gt; config_info['vix_down_level']:&#10;            self.close_all()&#10;            self.log(&quot;当日跌幅超过%.4f，进入恐慌状态&quot; % avg_open_close_ratio)&#10;            return True&#10;&#10;        prev_day = pd.to_datetime(self.data.datetime.date(-1))&#10;        prev_close = self.index_data.loc[prev_day, 'close']&#10;        avg_close_change_ratio = ((prev_close - close_price) / prev_close).mean()&#10;&#10;        if np.isnan(avg_close_change_ratio):&#10;            return False&#10;&#10;        # 如果变化比率超过阈值，则清仓&#10;        if avg_close_change_ratio &gt; config_info['vix_down_level']:&#10;            self.close_all()&#10;            self.log(&quot;当日相对上日跌幅超过%.4f，进入恐慌状态&quot; % avg_close_change_ratio)&#10;            return True&#10;        return False&#10;&#10;    def update_cur_step_data_for_empty_slots(self, empty_count):&#10;        added_feature = config_info.get('added_alpha_for_stratergy')&#10;        if added_feature:&#10;            feature_list = [feature.strip() for feature in added_feature.split(',')]&#10;            if len(feature_list) == 1:&#10;                ascending = added_feature.startswith('_')&#10;                if ascending:&#10;                    added_feature = added_feature[1:]&#10;                rerange = min(config_info['topn'], empty_count)&#10;                top2n = self.cur_step_pred_data.head(rerange)&#10;                top2n = top2n.sort_values(by=added_feature, ascending=ascending)&#10;                self.cur_step_pred_data.iloc[:rerange] = top2n&#10;            else:&#10;                coefficients = []&#10;                actual_features = []&#10;&#10;                for feature in feature_list:&#10;                    if feature.startswith('_'):&#10;                        coefficients.append(-1)&#10;                        actual_features.append(feature[1:])&#10;                    else:&#10;                        coefficients.append(1)&#10;                        actual_features.append(feature)&#10;&#10;                top2n = self.cur_step_pred_data.head(empty_count).copy()&#10;                rank_df = top2n[actual_features].rank(method='first', ascending=False)&#10;                sort_sum = rank_df.multiply(coefficients).sum(axis=1)&#10;                top2n['sort_sum'] = sort_sum&#10;                top2n_sorted = top2n.sort_values(by='sort_sum', ascending=True)&#10;                self.cur_step_pred_data.iloc[:empty_count] = top2n_sorted.drop(columns='sort_sum')&#10;&#10;        if self.rank_model is not None:&#10;            current_date = pd.to_datetime(self.datas[0].datetime.date(0))&#10;            rerange = min(config_info['topn'], empty_count)&#10;            top2n_stocks = self.cur_step_pred_data.head(rerange)['sec_id'].to_list()&#10;            rank_predictions = predict_ranks(self.rank_model, top2n_stocks, current_date)&#10;            rank_pred_data = prepare_bt_pred_data(rank_predictions, None, 0)&#10;            rank_pred_data['trade_rank'] = rank_pred_data['rank']&#10;            if not rank_predictions.empty:&#10;                self.cur_step_pred_data = pd.merge(&#10;                    self.cur_step_pred_data,&#10;                    rank_pred_data[['sec_id', 'trade_rank']],&#10;                    on='sec_id',&#10;                    how='left'&#10;                )&#10;                &#10;                topn_part = self.cur_step_pred_data.head(rerange).copy()&#10;                if not topn_part.empty:&#10;                    topn_part = topn_part.sort_values('trade_rank', ascending=True)&#10;                    self.cur_step_pred_data.iloc[:len(topn_part)] = topn_part&#10;&#10;&#10;def plot_stock_charts(stock_codes, output_dir, days=90):&#10;    os.makedirs(output_dir, exist_ok=True)&#10;    code_names = pd.read_csv(os.path.expanduser('~/stock_comments.csv'), index_col=0, dtype={'sec_id': str})&#10;    for code in stock_codes:&#10;        today = datetime.date.today()&#10;        df = D.features([f&quot;SH{code}&quot; if code.startswith('6') else f&quot;SZ{code}&quot;],&#10;                        fields=[&quot;$close&quot;, &quot;$open&quot;, &quot;$high&quot;, &quot;$low&quot;, &quot;$volume&quot;],&#10;                        start_time=today - datetime.timedelta(days=days), end_time=today)&#10;        code_name = code_names.loc[code, '名称']&#10;        if df.empty:&#10;            print(f&quot;No data available for stock {code}&quot;)&#10;            continue&#10;&#10;        df.reset_index(level=0, inplace=True)&#10;        df = df[df.index &gt;= df.index[-1] - pd.DateOffset(days=days)]&#10;&#10;        if df.empty:&#10;            print(f&quot;No data available for the last month for stock {code}-{code_name}&quot;)&#10;            continue&#10;&#10;        df.columns = ['symbol', 'close', 'open', 'high', 'low', 'volume']&#10;        plt.rcParams['font.sans-serif'] = [&#10;            'Noto Sans CJK JP']&#10;        plt.rcParams['axes.unicode_minus'] = False  # Ensure minus signs are displayed correctly&#10;&#10;        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 6), gridspec_kw={'height_ratios': [3, 1]})&#10;        mpf.plot(df, type='candle', mav=(5, 10, 20), volume=ax2, ax=ax1, show_nontrading=True)&#10;&#10;        ax1.set_title(f&quot;{code}-{code_name}&quot;)&#10;&#10;        plt.savefig(os.path.join(output_dir, f&quot;{code}.png&quot;))&#10;        plt.close()&#10;&#10;&#10;def get_intervals_from_config():&#10;    csi_code = configs.convert_to_csi_code(config_info['market'])&#10;    index_file = f'{config_info[&quot;qlib_url&quot;]}/instruments/{csi_code}.txt'&#10;    intervals = []&#10;    with open(index_file, 'r') as f:&#10;        for line in f:&#10;            line = line.strip()&#10;            if not line:&#10;                continue&#10;            parts = line.split()&#10;            if len(parts) &lt; 3:&#10;                continue&#10;            start_str, end_str = parts[1], parts[2]&#10;            start_date = datetime.datetime.strptime(start_str, '%Y-%m-%d').date()&#10;            end_date = datetime.datetime.strptime(end_str, '%Y-%m-%d').date()&#10;            intervals.append((start_date, end_date))&#10;    intervals = list(set(intervals))&#10;    intervals.sort(key=lambda x: x[0])&#10;    return intervals&#10;&#10;def prepare_rank_training_data(trade_data_path, stock_data, start_date, end_date):&#10;    if not os.path.exists(trade_data_path):&#10;        return None&#10;    trade_data = pd.read_csv(trade_data_path, parse_dates=['date'], index_col=0)&#10;    trade_data['sec_id'] = trade_data['sec_id'].apply(lambda x: str(x).zfill(6))&#10;    trade_data.reset_index(drop=True, inplace=True)&#10;    trade_data.set_index(['date', 'sec_id'], inplace=True)&#10;    trade_data = trade_data[(trade_data.index.get_level_values(0) &gt;= start_date) &amp; (trade_data.index.get_level_values(0) &lt;= end_date)]&#10;    ret = pd.merge(stock_data, trade_data, left_index=True, right_index=True, how='inner')&#10;    # ret['return_10D'] = ret.groupby(level=0)['return'].rank(ascending=True, pct=True)&#10;    return ret&#10;&#10;def _get_ranked_features_from_ic_files():&#10;    ic_table = pd.read_csv(f'a458_{config_info[&quot;market&quot;]}_ic.csv', index_col=0)&#10;    if os.path.exists(f'basic_{config_info[&quot;market&quot;]}_ic.csv'):&#10;        ic_table = pd.concat([ic_table, pd.read_csv(f'basic_{config_info[&quot;market&quot;]}_ic.csv', index_col=0)])&#10;    rank_1d = abs(ic_table.loc[:, f'{config_info[&quot;period_n&quot;]}D']).rank(ascending=False)&#10;    return rank_1d.index.to_list()&#10;&#10;&#10;def get_decorrelated_top_features(train_data: pd.DataFrame, feature_names: list, target_col: str = 'return_10D', top_k: int = 20, prefix=''):&#10;    decorrelated_features_file = f'{prefix}decorrelated_features.txt'&#10;    if os.path.exists(decorrelated_features_file):&#10;        with open(decorrelated_features_file, 'r') as f:&#10;            features = [line.strip() for line in f if line.strip()]&#10;        return features[:top_k]&#10;&#10;    valid_feature_names = [f for f in feature_names if f in train_data.columns]&#10;    X = train_data[valid_feature_names].copy()&#10;    y = train_data[target_col]&#10;&#10;    selected_features = []&#10;&#10;    tqdm_bar = tqdm.tqdm(range(min(top_k, len(valid_feature_names))), desc=&quot;Selecting Features&quot;)&#10;&#10;    for _ in tqdm_bar:&#10;        corrs = X.corrwith(y).abs()&#10;        if corrs.dropna().empty:&#10;            break&#10;&#10;        best_feature = corrs.idxmax()&#10;        selected_features.append(best_feature)&#10;        tqdm_bar.set_description(f&quot;Selected: {best_feature}&quot;)&#10;        f = X[best_feature]&#10;&#10;        for col in X.columns:&#10;            if col == best_feature:&#10;                continue&#10;&#10;            tqdm_bar.set_description(f&quot;Selected: {best_feature}， processing: {col}&quot;)&#10;&#10;            f_col = f&#10;            t_col = X[col]&#10;            valid_idx = f_col.notna() &amp; t_col.notna()&#10;            valid_idx &amp;= (~f_col.isin([np.inf, -np.inf])) &amp; (~t_col.isin([np.inf, -np.inf]))&#10;            valid_idx &amp;= f_col.abs() &lt; 1e10&#10;            valid_idx &amp;= t_col.abs() &lt; 1e10&#10;&#10;            if valid_idx.sum() &lt; 2:&#10;                continue&#10;&#10;            reg = LinearRegression()&#10;            f_valid = f_col[valid_idx].values.reshape(-1, 1)&#10;            col_valid = t_col[valid_idx].values&#10;            reg.fit(f_valid, col_valid)&#10;&#10;            pred = np.full_like(f_col, fill_value=np.nan, dtype=np.float64)&#10;            pred[valid_idx] = reg.predict(f_valid)&#10;&#10;            X[col] = X[col] - pred&#10;&#10;        X.drop(columns=[best_feature], inplace=True)&#10;&#10;        if X.empty:&#10;            break&#10;&#10;    with open(decorrelated_features_file, 'w') as f:&#10;        for feature in selected_features:&#10;            f.write(f&quot;{feature}\n&quot;)&#10;    return selected_features&#10;&#10;&#10;def train_rank_model(trade_data_path, stock_data, start_date, end_date, save_path):&#10;    train_data = prepare_rank_training_data(trade_data_path, stock_data, start_date, end_date)&#10;    if train_data is None or len(train_data) &lt; 1000:&#10;        return None&#10;    rfid = config_info['remove_from_fid']&#10;    config_info['remove_from_fid'] = 300&#10;    td = config_info['tuning_days']&#10;    config_info['tuning_days'] = 0&#10;    presets = config_info['presets']&#10;    config_info['presets'] = 'medium_quality'&#10;    tt = config_info['train_type']&#10;    config_info['train_type'] = 'rank'&#10;    dtf = config_info['decorrelated_top_features']&#10;    config_info['decorrelated_top_features'] = False&#10;    zh = config_info['zeroshot']&#10;    config_info['zeroshot'] = False&#10;&#10;    feature_names = _get_ranked_features_from_ic_files()&#10;    feature_names = get_decorrelated_top_features(train_data, feature_names, top_k=20, prefix='rank_')&#10;&#10;    ret = train_model(train_data, feature_names, config_info['period_n'], start_date, end_date, save_path,&#10;                      selected_hyper={'LR':{}})&#10;&#10;    config_info['remove_from_fid'] = rfid&#10;    config_info['tuning_days'] = td&#10;    config_info['presets'] = presets&#10;    config_info['train_type'] = tt&#10;    config_info['decorrelated_top_features'] = dtf&#10;    config_info['zeroshot'] = zh&#10;    return ret&#10;&#10;&#10;def predict_ranks(model, stocks, date):&#10;    df = load_stock_data(config_info['alpha_type'], config_info['market'])&#10;    feature_names = _get_ranked_features_from_ic_files()&#10;    pred_data = prepare_data_for_model(df, feature_names, config_info['period_n'], date, date)&#10;    pred_data = pred_data.loc[pred_data.index.get_level_values(1).isin(stocks)]&#10;    return predict_proba(model, pred_data)&#10;" />
            </PendingDiffInfo>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>