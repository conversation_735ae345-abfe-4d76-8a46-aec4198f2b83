import logging
from autogluon.core.models import AbstractModel


class LGBMRankerModel(AbstractModel):
    """
    LightGBM Ranker wrapper for AutoGluon.
    Always groups by the 'gdate' column, supports bagging folds correctly,
    and preserves self.model across save/load by using the default AbstractModel behavior.
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.model = None
        self._group_column = 'gdate'

    @classmethod
    def _get_default_ag_args(cls):
        # Disable stacking, treat as regression problem so AutoGluon picks it up
        return {'valid_stacker': False, 'problem_types': ['regression']}

    def _set_default_params(self):
        # Inject default LightGBM parameters
        default_params = {
            'objective': 'rank_xendcg',
            'ndcg_eval_at':[10],
            'metric': 'ndcg',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'random_state': 42,
            'n_estimators': 100
        }
        for name, value in default_params.items():
            self._set_default_param_value(name, value)

    def _fit(self, X, y, X_val=None, y_val=None, **kwargs):
        # Import inside to avoid requiring heavy dependencies at import time
        from lightgbm import LGBMRanker, early_stopping, log_evaluation

        # Run AutoGluon's preprocessing (handles features/metadata)
        X = self.preprocess(X)
        if self._group_column not in X.columns:
            raise ValueError(f"Training data is missing group column '{self._group_column}'")

        # Build group sizes for each day
        group_train = [len(g) for _, g in X.groupby(self._group_column, sort=False)]
        X_train = X.drop(columns=[self._group_column])

        # Initialize and fit the LightGBM ranker
        params = self.params.copy()
        self.model = LGBMRanker(**params)

        fit_kwargs = {'group': group_train}
        if X_val is not None and y_val is not None:
            X_val = self.preprocess(X_val)
            if self._group_column not in X_val.columns:
                raise ValueError(f"Validation data is missing group column '{self._group_column}'")
            group_val = [len(g) for _, g in X_val.groupby(self._group_column, sort=False)]
            X_val_feat = X_val.drop(columns=[self._group_column])
            fit_kwargs.update({
                'eval_set': [(X_val_feat, y_val)],
                'eval_group': [group_val],
                'callbacks': [
                    early_stopping(stopping_rounds=100),
                    log_evaluation(period=0)
                ]
            })

        self.model.fit(X_train, y, **fit_kwargs)

    def _predict(self, X, **kwargs):
        X = self.preprocess(X)
        X_pred = X.drop(columns=[self._group_column], errors='ignore')
        if self.model is None:
            raise ValueError("Model has not been fitted yet")
        return self.model.predict(X_pred)

    def _predict_proba(self, X, **kwargs):
        # Treat ranking scores as pseudo-probabilities for AutoGluon's evaluation
        return self._predict(X, **kwargs)

    def can_predict_proba(self):
        # Inform AutoGluon that we support predict_proba
        return True


class CatBoostRankerModel(AbstractModel):
    """
    CatBoost Ranker wrapper for AutoGluon.
    Always groups by the 'gdate' column, uses default AbstractModel save/load.
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.model = None
        self._group_column = 'gdate'

    @classmethod
    def _get_default_ag_args(cls):
        return {'valid_stacker': False, 'problem_types': ['regression']}

    def _set_default_params(self):
        default_params = {
            'loss_function': 'QuerySoftMax',
            'eval_metric': 'NDCG:top=10',
            'iterations': 1500,
            'learning_rate': 0.02,
            'depth': 6,
            'l2_leaf_reg': 3,
            'random_seed': 42,
            'verbose': False,
            'allow_writing_files': False
        }
        for name, value in default_params.items():
            self._set_default_param_value(name, value)

    def _fit(self, X, y, X_val=None, y_val=None, **kwargs):
        from catboost import CatBoostRanker, Pool

        X = self.preprocess(X)
        if self._group_column not in X.columns:
            raise ValueError(f"Training data is missing group column '{self._group_column}'")

        train_pool = Pool(
            data=X.drop(columns=[self._group_column]),
            label=y,
            group_id=X[self._group_column]
        )

        fit_kwargs = {}
        if X_val is not None and y_val is not None:
            X_val = self.preprocess(X_val)
            if self._group_column not in X_val.columns:
                raise ValueError(f"Validation data is missing group column '{self._group_column}'")
            val_pool = Pool(
                data=X_val.drop(columns=[self._group_column]),
                label=y_val,
                group_id=X_val[self._group_column]
            )
            fit_kwargs.update({
                'eval_set': val_pool,
                'early_stopping_rounds': 100,
                'verbose': False
            })

        params = self.params.copy()
        self.model = CatBoostRanker(**params)
        self.model.fit(train_pool, **fit_kwargs)

    def _predict(self, X, **kwargs):
        X = self.preprocess(X)
        X_pred = X.drop(columns=[self._group_column], errors='ignore')
        if self.model is None:
            raise ValueError("Model has not been fitted yet")
        return self.model.predict(X_pred)

    def _predict_proba(self, X, **kwargs):
        return self._predict(X, **kwargs)

    def can_predict_proba(self):
        return True
