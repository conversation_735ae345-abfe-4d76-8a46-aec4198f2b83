import datetime
import logging
import os
import random
import smtplib
import warnings
from email.header import Header
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

import alphalens
import backtrader as bt
import matplotlib.pyplot as plt
import mplfinance as mpf
import numpy as np
import openai
import pandas as pd
import qlib
from alphalens import performance as perf
from alphalens.utils import MaxLossExceededError
from autogluon.core.metrics import make_scorer
from autogluon.tabular import TabularPredictor
from langchain.chat_models import AzureChatOpenAI
from langchain.embeddings import OpenAIEmbeddings
from numba import njit, prange
from qlib.data import D
from scipy.stats import zscore, pearsonr
from sklearn.linear_model import LinearRegression
from sklearn.metrics import confusion_matrix
from sklearn.metrics import mean_squared_error
from sklearn.metrics import r2_score
import tqdm

import configs
from configs import config_info, convert_to_csi_code
from custom_ranking_models import LGBMRankerModel, CatBoostRankerModel

warnings.filterwarnings('ignore')

global cache
cache = {}
qlib_inited = False
MAX_FEATURE_COUNT = 1000

MAX_TIME_SUB_MODEL = 2000

ALL_HYPERPARAMETERS = {
    'GBM': [
        {
            'seed': 0,
            'bagging_seed': 0,
            'feature_fraction_seed': 0,
            'data_random_seed': 0,
            'feature_fraction': 0.9,
            "learning_rate": 0.03,
            "num_leaves": 128,
            "min_data_in_leaf": 5,
            'ag_args': dict(model_type="GBM", name_suffix="Large", priority=0),
        },
        {
            "extra_trees": True,
            'seed': 0,
            'bagging_seed': 0,
            'feature_fraction_seed': 0,
            'drop_seed': 0,
            'extra_seed': 0,
            'objective_seed': 0,
            'data_random_seed': 0,
            'bagging_fraction': 1.0,
            'feature_fraction': 1.0,
            'bagging_freq': 0,
            "ag_args": {"name_suffix": "XT", "priority": 0},
            'early_stopping_round': 100,
            'force_col_wise': True,
        },
        {
            'seed': 0,
            'bagging_seed': 0,
            'feature_fraction_seed': 0,
            'data_random_seed': 0,
        },
    ],
    'XGB': {
        'seed': 0,
    },
    "CAT": {
        'random_seed': 0,
    },
    "FASTAI": {
        'seed': 0,
    },
    "NN_TORCH": {
    },
    "RF": {
        "criterion": "squared_error",
        "random_state": 0,
        "ag_args": {"name_suffix": "MSE", "problem_types": ["regression", "quantile"]}
    },
    "XT": {
        "criterion": "squared_error",
        "random_state": 0,
        "ag_args": {"name_suffix": "MSE", "problem_types": ["regression", "quantile"]},
    },
    "KNN": [{"weights": "uniform", "ag_args": {"name_suffix": "Unif"}}],
}


class AnnualizedSortinoRatio(bt.Analyzer):
    params = (
        ('riskfreerate', 0.0),  # 年化无风险收益率，例如 0.03 表示 3%
        ('trading_days', 252),  # 一年中的交易日数量
    )

    def start(self):
        self.returns = []  # 用于存储每日收益率
        # 记录回测开始时的账户总值
        self.prev_value = self.strategy.broker.getvalue()

    def next(self):
        current_value = self.strategy.broker.getvalue()
        # 计算当期（每日）的收益率
        # daily_ret 为当天收益率 = 当天账户价值 / 前一天账户价值 - 1
        if self.prev_value != 0:
            daily_ret = (current_value / self.prev_value) - 1.0
            self.returns.append(daily_ret)
        self.prev_value = current_value  # 更新前一日账户价值

    def stop(self):
        returns = np.array(self.returns)
        # 将年化的无风险收益率转换为每日无风险收益率（简单除以交易日数量）
        daily_rf = self.p.riskfreerate / self.p.trading_days
        # 每日超额收益 = 每日实际收益 - 每日无风险收益率
        excess_returns = returns - daily_rf

        # 年化平均超额收益率 = 日均超额收益 * 交易日数
        annualized_excess_return = np.mean(excess_returns) * self.p.trading_days

        # 只选取亏损的那部分收益（超额收益为负的部分）
        downside_excess = excess_returns[excess_returns < 0]
        if downside_excess.size == 0:
            annualized_downside_std = 0.0
        else:
            # 年化下行波动率 = 日下行波动率 * sqrt(交易日数)
            annualized_downside_std = np.std(downside_excess) * np.sqrt(self.p.trading_days)

        if annualized_downside_std == 0:
            sortino = float('inf')
        else:
            sortino = annualized_excess_return / annualized_downside_std

        # 将计算结果存入 self.rets
        self.rets = {'sortino': sortino}

    def get_analysis(self):
        return self.rets


def sign_step(x):
    return x if x > 0 else 0


def init_qlib():
    global qlib_inited
    if not qlib_inited:
        configs.init_config()
        qlib.init(provider_uri=config_info['qlib_url'])
        qlib_inited = True

    if os.path.exists("interest.txt"):
        with open("interest.txt", "r") as f:
            interest_str = f.read()
        interest_list = interest_str.split("\n")
        interest_list = [x for x in interest_list if x]
        config_info['interest'] = interest_list


def create_llm(model='deepseek-chat'):
    # llm = ChatOpenAI(openai_api_base='https://api.deepseek.com/v1',
    #                  openai_api_key="sk-0c9e855553a04ef2afcb675bea3d5dc9",
    #                  model=model)
    llm = AzureChatOpenAI(deployment_name="gpt-4o", openai_api_version="2024-05-01-preview",
                          model_name='gpt-4o')
    return llm


def create_embedding():
    embeddings = OpenAIEmbeddings(
        deployment="text-embedding-ada-002",
        model="text-embedding-ada-002",
        chunk_size=1,
    )
    return embeddings


def init_gpt():
    openai.api_type = os.getenv('OPENAI_API_TYPE')
    openai.api_base = os.getenv('OPENAI_API_BASE')
    openai.api_key = os.getenv('OPENAI_API_KEY')
    openai.api_version = os.getenv('OPENAI_API_VERSION')

    if openai.api_key:
        os.environ["OPENAI_API_KEY"] = openai.api_key
    if openai.api_type:
        os.environ['OPENAI_API_TYPE'] = openai.api_type
    if openai.api_base:
        os.environ['OPENAI_API_BASE'] = openai.api_base
    if openai.api_version:
        os.environ['OPENAI_API_VERSION'] = openai.api_version

    serpapi_api_key = os.getenv('SERPAPI_API_KEY')
    if serpapi_api_key:
        os.environ["SERPAPI_API_KEY"] = serpapi_api_key


def predict_proba(model, test_data, sub_model_name=None):
    if config_info['train_type'] == 'reg':
        if config_info['ranking_autogluon']:
            test_data['gdate'] = pd.to_numeric(test_data.index.get_level_values(0).astype('int64'))
        predictions = model.predict(test_data, model=sub_model_name)

        if 'return' in test_data.columns:
            y_true = test_data['return']
            # 过滤掉NaN值
            valid_mask = ~(pd.isna(y_true) | pd.isna(predictions))
            if valid_mask.sum() > 0:
                y_true_valid = y_true[valid_mask]
                predictions_valid = predictions[valid_mask]
                r2 = r2_score(y_true_valid, predictions_valid)
                logging.getLogger().info(f"R² Score: {r2:.4f}")

                ic_corr, ic_p_value = pearsonr(predictions_valid, y_true_valid)
                logging.getLogger().info(f"IC Score: {ic_corr:.4f}")
        return predictions
    else:
        return model.predict_proba(test_data, model=sub_model_name)


def clear_cache():
    cache.clear()


def load_stock_data(alpha_type, market, prefix=''):
    file_name = f'refined_{prefix}{alpha_type}_{market}.pkl'
    if cache.get(file_name) is None:
        df = pd.read_pickle(file_name)
        df = df.loc[:, ~df.columns.duplicated()]
        cache[file_name] = df

    ret = cache.get(file_name)
    if len(config_info['secid_filter_train']) > 0:
        ret = ret[ret.index.get_level_values(1).isin(config_info['secid_filter_train'])]
    return ret


def get_flag_by_gates(changes, gates):
    flags = changes.apply(lambda row: (row >= row.quantile(gates)).astype(int), axis=1)
    return flags


def refine_stock_df(df, gates=[(1, 0.9), (5, 0.9), (10, 0.9), (20, 0.9)], with_flag=True):
    if config_info['index_enhance']:
        index_path = f'./{config_info["market"]}.csv'
        stock_index = pd.read_csv(index_path, index_col=0)
        stock_index.index = pd.to_datetime(stock_index.index)
        stock_index = stock_index[['close']].rename(columns={'close': 'index_close'}).reset_index()
        df_reset = df.reset_index()
        df = pd.merge(df_reset, stock_index, on='date', how='left')
        df.set_index(['date', 'sec_id'], inplace=True)

    df.reset_index(inplace=True)
    if 'return' in df.columns:
        returns = df.pivot(index='date', columns='sec_id', values='return')
    else:
        returns = df.pivot(index='date', columns='sec_id', values='return_1D')
    close = df.pivot(index='date', columns='sec_id', values='close')

    if 'index_close' in df.columns:
        index_close = df.pivot(index='date', columns='sec_id', values='index_close')
    else:
        index_close = None

    for i, j in gates:
        if i != 1:
            changes = close.pct_change(i)
            if index_close is not None:
                index_changes = index_close.pct_change(i)
        else:
            changes = returns.copy()
            if index_close is not None:
                index_changes = index_close.pct_change(1)

        if f'return_{i}D' in df.columns:
            df.drop(columns=[f'return_{i}D'], inplace=True)
        if f'flag_{i}D' in df.columns:
            df.drop(columns=[f'flag_{i}D'], inplace=True)

        changes = changes.shift(-i)
        if index_close is not None:
            index_changes = index_changes.shift(-i)
            changes = changes - index_changes
            daily_std = changes.std(axis=1, skipna=True)
            mean_std = daily_std.mean()
            changes = changes.div(daily_std, axis=0) * mean_std
            clip_range = config_info['max_return'][i] * 2
            changes = changes.clip(-clip_range, clip_range)
            # 打印changes的统计信息
            print(f'return_{i}D changes统计信息:')
            print(f'  形状: {changes.shape}')
            print(f'  均值: {changes.mean().mean():.6f}')
            print(f'  标准差: {changes.std().mean():.6f}')
            print(f'  最小值: {changes.min().min():.6f}')
            print(f'  最大值: {changes.max().max():.6f}')
            print(f'  daily_std均值: {mean_std:.6f}')
            print(f'  daily_std标准差: {daily_std.std():.6f}')

        if with_flag:
            print(f'flag_{i}D')
            flags = get_flag_by_gates(changes, j)
            flags = flags.reset_index().melt(id_vars='date', var_name='sec_id', value_name='return')
            print(flags['return'].value_counts() / len(flags))
            flags.rename(columns={'return': f'flag_{i}D'}, inplace=True)

        changes = changes.reset_index().melt(id_vars='date', var_name='sec_id', value_name='return')
        changes.rename(columns={'return': f'return_{i}D'}, inplace=True)
        df = df.merge(changes, on=['date', 'sec_id'], how='left')

        if with_flag:
            df = df.merge(flags, on=['date', 'sec_id'], how='right')

    if 'return' in df.columns:
        df.dropna(subset=['return'], inplace=True)
    else:
        df.dropna(subset=['return_1D'], inplace=True)
        df['return'] = df['return_1D']

    if index_close is not None:
        df.drop(columns=['index_close'], inplace=True)
    df.set_index(['date', 'sec_id'], inplace=True)
    return df


def refine_stock_data(alpha_type, prefix=''):
    df = pd.read_pickle(f'{prefix}{alpha_type}_{config_info["market"]}.pkl')
    df = refine_stock_df(df)
    df.to_pickle(f'refined_{prefix}{alpha_type}_{config_info["market"]}.pkl')


def get_feature_names(alpha_type, market, period_n, feature_count=-1):
    if period_n > 10:
        period_n = 10
    year = config_info.get('ic_year')
    ic_file = f'{alpha_type}_{market}_ic_{year}.csv'
    if not os.path.exists(ic_file):
        ic_file = f'{alpha_type}_{market}_ic.csv'
    if not os.path.exists(ic_file):
        return []
    ic_table = pd.read_csv(ic_file, index_col=0)
    rank_1d = abs(ic_table.loc[:, f'{period_n}D']).rank(ascending=False)
    feature_abandon = f'feature_abandon_{year}' if os.path.exists(f'feature_abandon_{year}') else "feature_abandon"
    if not config_info['decorrelated_top_features'] and config_info['remove_from_fid'] > 0 and os.path.exists(
            f"feature_importance.csv"):
        df = pd.read_csv(f"feature_importance.csv", index_col=0)
        df = df.sort_values(by='p99_high', ascending=True)
        df = df.iloc[config_info['remove_from_fid']:]
        selected_features = df.index.tolist()
        if config_info['use_basic_factors']:
            # 获取以basic_开头的列名并加入selected_features，注意去重
            basic_features = [col for col in ic_table.index if col.startswith('basic_')]
            selected_features.extend(basic_features)
            # 去重，保持顺序
            selected_features = list(dict.fromkeys(selected_features))
        return selected_features
    elif os.path.exists(feature_abandon):
        with open(feature_abandon, "r") as f:
            abandoned = [line.strip() for line in f if line.strip()]
            rank_1d = rank_1d[~rank_1d.index.isin(abandoned)]
    if feature_count <= 0:
        feature_count = config_info['feature_count']
    factor_names = rank_1d[rank_1d <= feature_count].index.tolist()
    if config_info['index_feature_count'] > 0:
        factor_names.extend([f'index_feature{i}' for i in range(1, config_info['index_feature_count'] + 1)])
    return factor_names


def reshape_daily_stock_data(stock_code, stock_df, save_path):
    stock_df.rename(columns={'开盘': 'open', '收盘': 'close', '最高': 'high', '最低': 'low', '成交量': 'volume',
                             '成交额': 'amount', '换手率': 'rate', '日期': 'date'}, inplace=True)
    stock_df.drop(columns=['振幅', '涨跌幅', '涨跌额'], inplace=True)
    stock_df.to_csv(f'./{save_path}/{stock_code}.csv')
    stock_df['date'] = pd.to_datetime(stock_df['date'])
    stock_df.set_index('date', inplace=True)
    return stock_df


def compute_alphalen_factor(returns, factor_name, stock_data):
    feature = stock_data.loc[:, factor_name]

    try:
        factors = alphalens.utils.get_clean_factor_and_forward_returns(feature, returns,
                                                                       bins=None,
                                                                       periods=(1, 5, 10), quantiles=5,
                                                                       max_loss=0.35)
    except MaxLossExceededError as e:
        print(e)
        print('fall back to binn mode')
        min_f, max_f = min(feature), max(feature)
        try:
            factors = alphalens.utils.get_clean_factor_and_forward_returns(feature, returns,
                                                                           bins=np.linspace(min_f, max_f, 5),
                                                                           periods=(1, 5, 10), quantiles=None,
                                                                           max_loss=0.52)
        except MaxLossExceededError as e:
            factors = alphalens.utils.get_clean_factor_and_forward_returns(feature, returns,
                                                                           bins=3,
                                                                           periods=(1, 5, 10), quantiles=None,
                                                                           max_loss=0.52)
    return factors


def get_instrument_list(full_mode=False):
    market = convert_to_csi_code(config_info['market'])
    start = None
    end = None
    if config_info['generate_ins_date'] is not None:
        start = config_info['generate_ins_date'][0]
        end = config_info['generate_ins_date'][1]
    instruments = []
    while len(instruments) == 0:
        if full_mode:
            instruments = D.instruments(market=market)
        else:
            instruments = [item.lstrip('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ') for item in
                           D.list_instruments(D.instruments(market=market), start_time=start, end_time=end)]
        # 将end往后延一年
        if end is not None:
            end = pd.to_datetime(end)
            end = end + pd.DateOffset(years=1)
    return instruments


def compute_ic_table_for_data(df, start=None, end=None, existing_ic=None):
    if start is not None and end is not None:
        df = df.loc[(start < df.index.get_level_values(0)) & (df.index.get_level_values(0) < end), :]
    returns = df.loc[:, ['close']].copy()
    returns = returns.pivot_table(index='date', columns='sec_id', values='close')
    returns.rename(columns={'close': 'returns'}, inplace=True)
    ret = pd.DataFrame()
    for col in df.columns:
        if col in ['return', 'open', 'high', 'low', 'close', 'volume', 'vwap', 'index_close', 'amount', 'adjclose',
                   'factor',
                   'benchmark_open', 'benchmark_close']:
            continue
        if col.startswith('flag') or col.startswith('return'):
            continue
        if existing_ic and col in existing_ic:
            continue
        print('compute ic for %s' % col)
        factors = compute_alphalen_factor(returns, col, df)
        ic = perf.mean_information_coefficient(factors)
        ic = abs(ic)
        ret = pd.concat([ret, pd.DataFrame(ic.T, columns=[col])], axis=1)

    return ret.T


def prepare_data_for_model(stock_data, factors, period, start_date, end_date, keep_close=False):
    end_date = pd.to_datetime(end_date)
    if config_info['train_type'] == 'cls':
        label_tag = f'flag_{period}D'
        drop_label = f'return_{period}D'
    else:
        label_tag = f'return_{period}D'
        drop_label = f'flag_{period}D'

    extra_labels = [label_tag, drop_label, 'return']
    if keep_close:
        extra_labels.append('close')
    extra_labels = [col for col in extra_labels if col in stock_data.columns]

    labels = factors + extra_labels
    if 'regime_id' in stock_data.columns and 'regime_id' not in labels:
        labels.append('regime_id')

    ret_data = stock_data.loc[
        (stock_data.index.get_level_values(0) >= start_date) &
        (stock_data.index.get_level_values(0) <= end_date),
        labels
    ].copy()

    if not (config_info['auto_feature_select'] or config_info['train_type'] == 'rank' or config_info[
        'ranking_autogluon']):
        if config_info['train_type'] == 'reg':
            def weights_caculator(row, end):
                base_weight = row.get(f'return_{period}D', 0) / config_info['max_return'][period]
                weights = min(config_info['max_weight'], abs(base_weight))
                if config_info['weights421']:
                    days_diff = (end - row.name[0]).days
                    max_level_days = max(config_info['more_weights_days'], 180)
                    sec_level_days = max_level_days * 2
                    if days_diff < max_level_days:
                        return 40 * (weights ** config_info['curve_2'])
                    elif days_diff < sec_level_days:
                        return 30 * (weights ** config_info['curve_2'])
                return 10 * (weights ** config_info['curve'])

            ret_data['weight'] = ret_data.apply(weights_caculator, args=(end_date,), axis=1)
        elif config_info['train_type'] == 'cls':
            if f'flag_{period}D' in ret_data.columns:
                weight = ret_data[f'flag_{period}D'].value_counts() / len(ret_data)
                weight = (1 - weight) / min(1 - weight)
                ret_data['weight'] = ret_data[f'flag_{period}D'].apply(lambda x: weight[x])
            else:
                ret_data['weight'] = 1

    for col in ['return', drop_label]:
        if col in ret_data.columns:
            ret_data.drop(columns=[col], inplace=True)
    if label_tag in ret_data.columns:
        ret_data.rename(columns={label_tag: 'return'}, inplace=True)

    if config_info['train_with_add_features']:
        add_features = prepare_add_features(start_date, end_date, False)
        extra_feat = f'return_{config_info["period_n"]}D'
        if extra_feat in add_features.columns:
            add_features.drop(columns=[extra_feat], inplace=True)
        ret_data = pd.merge(ret_data, add_features, left_index=True, right_index=True, how='left')

    ret_data.dropna(subset=ret_data.columns.difference(['return']), inplace=True)
    ret_data = ret_data.loc[~ret_data.isin([np.inf, -np.inf]).any(axis=1)]

    return ret_data


def load_model(model_name):
    if not os.path.exists('models/' + model_name):
        return None
    try:
        ret = TabularPredictor.load('models/' + model_name, require_version_match=False, verbosity=0)
        return ret if ret.is_fit else None
    except:
        return None


def stock_metric(y_true, y_pred):
    cm = confusion_matrix(y_true, y_pred, labels=[1, 0])
    total = 1 if cm[:, 0].sum() == 0 else cm[:, 0].sum()
    precision_1 = cm[0, 0] / total
    false_positive_ratio_3 = cm[1, 0] / total
    result = precision_1 - false_positive_ratio_3
    return result


def prepare_add_features(start_date, end_date, with_main_features=True):
    label = f'return_{config_info["period_n"]}D'
    stock_data = load_stock_data(config_info['alpha_type'], config_info['market'])
    df = stock_data.loc[
        (stock_data.index.get_level_values(0) >= start_date) & (stock_data.index.get_level_values(0) <= end_date), [
            label]]

    add_df = pd.read_csv(f'{config_info["market"]}_additional_features.csv', parse_dates=['date'],
                         dtype={'stock_code': str})
    add_df = add_df.loc[(add_df['date'] >= start_date) & (add_df['date'] <= end_date)]
    add_df.rename(columns={'stock_code': 'sec_id'}, inplace=True)
    add_df['sec_id'] = add_df['sec_id'].apply(lambda x: x.zfill(6))
    add_df.set_index(['date', 'sec_id'], inplace=True)

    df = pd.merge(df, add_df, left_index=True, right_index=True, how='left')

    if with_main_features:
        period = config_info['period_n']
        factors = get_feature_names(config_info['alpha_type'], config_info['market'], config_info['period_n'])
        train_data = prepare_data_for_model(stock_data, factors, period, start_date, end_date)
        train_data = remove_lastdays_from_data(train_data)
        train_data.drop(columns=['return'], inplace=True)
        df = pd.merge(df, train_data, left_index=True, right_index=True, how='left')
    return df


def remove_lastdays_from_data(train_data):
    train_data = train_data.sort_index(level=0, ascending=False)
    last_n_days = train_data.index.get_level_values(0).unique()[:config_info['period_n']]
    train_data = train_data[~train_data.index.get_level_values(0).isin(last_n_days)]
    train_data.dropna(inplace=True)
    return train_data


def compute_batch_loss(pos_batch, neg_scores):
    """
    对一个正样本批次与所有负样本计算 pairwise 排序损失，
    返回该批次损失总和和元素个数。
    """
    differences_batch = pos_batch[:, None] - neg_scores[None, :]
    batch_loss = np.log1p(np.exp(-differences_batch))
    return np.sum(batch_loss), batch_loss.size


@njit(parallel=True, fastmath=True)
def pairwise_logistic_loss_parallel(pos_scores, neg_scores):
    total_loss = 0.0
    # 并行化外层循环，每次处理一个正样本对应所有负样本对
    for i in prange(pos_scores.shape[0]):
        local_loss = 0.0
        # 内层循环不做并行处理，累加当前正样本与所有负样本的损失
        for j in range(neg_scores.shape[0]):
            diff = pos_scores[i] - neg_scores[j]
            local_loss += np.log1p(np.exp(-diff))
        total_loss += local_loss  # 这里使用加法累加各线程局部结果
    count = pos_scores.shape[0] * neg_scores.shape[0]
    return total_loss, count


def mixed_loss_metric(y_true, y_pred, pos_weight=0.9, neg_weight=0.1, lambda_rank=1.0):
    eps = 1e-8
    y_pred = np.clip(y_pred, eps, 1 - eps)
    # 加权二元交叉熵损失
    bce_loss = - (pos_weight * y_true * np.log(y_pred) +
                  neg_weight * (1 - y_true) * np.log(1 - y_pred))
    bce_loss_mean = np.mean(bce_loss)

    # 将预测概率转换为 logits
    logits = np.log(y_pred / (1 - y_pred))

    pos_idx = np.where(y_true == 1)[0]
    neg_idx = np.where(y_true == 0)[0]

    if len(pos_idx) == 0 or len(neg_idx) == 0:
        ranking_loss = 0.0
    else:
        pos_scores = logits[pos_idx]
        neg_scores = logits[neg_idx]
        total_loss, count = pairwise_logistic_loss_parallel(pos_scores, neg_scores)
        ranking_loss = total_loss / count

    total_loss_final = bce_loss_mean + lambda_rank * ranking_loss
    return total_loss_final


class MixedLossMetric(object):
    def __init__(self, pos_weight=0.9, neg_weight=0.1, lambda_rank=1.0):
        self.pos_weight = pos_weight
        self.neg_weight = neg_weight
        self.lambda_rank = lambda_rank
        self.greater_is_better = False  # 损失越低越好
        self.greater_is_better_internal = self.greater_is_better
        self.needs_pred = False  # 表示不需要"预测标签"
        self.needs_proba = True  # 表示需要概率
        self.needs_quantile = False
        self.name = "mixed_loss_metric"
        self._optimum = 1
        self.needs_class = True

    def __call__(self, y_true, y_pred, weight=None):
        if y_pred.ndim > 1 and y_pred.shape[1] > 1:
            y_pred = y_pred[:, 1]
        return mixed_loss_metric(y_true, y_pred, self.pos_weight, self.neg_weight, self.lambda_rank)


def top_mean_squarer(y_true, y_pred, *, sample_weight=None, multioutput="uniform_average", squared=False):
    sorted_idx = np.argsort(-y_pred)
    cutoff = int(np.ceil(len(y_pred) * 0.1))
    top_idx = sorted_idx[:cutoff]
    return mean_squared_error(y_true[top_idx], y_pred[top_idx], sample_weight=None, multioutput=multioutput,
                              squared=squared)


def ndcg_at_k_grouped(y_true, y_pred, X, k=10):
    """
    以 X['gdate'] 为分组，计算平均 NDCG@k。
    必须保证 X 是一个 pandas.DataFrame，且包含 'gdate' 列。
    """
    if not hasattr(X, "loc"):
        raise ValueError("X must be a DataFrame with a 'gdate' column")
    if 'gdate' not in X.columns:
        raise ValueError("X must contain a 'gdate' column")

    df = pd.DataFrame({
        'gdate': X['gdate'].values,
        'y_true': y_true,
        'y_pred': y_pred
    })
    scores = []
    # groupby preserves order if sort=False
    for _, grp in df.groupby('gdate', sort=False):
        y_true_arr = grp['y_true'].to_numpy().reshape(1, -1)
        y_pred_arr = grp['y_pred'].to_numpy().reshape(1, -1)
        # sklearn.ndcg_score expects shape (n_queries, n_items)
        scores.append(ndcg_score(y_true_arr, y_pred_arr, k=k))
    return float(np.nanmean(scores))


# 顶层函数，固定 k=10
def ndcg10_eval(y_true, y_pred, X=None, **kwargs):
    return ndcg_at_k_grouped(y_true, y_pred, X, k=10)


def train_model(stock_data, factors, period, start_date, end_date, save_path, selected_hyper=True):
    if config_info['decorrelated_top_features']:
        factors = get_decorrelated_top_features(stock_data, factors, f'return_{period}D',
                                                len(factors) - config_info['remove_from_fid'])
    if 'regime_id' in stock_data.columns and 'regime_id' not in factors:
        factors.append('regime_id')
    train_data = prepare_data_for_model(stock_data, factors, period, start_date, end_date)
    train_data = remove_lastdays_from_data(train_data)
    last_day = max(train_data.index.get_level_values(0).unique())
    metric = None
    problem_type = 'regression'
    if config_info['train_type'] == 'reg':
        metric = make_scorer("mean_squared_error", top_mean_squarer, optimum=0, greater_is_better=False)
    else:
        problem_type = 'binary'

    if config_info['ranking_autogluon']:
        metric = make_scorer(
            name='ndcg@10',
            score_func=ndcg10_eval,
            greater_is_better=True
        )
        train_data['gdate'] = pd.to_numeric(train_data.index.get_level_values(0).astype('int64'))
        train_data['return'] = train_data.groupby('gdate')['return'].transform(lambda x: x.rank(pct=True))
        train_data['return'] = np.minimum((train_data['return'] * 5).astype(int), 4)

    # elif  config_info['train_type'] == 'cls':
    #    metric = make_scorer(name='stock_metric', score_func=stock_metric)
    #    metric = MixedLossMetric(pos_weight=0.9, neg_weight=0.1, lambda_rank=1.0)

    if config_info['auto_feature_select'] or config_info['train_type'] == 'rank' or config_info['ranking_autogluon']:
        weight = None
    else:
        weight = 'weight'
    presets = config_info['presets']
    tuning_days = config_info['tuning_days']
    good_quality = (presets not in ['medium_quality'])

    if tuning_days > 0:
        tuned_date = train_data.index.get_level_values(0).max() - pd.DateOffset(days=tuning_days)
        tuned_data = train_data.loc[train_data.index.get_level_values(0) >= tuned_date, :]
        holdout_frac = None

        if not config_info['ranking_autogluon']:
            unique_sec_ids = tuned_data.index.get_level_values(1).unique()
            selected_sec_ids = np.random.RandomState(0).choice(
                unique_sec_ids, size=len(unique_sec_ids) // 2, replace=False)

            with open('tuning_sec_ids.txt', 'w') as f:
                for sec_id in selected_sec_ids:
                    f.write(f"{sec_id}\n")
            tuned_data_sample = tuned_data.loc[tuned_data.index.get_level_values(1).isin(selected_sec_ids)]
            train_data = pd.concat([
                train_data.loc[train_data.index.get_level_values(0) <= tuned_date, :],
                tuned_data.loc[~tuned_data.index.get_level_values(1).isin(selected_sec_ids), :]
            ])
            tuned_data = tuned_data_sample

    else:
        tuned_data = None
        holdout_frac = 0.1

    use_bag_holdout = True
    global_seed = config_info['train_seed']
    kwargs = {}
    groups_name = None
    if good_quality:
        folds = config_info['num_bag_folds']
        kwargs = {
            'num_stack_levels': config_info['num_stack_levels'],
            'num_bag_folds': folds,
        }
        if folds > 0:
            groups_name = 'group'
            group_index = 0 if config_info['ranking_autogluon'] else 1
            train_indices = train_data.index.get_level_values(group_index).unique()
            np.random.seed(global_seed)
            train_indices = np.array(train_indices)
            np.random.shuffle(train_indices)
            groups = np.array_split(train_indices, folds)
            train_data['group'] = 0
            for fold_idx, group in enumerate(groups):
                mask = train_data.index.get_level_values(group_index).isin(group)
                train_data.loc[mask, 'group'] = fold_idx

    if isinstance(selected_hyper, dict):
        hyperparameters = selected_hyper
    elif selected_hyper:
        hyperparameters = get_hyperparameters(global_seed)
    else:
        hyperparameters = 'zeroshot' if config_info['zeroshot'] else ALL_HYPERPARAMETERS
    if config_info['ranking_autogluon']:
        hyperparameters = {LGBMRankerModel: hyperparameters['GBM'], CatBoostRankerModel: hyperparameters['CAT']}

    logging.getLogger().info('%s stocks in the training' % len(train_data.index.get_level_values(1).unique().tolist()))
    logging.getLogger().info(f'start at {start_date}, end at {end_date}， last date is {last_day}')

    logging.getLogger().info(f'regimeID in  training: {"regime_id" in stock_data.columns}')

    logging.getLogger().info(f'weights is in columns {"weight" in train_data.columns}')

    pred = TabularPredictor(problem_type=problem_type, label='return', path=f'models/{save_path}', eval_metric=metric,
                            sample_weight=weight,
                            weight_evaluation=weight is not None, groups=groups_name).fit(
        train_data,
        tuned_data,
        verbosity=config_info['ag_verbosity'],
        time_limit=config_info['train_time_limit'],
        presets=presets,
        keep_only_best=False,
        use_bag_holdout=use_bag_holdout,
        hyperparameters=hyperparameters,
        ds_args={'enable_ray_logging': True},
        num_gpus=0 if config_info['cpu'] else 1,
        holdout_frac=holdout_frac,
        feature_prune_kwargs={} if config_info['auto_feature_select'] else None,
        hyperparameter_tune_kwargs='auto' if config_info['hyperparameter_tune_kwargs'] else None,
        **kwargs
    )
    if config_info['num_stack_levels'] == 0 and config_info['enable_second_ensemble']:
        all_models = pred.model_names()
        l1_models = [m for m in all_models if 'WeightedEnsemble' not in m]
        pred.fit_weighted_ensemble(
            base_models=list(l1_models))
    fid_path = f'feature_importance.csv'
    if tuned_data is not None and not os.path.exists(fid_path):
        logging.getLogger().info(f'tuning data lens {tuned_data.shape[0]}')
        fid = pred.feature_importance(tuned_data)
        fid['imp_div_std'] = fid['importance'] / fid['stddev']
        fid['neg_log_p'] = -np.log10(fid['p_value'])
        fid['z_p99'] = zscore(fid['p99_high'])
        fid['z_imp_div_std'] = zscore(fid['imp_div_std'])
        fid['z_neg_log_p'] = zscore(fid['neg_log_p'])
        fid['composite_score'] = fid['z_p99'] + fid['z_imp_div_std'] + fid['z_neg_log_p']
        fid.to_csv(fid_path)
    return pred


def get_hyperparameters(global_seed):
    hyperparameters = {}
    if config_info['zeroshot']:
        for model_name in config_info.get('models_to_use', ['GBM', 'CAT', 'FASTAI']):
            if model_name in configs.hyperparameter_portfolio_small:
                hyperparameters[model_name] = configs.hyperparameter_portfolio_small[model_name]
    else:
        for model_name in config_info.get('models_to_use', ['GBM', 'CAT', 'FASTAI']):
            if model_name in ALL_HYPERPARAMETERS:
                if model_name == 'GBM':
                    if isinstance(ALL_HYPERPARAMETERS[model_name], list):
                        if len(config_info['gbm_to_use']) > 0:
                            hyperparameters[model_name] = [ALL_HYPERPARAMETERS[model_name][int(i)] for i in
                                                           config_info['gbm_to_use']]
                        else:
                            hyperparameters[model_name] = ALL_HYPERPARAMETERS[model_name]
                    else:
                        hyperparameters[model_name] = ALL_HYPERPARAMETERS[model_name]
                else:
                    hyperparameters[model_name] = ALL_HYPERPARAMETERS[model_name]

    for model_name, model_params in hyperparameters.items():
        if isinstance(model_params, list):
            for param_set in model_params:
                if 'ag_args_fit' in param_set:
                    param_set['ag_args_fit']['max_time_limit'] = config_info['max_time_sub_model']
                else:
                    param_set['ag_args_fit'] = dict(max_time_limit=config_info['max_time_sub_model'])
                for seed_key in param_set:
                    if seed_key.endswith('seed'):
                        param_set[seed_key] = global_seed
        else:
            if 'ag_args_fit' in model_params:
                model_params['ag_args_fit']['max_time_limit'] = config_info['max_time_sub_model']
            else:
                model_params['ag_args_fit'] = dict(max_time_limit=config_info['max_time_sub_model'])
            for seed_key in model_params.keys():
                if seed_key.endswith('seed'):
                    model_params[seed_key] = global_seed

    if config_info['cpu']:
        if 'GBM' in hyperparameters and isinstance(hyperparameters['GBM'], list):
            for param_set in hyperparameters['GBM']:
                if 'device_type' in param_set:
                    param_set['device_type'] = 'cpu'

        if 'XGB' in hyperparameters:
            if 'tree_method' in hyperparameters['XGB']:
                hyperparameters['XGB']['tree_method'] = 'hist'
            if 'predictor' in hyperparameters['XGB']:
                hyperparameters['XGB']['predictor'] = 'cpu_predictor'
    else:
        if 'GBM' in hyperparameters and isinstance(hyperparameters['GBM'], list):
            for param_set in hyperparameters['GBM']:
                if 'device_type' in param_set:
                    param_set['device_type'] = 'gpu'
    return hyperparameters


def prepare_bt_data(stock_data, cerebro_bt, start_date, end_date):
    def to_qlib_code(code):
        if code.startswith('6'):
            return f"SH{code}"
        elif code.startswith(('0', '1', '2', '3')):
            return f"SZ{code}"
        elif code.startswith(('87', '83', '4')):
            return f"BJ{code}"
        else:
            return code

    index_data = pd.read_csv(f'{config_info["market"]}.csv', index_col=0, parse_dates=True)
    index_data = index_data.loc[(index_data.index >= start_date) & (index_data.index <= end_date), :]
    index_data.rename(columns={'close': 'Close', 'open': 'Open', 'high': 'High', 'low': 'Low', 'volume': 'Volume'},
                      inplace=True)
    data = bt.feeds.PandasData(dataname=index_data)
    cerebro_bt.adddata(data, name=config_info["market"])

    fields = ["$open", "$high", "$low", "$close", "$volume"]

    instruments = [to_qlib_code(cd) for cd in stock_data]

    data_df = D.features(
        instruments=instruments,
        fields=fields,
        start_time=start_date,
        end_time=end_date
    )

    all_codes = data_df.index.get_level_values('instrument').unique()

    for q_code in all_codes:
        df_single = data_df.xs(q_code, level='instrument')
        df_single = df_single.rename(columns={
            '$open': 'Open',
            '$high': 'High',
            '$low': 'Low',
            '$close': 'Close',
            '$volume': 'Volume'
        })

        if not isinstance(df_single.index, pd.DatetimeIndex):
            df_single.index = pd.to_datetime(df_single.index)

        original_code = q_code.lstrip('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ')

        trading_days = D.calendar(start_time=start_date, end_time=end_date)
        df_single = df_single.reindex(trading_days, method='ffill')
        df_single.fillna(method='ffill', inplace=True)
        df_single.fillna(method='bfill', inplace=True)
        data = bt.feeds.PandasData(dataname=df_single)
        cerebro_bt.adddata(data, name=original_code)


def prepare_bt_pred_data(pred_data, df, topn=10):
    if config_info['train_type'] == 'cls':
        pred_pos_data = pd.DataFrame(pred_data, index=pred_data.index, columns=[1])
        pred_pos_data.rename(columns={1: 'return'}, inplace=True)
        pred_neg_data = pd.DataFrame(pred_data, index=pred_data.index, columns=[0])
        pred_neg_data.rename(columns={0: 'return'}, inplace=True)
        pred_data = pred_pos_data - pred_neg_data
    else:
        pred_data = pd.DataFrame(pred_data, index=pred_data.index, columns=['return'])
    pred_data['date'] = pred_data.index.get_level_values(0)
    pred_data['sec_id'] = pred_data.index.get_level_values(1)
    pred_data.reset_index(drop=True, inplace=True)
    secid_filter = config_info['secid_filter']
    if len(secid_filter) > 0:
        pred_data = pred_data.loc[pred_data['sec_id'].isin(secid_filter), :]
    pred_data['rank'] = pred_data.groupby('date')['return'].rank(ascending=False, method='min')
    if topn > 0:
        pred_data = pred_data.loc[pred_data['rank'] <= topn].copy()
    if df is not None:
        pred_data = add_additional_features(pred_data, df)
    pred_data.sort_values(['date', 'rank'], inplace=True)
    return pred_data


def add_additional_features(backtrader_pred, df):
    ret5 = df['vwap'].groupby('sec_id').pct_change(5)
    ret5 = pd.DataFrame(ret5)
    ret5.rename(columns={'vwap': 'vwap5'}, inplace=True)
    ret = pd.merge(backtrader_pred, ret5, left_on=['date', 'sec_id'], right_on=['date', 'sec_id'],
                   how='left')
    feature_names = config_info['added_alpha_for_stratergy']
    if feature_names is not None:
        feature_list = [feature.strip() for feature in feature_names.split(',')]
        feature_list = [feature[1:] if feature.startswith('_') else feature for feature in feature_list]

        if config_info['train_with_add_features']:
            add_features = prepare_add_features(min(backtrader_pred['date']), max(backtrader_pred['date']), False)
            existing_columns = [col for col in feature_list if col in add_features.columns]
            if len(existing_columns) > 0:
                add_features = add_features.loc[:, existing_columns]
            add_features.reset_index(inplace=True)
            ret = pd.merge(ret, add_features, left_on=['date', 'sec_id'], right_on=['date', 'sec_id'], how='left')

        existing_columns = [col for col in feature_list if col in df.columns]
        ret = pd.merge(ret, df[existing_columns], left_on=['date', 'sec_id'], right_on=['date', 'sec_id'],
                       how='left')

    return ret


def init_logger():
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    ch.setFormatter(formatter)
    logger.addHandler(ch)

    if config_info['trade_log']:
        fh = logging.FileHandler('logfile.log', mode='a')
        fh.setLevel(logging.INFO)
        fh.setFormatter(formatter)
        logger.addHandler(fh)

    pd.set_option('display.max_columns', None)
    pd.set_option('display.max_rows', None)
    pd.set_option('display.width', None)
    pd.set_option('display.max_colwidth', None)

    return logger


def send_email(title, msg, addr):
    try:
        smtp = smtplib.SMTP()
        smtp.connect('smtp.126.com')
        from_adr = '<EMAIL>'
        smtp.login(from_adr, 'QNspf6Q8HQKy5VBj')
        mime_msg = MIMEText(msg, 'plain', 'utf-8')
        mime_msg['Subject'] = Header(title, 'utf-8')
        mime_msg['From'] = from_adr
        mime_msg['To'] = addr
        smtp.sendmail(from_adr, addr.split(','), mime_msg.as_string())
        smtp.quit()
    except:
        pass


def send_email_attachment(title, addr, msg, *files):
    try:
        smtp = smtplib.SMTP()
        smtp.connect('smtp.126.com')
        from_adr = '<EMAIL>'
        smtp.login(from_adr, 'QNspf6Q8HQKy5VBj')
        mime_msg = MIMEMultipart()
        mime_msg['Subject'] = Header(title, 'utf-8')
        mime_msg['From'] = from_adr
        mime_msg['To'] = addr
        mime_msg.attach(MIMEText(msg, 'plain', 'utf-8'))
        for file_path in files:
            with open(file_path, 'rb') as f:
                attachment_content = MIMEApplication(f.read(), _subtype='txt')
                attachment_content.add_header('Content-Disposition', 'attachment', filename=file_path.split("/")[-1])
                mime_msg.attach(attachment_content)
        smtp.sendmail(from_adr, addr.split(','), mime_msg.as_string())
        smtp.quit()
    except:
        pass


def notify_by_email(title, msg=None, also_to_jammie=False, force_send=False):
    if not config_info['notify_by_mail'] and not force_send:
        return
    if msg is None:
        msg = title
    send_email(title, msg, config_info['mail'])
    if also_to_jammie:
        send_email(title, msg, '<EMAIL>')


def notify_by_email_with_attachment(title, *file_path):
    send_email_attachment(title, config_info['mail'], '', *file_path)


def get_last_time():
    market = pd.read_csv(f'{config_info["market"]}.csv', index_col=0)
    now = pd.to_datetime(market.index[-1])
    return now


def prepare_data_for_cerebro(df, end_date, test_start_date, tuning_sec_only=False):
    cerebro = bt.Cerebro()
    bt_data = df.loc[(df.index.get_level_values(0) >= test_start_date) & (
            df.index.get_level_values(0) <= end_date), :].index.get_level_values(1).unique().tolist()
    if tuning_sec_only:
        with open('tuning_sec_ids.txt', 'r') as f:
            tuning_sec_ids = f.read().splitlines()
            bt_data = list(set(bt_data) & set(tuning_sec_ids))
    prepare_bt_data(bt_data, cerebro, test_start_date, end_date)
    return cerebro


def prepare_cerebro(cerebro):
    cerebro.broker.set_coo(True)
    cerebro.broker.setcash(10000000.0)
    cerebro.broker.setcommission(commission=0.001)
    cerebro.addanalyzer(bt.analyzers.TimeReturn)
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, timeframe=bt.TimeFrame.Days, annualize=True, riskfreerate=0.0)
    cerebro.addanalyzer(AnnualizedSortinoRatio)
    cerebro.addanalyzer(bt.analyzers.DrawDown)
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer)
    cerebro.addanalyzer(bt.analyzers.AnnualReturn)
    cerebro.addobserver(bt.observers.Value)
    cerebro.addobserver(bt.observers.DrawDown)


def draw_values(stats, save_path):
    values = stats[0].observers.value.lines.value.get(size=stats[0].data.datetime.buflen())
    dates = [bt.num2date(x) for x in stats[0].data.datetime.array]
    plt.figure(figsize=(10, 6))
    plt.plot(dates, values)
    plt.title('Value over Time')
    plt.xlabel('Date')
    plt.ylabel('Value')
    plt.savefig(save_path)


def dump_last_broker(cerebro, stat):
    broker = cerebro.broker
    positions = broker.positions
    ret = '\n-------operations-------\n'
    sells = []
    buys = []
    holdings = []
    file_name = pd.to_datetime(stat.data.datetime.date(0)).strftime('%Y-%m-%d')
    file_name = f'{file_name}_holding.txt'
    for order in stat.last_order:
        ret += f"{order[0]} {order[1]}\n"
        if order[0] == 'sel':
            sells.append(order[1])
        else:
            buys.append(order[1])
    ret += '\n-------holding------\n'
    for data, position in positions.items():
        if not position.size > 0:
            continue
        if data._name in sells:
            continue

        last_day_change = (data.close[0] - data.close[-1]) / data.close[-1]
        holding_period_change = (data.close[0] - position.price) / position.price
        holdings.append(data._name)
        ret += f'{data._name}, 最后一日涨幅: {format(last_day_change * 100, ".2f")}%, 持有收益: {format(holding_period_change * 100, ".2f")}%\n'

    if config_info['trade_log']:
        with open(file_name, 'w') as f:
            f.write('Buys:\n')
            for buy in buys:
                f.write(f'{buy}\n')
            f.write('\nHoldings:\n')
            for holding in holdings:
                f.write(f'{holding}\n')
    return ret


def dump_stats_cerebro(cerebro, stats, mail_title=None):
    values = stats[0].observers.value.lines.value.get(size=stats[0].data.datetime.buflen())
    dates = [bt.num2date(x) for x in stats[0].data.datetime.array]
    values = pd.Series(values, index=dates)
    change = values.pct_change()[-1]
    msg = mail_title + '\r\n\r\n'
    msg += str(stats[0].analyzers.annualreturn.get_analysis()) + '\r\n'
    msg += str(stats[0].analyzers.sharperatio.get_analysis()) + '\r\n'
    msg += str(stats[0].analyzers.annualizedsortinoratio.get_analysis()) + '\r\n'
    msg += str(stats[0].analyzers.drawdown.get_analysis()) + '\r\n'

    # Get trade analysis
    trade_analysis = stats[0].analyzers.tradeanalyzer.get_analysis()
    if 'len' in trade_analysis:
        avg_holding = str(trade_analysis['len']['average'])
        msg += f'avg Holding: {avg_holding}\r\n'

    # Calculate winning ratio
    total_completed = trade_analysis.get('total', {}).get('closed', 0)
    total_won = trade_analysis.get('won', {}).get('total', 0)
    if total_completed > 0:
        win_ratio = (total_won / total_completed) * 100
        msg += f'Win Ratio: {win_ratio:.2f}%\r\n'

    msg += f'last day change: {change}'

    if config_info['dump_change']:
        msg += dump_last_broker(cerebro, stats[0])
    if (mail_title is not None) and (config_info['notify_by_mail'] is True):
        value_png_path = 'value.png'
        draw_values(stats, value_png_path)
        send_email_attachment(mail_title, config_info['mail'], msg, value_png_path)

    logger = logging.getLogger()
    logger.info(msg)


class TopNStratergy(bt.Strategy):
    params = (
        ('topn', 10),
        ('period', 10),
        ('printlog', False),
        ('pred_data', None),
        ('max_down', 0.1),
        ('min_hold', 1000),
        ('min_return', -1),
        ('trade_data', None),
        ('min_mean_return', -1),
        ('train_year', config_info['train_year']),
        ('rank_model', None)
    )

    def log(self, txt, dt=None):
        dt = dt or self.data.datetime.date(0)
        if self.params.printlog:
            logging.getLogger().info('%s, %s' % (dt.isoformat(), txt))

    def notify_order(self, order):
        if order.status in [order.Submitted, order.Accepted]:
            return
        sec_code = order.data._name
        if order.status in [order.Completed]:
            if order.isbuy():
                exec_price = order.executed.price
                size = order.executed.size
                exec_date = self.data.datetime.date(0)  # 当前bar对应的日期
                self.buy_pre[sec_code] = [exec_price, exec_date, size]
                self.log('BUY EXECUTED %s, Price: %.4f, Cost: %.2f, Comm %.2f' %
                         (sec_code,
                          order.executed.price,
                          order.executed.value,
                          order.executed.comm))
            elif order.issell():
                if sec_code in self.buy_pre:
                    del self.buy_pre[sec_code]
                self.log('SELL EXECUTED %s, Price: %.4f, Cost: %.2f, Comm %.2f' %
                         (sec_code,
                          order.executed.price,
                          order.executed.value,
                          order.executed.comm))
        elif order.status == order.Margin:
            attempted_cost = order.created.price * order.created.size
            current_cash = self.broker.get_cash()
            shortfall = attempted_cost - current_cash

            self.log(f"Margin error for {sec_code}, short of {shortfall:.2f} cash")
        elif order.status in [order.Canceled, order.Rejected]:
            self.log('Order Canceled/Rejected %s:%s ' % (sec_code, order.isbuy()))

    def notify_trade(self, trade):
        if not trade.isclosed:
            return
        self.log('OPERATION PROFIT, GROSS %.2f, NET %.2f' % (trade.pnl, trade.pnlcomm))

    def __init__(self):
        self.skip_days = config_info['skip_days']
        self.is_last_day = False
        self.last_day = None
        self.last_order = []
        self.current_bar = 0
        self.vix = False
        self.topn = self.p.topn
        self.period = self.p.period
        self.pred_data = self.p.pred_data
        self.buy_pre = {}
        self.max_down = self.p.max_down
        self.min_hold = self.p.min_hold
        self.min_return = self.p.min_return
        self.trade_data = self.p.trade_data
        self.min_mean_return = self.p.min_mean_return
        self.train_year = self.p.train_year
        self.rank_model = self.p.rank_model
        if self.pred_data is not None:
            self.farrest_day = self.pred_data['date'].min()
        self.cur_step_pred_data = None
        if config_info['wencai_scores_path'] is not None:
            self.wencai_data = pd.read_csv(config_info['wencai_scores_path'], index_col=[0, 1], parse_dates=True,
                                           dtype={1: str})
            if self.pred_data is not None:
                wencai_columns = 'predict'
                updated = pd.merge(self.pred_data, self.wencai_data[wencai_columns], left_on=['date', 'sec_id'],
                                   right_index=True)
                if not updated.empty:
                    self.pred_data = updated
                    temp = []
                    select_range = config_info['select_range']
                    for date, group in self.pred_data.groupby('date'):
                        group = group.copy()
                        top_rows = group[group['rank'] <= select_range].copy()
                        if not top_rows.empty:
                            top_rows = top_rows.sort_values(by=wencai_columns, ascending=False)
                            top_rows['rank'] = range(1, len(top_rows) + 1)
                            group.update(top_rows)
                        temp.append(group)
                    self.pred_data = pd.concat(temp)
        else:
            self.wencai_data = None
        self.index_data = pd.read_csv(f'{config_info["market"]}.csv', index_col=0, parse_dates=True)
        if os.path.exists('trade_date.txt') and config_info['consider_trade_date']:
            with open('trade_date.txt', 'r') as f:
                self.trade_date = f.readlines()
                self.trade_date = [pd.to_datetime(date) for date in self.trade_date]
        else:
            self.trade_date = None

    def get_predict_data(self, predict_date):
        return self.pred_data.loc[(self.pred_data['date'] == predict_date), :]

    def get_low_level(self, sec_code):
        if self.cur_step_pred_data.empty:
            return 0
        returns = self.cur_step_pred_data.loc[self.cur_step_pred_data['sec_id'] == sec_code, 'rank_sel']
        if returns.empty:
            return 1
        if returns.values[0] <= self.topn * config_info['topn_dropout_k']:
            return 0
        return 1 if returns.values[0] <= config_info['bottom_rank'] else 2

    def check_sells(self, dt, predict_date):
        buy_pre_items = self.buy_pre.items()
        added_cash = 0

        buy_pre_items = sorted(
            buy_pre_items,
            key=lambda x: (
                self.cur_step_pred_data.loc[
                    (self.cur_step_pred_data['sec_id'] == x[0]),
                    'rank_sel'
                ].values[0]
                if not self.cur_step_pred_data.loc[
                    (self.cur_step_pred_data['sec_id'] == x[0])
                ].empty
                else float('inf')
            ),
            reverse=True
        )
        sells = 0
        for sec_code, info in buy_pre_items:
            price = info[0]
            buy_date = info[1]
            size = info[2]

            cur_close = self.getdatabyname(sec_code).close[0]
            diff = (price - cur_close) / price
            last_close = self.getdatabyname(sec_code).close[-1]
            day_max_down = config_info['day_max_down']
            low_level = self.get_low_level(sec_code)
            if ((config_info['hold_till_end'] > 0) and (
                    predict_date - pd.Timestamp(buy_date)).days >= config_info['hold_till_end'] * 7 / 5) or (
                    (config_info['hold_till_end'] == 0) and (
                    (diff > self.max_down)
                    or low_level >= 1
                    or ((last_close - cur_close) / last_close > day_max_down)
            )):
                price_sell = self.getdatabyname(sec_code).close[0]
                self.close(data=self.getdatabyname(sec_code))
                added_cash += (size * price_sell)
                if self.is_last_day:
                    self.last_order.append(('sel', sec_code))
                sells += 1
                if sells >= config_info['max_sell'] and low_level < 2:
                    break
        return self.topn - (len(self.buy_pre) - sells), added_cash

    def close_all(self):
        for sec_code, info in self.buy_pre.items():
            self.close(data=self.getdatabyname(sec_code))
            if self.is_last_day:
                self.last_order.append(('sell', sec_code))

    def get_mean_returns(self, dt, predict_date):
        mean_returns = self.pred_data.loc[pd.to_datetime(self.pred_data['date']) == predict_date, 'return'].mean()
        return mean_returns

    def get_selected_sec(self, dt, i, predict_date):
        if self.cur_step_pred_data.empty or len(self.cur_step_pred_data) <= i:
            return None, None, True
        selected = self.cur_step_pred_data.iloc[i]
        sec_code = selected['sec_id']
        returns = selected['return']
        if not config_info['buy_high_price']:
            if sec_code in self.getdatanames():
                if self.getdatabyname(sec_code).close[-1] / self.getdatabyname(sec_code).close[-2] >= 1.095:
                    if pd.to_datetime(self.data.datetime.date(-1)) < predict_date and \
                            pd.to_datetime(self.data.datetime.date(-2)) < predict_date:
                        self.log(f'{sec_code} is too high')
                        return sec_code, returns, True
        return sec_code, returns, False

    def next(self):
        if self.skip_days > 0:
            self.skip_days -= 1
            return
        if self.last_day is None:
            self.last_day = pd.to_datetime(self.data.datetime.date(-1))
            first_run = True
        else:
            first_run = False
        predict_date = dt = pd.to_datetime(self.data.datetime.date(0))
        if predict_date == self.last_day:
            self.is_last_day = True
        self.current_bar += 1
        self.prepare_cur_step_predicted_data(predict_date)
        if self.trade_date is not None:
            if predict_date not in self.trade_date:
                self.close_all()
                self.vix = False
                return
        if config_info['consider_vix']:
            if self.vix:
                if self.can_trade():
                    self.vix = False
                else:
                    return
            if not first_run:
                self.vix = self.evaluate_and_clear_positions(predict_date)
                if self.vix:
                    return

        for i in range(0, min(config_info['select_range'], len(self.cur_step_pred_data))):
            newline = pd.DataFrame([self.cur_step_pred_data.iloc[i]['sec_id'], dt]).T
            newline.columns = self.trade_data[0].columns
            self.trade_data[0] = pd.concat([self.trade_data[0], newline])

        empty_count, added_cash = self.check_sells(dt, predict_date)
        if empty_count <= 0:
            return
        self.update_cur_step_data_for_empty_slots(max(config_info['select_range'], empty_count * 2))
        mean_returns = self.get_mean_returns(dt, predict_date)
        if mean_returns < self.min_mean_return:
            return

        avail_cash = self.broker.get_cash() + added_cash

        each_cash = self.broker.get_value() / self.topn
        for i in range(0, len(self.cur_step_pred_data)):
            if avail_cash < each_cash * 0.8:
                break
            sec_code, returns, should_skip = self.get_selected_sec(dt, i, predict_date)
            if sec_code is None:
                break
            if returns < self.min_return:
                break
            if should_skip:
                continue
            if self.buy_pre.get(sec_code) is not None:
                continue
            if sec_code not in self.getdatanames():
                continue
            price = self.getdatabyname(sec_code).close[0]
            buy_cash = min(avail_cash, each_cash) * 0.98
            avail_cash -= buy_cash
            size = int(buy_cash / price)
            self.buy(data=self.getdatabyname(sec_code), size=size)
            if self.is_last_day:
                self.last_order.append(('buy', sec_code))
            empty_count -= 1
            if empty_count <= 0:
                break

    def prepare_cur_step_predicted_data(self, predict_date):
        now_time = pd.to_datetime(predict_date)
        start_time = now_time.replace(day=1)
        instruments = D.list_instruments(D.instruments(market=convert_to_csi_code(config_info['market'])),
                                         start_time=start_time, end_time=now_time)
        instruments = [itr.lstrip('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ') for itr in instruments]
        cur_step_pred_data = self.pred_data.loc[(self.pred_data['date'] == predict_date)]
        cur_step_pred_data = cur_step_pred_data.loc[cur_step_pred_data['sec_id'].isin(instruments) |
                                                    cur_step_pred_data['sec_id'].isin(self.buy_pre.keys())]
        cur_step_pred_data['rank_sel'] = cur_step_pred_data['rank'].rank(ascending=True)
        self.cur_step_pred_data = cur_step_pred_data.loc[
            (cur_step_pred_data['rank_sel'] <= self.topn * config_info['topn_dropout_k']) |
            (cur_step_pred_data['sec_id'].isin(self.buy_pre.keys()))]
        self.cur_step_pred_data = self.cur_step_pred_data.sort_values(by='rank_sel')
        if config_info['random_backtest']:
            n = config_info['select_range']
            if n < len(self.cur_step_pred_data):
                subset = self.cur_step_pred_data.head(n).sample(frac=1, random_state=random.randint(0, 100000))
                remaining = self.cur_step_pred_data.iloc[n:]
                self.cur_step_pred_data = pd.concat([subset, remaining])
            else:
                self.cur_step_pred_data = self.cur_step_pred_data.sample(frac=1, random_state=random.randint(0, 100000))

    def get_data_value(self, sec_id, attr, idx):
        if sec_id in self.getdatanames():
            return getattr(self.getdatabyname(sec_id), attr)[idx]
        else:
            return np.nan

    def can_trade(self):
        # 计算当前日期的价格变化，并仅在满足条件时允许交易
        open_prices = self.cur_step_pred_data.apply(
            lambda row: self.get_data_value(row['sec_id'], 'open', 0), axis=1)
        close_prices = self.cur_step_pred_data.apply(
            lambda row: self.get_data_value(row['sec_id'], 'close', 0), axis=1)
        current_day_change = ((close_prices - open_prices) / open_prices).mean()

        if np.isnan(current_day_change):
            return False

        if current_day_change > config_info['vix_up_level']:
            close_neg1 = self.cur_step_pred_data.apply(
                lambda row: self.get_data_value(row['sec_id'], 'close', -1), axis=1)
            current_day_change = ((close_prices - close_neg1) / close_neg1).mean()
            if current_day_change > config_info['vix_up_level']:
                self.log("当日涨幅为%.4f，解除恐慌状态" % current_day_change)
                return True
        elif len(self.cur_step_pred_data) >= 2:
            close_0 = self.cur_step_pred_data.apply(
                lambda row: self.get_data_value(row['sec_id'], 'close', 0), axis=1)
            close_neg1 = self.cur_step_pred_data.apply(
                lambda row: self.get_data_value(row['sec_id'], 'close', -1), axis=1)
            close_neg2 = self.cur_step_pred_data.apply(
                lambda row: self.get_data_value(row['sec_id'], 'close', -2), axis=1)

            first_day_return = ((close_0 - close_neg1) / close_neg1).mean()
            second_day_return = ((close_neg1 - close_neg2) / close_neg2).mean()

            if np.isnan(first_day_return) or np.isnan(second_day_return):
                return False

            if first_day_return > 0 and second_day_return > 0:
                self.log("两日上涨，解除恐慌状态")
                return True
        return False

    def evaluate_and_clear_positions(self, cur_date):
        # 计算所有当前步骤预测数据的平均开盘-收盘比率
        open_price = self.index_data.loc[cur_date, 'open']
        close_price = self.index_data.loc[cur_date, 'close']
        avg_open_close_ratio = ((open_price - close_price) / open_price).mean()

        if np.isnan(avg_open_close_ratio):
            return False

        # 如果比率超过阈值，则清仓
        if avg_open_close_ratio > config_info['vix_down_level']:
            self.close_all()
            self.log("当日跌幅超过%.4f，进入恐慌状态" % avg_open_close_ratio)
            return True

        prev_day = pd.to_datetime(self.data.datetime.date(-1))
        prev_close = self.index_data.loc[prev_day, 'close']
        avg_close_change_ratio = ((prev_close - close_price) / prev_close).mean()

        if np.isnan(avg_close_change_ratio):
            return False

        # 如果变化比率超过阈值，则清仓
        if avg_close_change_ratio > config_info['vix_down_level']:
            self.close_all()
            self.log("当日相对上日跌幅超过%.4f，进入恐慌状态" % avg_close_change_ratio)
            return True
        return False

    def update_cur_step_data_for_empty_slots(self, empty_count):
        added_feature = config_info.get('added_alpha_for_stratergy')
        if added_feature:
            feature_list = [feature.strip() for feature in added_feature.split(',')]
            if len(feature_list) == 1:
                ascending = added_feature.startswith('_')
                if ascending:
                    added_feature = added_feature[1:]
                rerange = min(config_info['topn'], empty_count)
                top2n = self.cur_step_pred_data.head(rerange)
                top2n = top2n.sort_values(by=added_feature, ascending=ascending)
                self.cur_step_pred_data.iloc[:rerange] = top2n
            else:
                coefficients = []
                actual_features = []

                for feature in feature_list:
                    if feature.startswith('_'):
                        coefficients.append(-1)
                        actual_features.append(feature[1:])
                    else:
                        coefficients.append(1)
                        actual_features.append(feature)

                top2n = self.cur_step_pred_data.head(empty_count).copy()
                rank_df = top2n[actual_features].rank(method='first', ascending=False)
                sort_sum = rank_df.multiply(coefficients).sum(axis=1)
                top2n['sort_sum'] = sort_sum
                top2n_sorted = top2n.sort_values(by='sort_sum', ascending=True)
                self.cur_step_pred_data.iloc[:empty_count] = top2n_sorted.drop(columns='sort_sum')

        if self.rank_model is not None:
            current_date = pd.to_datetime(self.datas[0].datetime.date(0))
            rerange = min(config_info['topn'], empty_count)
            top2n_stocks = self.cur_step_pred_data.head(rerange)['sec_id'].to_list()
            rank_predictions = predict_ranks(self.rank_model, top2n_stocks, current_date)
            rank_pred_data = prepare_bt_pred_data(rank_predictions, None, 0)
            rank_pred_data['trade_rank'] = rank_pred_data['rank']
            if not rank_predictions.empty:
                self.cur_step_pred_data = pd.merge(
                    self.cur_step_pred_data,
                    rank_pred_data[['sec_id', 'trade_rank']],
                    on='sec_id',
                    how='left'
                )

                topn_part = self.cur_step_pred_data.head(rerange).copy()
                if not topn_part.empty:
                    topn_part = topn_part.sort_values('trade_rank', ascending=True)
                    self.cur_step_pred_data.iloc[:len(topn_part)] = topn_part


def plot_stock_charts(stock_codes, output_dir, days=90):
    os.makedirs(output_dir, exist_ok=True)
    code_names = pd.read_csv(os.path.expanduser('~/stock_comments.csv'), index_col=0, dtype={'sec_id': str})
    for code in stock_codes:
        today = datetime.date.today()
        df = D.features([f"SH{code}" if code.startswith('6') else f"SZ{code}"],
                        fields=["$close", "$open", "$high", "$low", "$volume"],
                        start_time=today - datetime.timedelta(days=days), end_time=today)
        code_name = code_names.loc[code, '名称']
        if df.empty:
            print(f"No data available for stock {code}")
            continue

        df.reset_index(level=0, inplace=True)
        df = df[df.index >= df.index[-1] - pd.DateOffset(days=days)]

        if df.empty:
            print(f"No data available for the last month for stock {code}-{code_name}")
            continue

        df.columns = ['symbol', 'close', 'open', 'high', 'low', 'volume']
        plt.rcParams['font.sans-serif'] = [
            'Noto Sans CJK JP']
        plt.rcParams['axes.unicode_minus'] = False  # Ensure minus signs are displayed correctly

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 6), gridspec_kw={'height_ratios': [3, 1]})
        mpf.plot(df, type='candle', mav=(5, 10, 20), volume=ax2, ax=ax1, show_nontrading=True)

        ax1.set_title(f"{code}-{code_name}")

        plt.savefig(os.path.join(output_dir, f"{code}.png"))
        plt.close()


def get_intervals_from_config():
    csi_code = configs.convert_to_csi_code(config_info['market'])
    index_file = f'{config_info["qlib_url"]}/instruments/{csi_code}.txt'
    intervals = []
    with open(index_file, 'r') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split()
            if len(parts) < 3:
                continue
            start_str, end_str = parts[1], parts[2]
            start_date = datetime.datetime.strptime(start_str, '%Y-%m-%d').date()
            end_date = datetime.datetime.strptime(end_str, '%Y-%m-%d').date()
            intervals.append((start_date, end_date))
    intervals = list(set(intervals))
    intervals.sort(key=lambda x: x[0])
    return intervals


def prepare_rank_training_data(trade_data_path, stock_data, start_date, end_date):
    if not os.path.exists(trade_data_path):
        return None
    trade_data = pd.read_csv(trade_data_path, parse_dates=['date'], index_col=0)
    trade_data['sec_id'] = trade_data['sec_id'].apply(lambda x: str(x).zfill(6))
    trade_data.reset_index(drop=True, inplace=True)
    trade_data.set_index(['date', 'sec_id'], inplace=True)
    trade_data = trade_data[
        (trade_data.index.get_level_values(0) >= start_date) & (trade_data.index.get_level_values(0) <= end_date)]
    ret = pd.merge(stock_data, trade_data, left_index=True, right_index=True, how='inner')
    # ret['return_10D'] = ret.groupby(level=0)['return'].rank(ascending=True, pct=True)
    return ret


def _get_ranked_features_from_ic_files():
    ic_table = pd.read_csv(f'a458_{config_info["market"]}_ic.csv', index_col=0)
    if os.path.exists(f'basic_{config_info["market"]}_ic.csv'):
        ic_table = pd.concat([ic_table, pd.read_csv(f'basic_{config_info["market"]}_ic.csv', index_col=0)])
    rank_1d = abs(ic_table.loc[:, f'{config_info["period_n"]}D']).rank(ascending=False)
    return rank_1d.index.to_list()


def get_decorrelated_top_features(train_data: pd.DataFrame, feature_names: list, target_col: str = 'return_10D',
                                  top_k: int = 20, prefix=''):
    decorrelated_features_file = f'{prefix}decorrelated_features.txt'
    if os.path.exists(decorrelated_features_file):
        with open(decorrelated_features_file, 'r') as f:
            features = [line.strip() for line in f if line.strip()]
        return features[:top_k]

    valid_feature_names = [f for f in feature_names if f in train_data.columns]
    X = train_data[valid_feature_names].copy()
    y = train_data[target_col]

    selected_features = []

    tqdm_bar = tqdm.tqdm(range(min(top_k, len(valid_feature_names))), desc="Selecting Features")

    for _ in tqdm_bar:
        corrs = X.corrwith(y).abs()
        if corrs.dropna().empty:
            break

        best_feature = corrs.idxmax()
        selected_features.append(best_feature)
        tqdm_bar.set_description(f"Selected: {best_feature}")
        f = X[best_feature]

        for col in X.columns:
            if col == best_feature:
                continue

            tqdm_bar.set_description(f"Selected: {best_feature}， processing: {col}")

            f_col = f
            t_col = X[col]
            valid_idx = f_col.notna() & t_col.notna()
            valid_idx &= (~f_col.isin([np.inf, -np.inf])) & (~t_col.isin([np.inf, -np.inf]))
            valid_idx &= f_col.abs() < 1e10
            valid_idx &= t_col.abs() < 1e10

            if valid_idx.sum() < 2:
                continue

            reg = LinearRegression()
            f_valid = f_col[valid_idx].values.reshape(-1, 1)
            col_valid = t_col[valid_idx].values
            reg.fit(f_valid, col_valid)

            pred = np.full_like(f_col, fill_value=np.nan, dtype=np.float64)
            pred[valid_idx] = reg.predict(f_valid)

            X[col] = X[col] - pred

        X.drop(columns=[best_feature], inplace=True)

        if X.empty:
            break

    with open(decorrelated_features_file, 'w') as f:
        for feature in selected_features:
            f.write(f"{feature}\n")
    return selected_features


def train_rank_model(trade_data_path, stock_data, start_date, end_date, save_path):
    train_data = prepare_rank_training_data(trade_data_path, stock_data, start_date, end_date)
    if train_data is None or len(train_data) < 1000:
        return None
    rfid = config_info['remove_from_fid']
    config_info['remove_from_fid'] = 300
    td = config_info['tuning_days']
    config_info['tuning_days'] = 0
    presets = config_info['presets']
    config_info['presets'] = 'medium_quality'
    tt = config_info['train_type']
    config_info['train_type'] = 'rank'
    dtf = config_info['decorrelated_top_features']
    config_info['decorrelated_top_features'] = False
    zh = config_info['zeroshot']
    config_info['zeroshot'] = False

    feature_names = _get_ranked_features_from_ic_files()
    feature_names = get_decorrelated_top_features(train_data, feature_names, top_k=20, prefix='rank_')

    ret = train_model(train_data, feature_names, config_info['period_n'], start_date, end_date, save_path,
                      selected_hyper={'LR': {}})

    config_info['remove_from_fid'] = rfid
    config_info['tuning_days'] = td
    config_info['presets'] = presets
    config_info['train_type'] = tt
    config_info['decorrelated_top_features'] = dtf
    config_info['zeroshot'] = zh
    return ret


def predict_ranks(model, stocks, date):
    df = load_stock_data(config_info['alpha_type'], config_info['market'])
    feature_names = _get_ranked_features_from_ic_files()
    pred_data = prepare_data_for_model(df, feature_names, config_info['period_n'], date, date)
    pred_data = pred_data.loc[pred_data.index.get_level_values(1).isin(stocks)]
    return predict_proba(model, pred_data)
